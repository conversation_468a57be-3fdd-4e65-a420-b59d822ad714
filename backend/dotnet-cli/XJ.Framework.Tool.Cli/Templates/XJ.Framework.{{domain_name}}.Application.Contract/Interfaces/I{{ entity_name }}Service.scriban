{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.Application.Contract.Interfaces;

/// <summary>
/// {{ entity_name }} 服务接口
/// </summary>
public interface I{{ entity_name }}Service :
    IAppService<{{ key_type }}, {{ entity_name }}Dto, {{ entity_name }}QueryCriteria>,
    IEditableAppService<{{ key_type }}, {{ entity_name }}OperationDto>
    {{~ if has_audit_fields || has_soft_delete ~}}
    {{~ else ~}}
    {{~ end ~}}
{
} 