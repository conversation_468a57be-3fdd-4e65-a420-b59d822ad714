{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.Application.Contract.QueryCriteria;

/// <summary>
/// {{ entity_name }} 查询条件
/// </summary>
/// </summary>
public class {{ entity_name }}QueryCriteria : BaseQueryCriteria
{
    {{~ for criteria in criterias ~}}
    /// <summary>
    /// {{~ for line in criteria.comment | string.split '\n' ~}}
    /// {{ line }}
    /// {{~ end ~}}
    /// </summary>
    [{{ criteria.attribute }}] public {{ criteria.type }}{{ if criteria.nullable }}?{{ end }} {{ criteria.name }} { get; set; }{{ if criteria.nullable }}{{ else }} = null!;{{ end }}

    {{~ end ~}}
} 
