using System.Data;
using Dapper;
using MySql.Data.MySqlClient;

namespace XJ.Framework.Tool.Cli.Database;

/// <summary>
/// MySQL数据库提供者
/// </summary>
public class MySqlDatabaseProvider : IDatabaseProvider
{
    private readonly string _connectionString;
    private IDbConnection? _connection;

    public MySqlDatabaseProvider(string connectionString)
    {
        _connectionString = connectionString;
    }

    public IDbConnection CreateConnection()
    {
        _connection = new MySqlConnection(_connectionString);
        return _connection;
    }

    public async Task<IEnumerable<TableInfo>> GetTablesAsync(string schema)
    {
        const string sql = @"
            SELECT 
                table_name as TableName,
                table_schema as SchemaName
            FROM information_schema.tables
            WHERE table_schema = @Schema
            AND table_type = 'BASE TABLE'";

        var parameters = new { Schema = schema };
        return await _connection!.QueryAsync<TableInfo>(sql, parameters);
    }

    public async Task<IEnumerable<ColumnInfo>> GetColumnsAsync(string tableName, string schema)
    {
        const string sql = @"
            SELECT 
                column_name as ColumnName,
                data_type as DataType,
                is_nullable as IsNullable,
                CASE WHEN extra = 'auto_increment' THEN 1 ELSE 0 END as IsIdentity,
                CASE 
                    WHEN character_maximum_length IS NOT NULL THEN character_maximum_length
                    ELSE NULL
                END as MaxLength,
                column_comment as Description
            FROM information_schema.columns
            WHERE table_name = @TableName
            AND table_schema = @Schema
            ORDER BY ordinal_position";

        var parameters = new { TableName = tableName, Schema = schema };
        return await _connection!.QueryAsync<ColumnInfo>(sql, parameters);
    }

    public void Dispose()
    {
        _connection?.Dispose();
    }
} 
