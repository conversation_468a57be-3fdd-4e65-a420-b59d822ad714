using System.Data;
using Dapper;
using Microsoft.Data.SqlClient;

namespace XJ.Framework.Tool.Cli.Database;

/// <summary>
/// SQL Server数据库提供者
/// </summary>
public class SqlServerDatabaseProvider : IDatabaseProvider
{
    private readonly string _connectionString;
    private IDbConnection? _connection;

    public SqlServerDatabaseProvider(string connectionString)
    {
        _connectionString = connectionString;
    }

    public IDbConnection CreateConnection()
    {
        _connection = new SqlConnection(_connectionString);
        return _connection;
    }

    public async Task<IEnumerable<TableInfo>> GetTablesAsync(string schema)
    {
        const string sql = @"
            SELECT 
                t.name as TableName,
                s.name as SchemaName
            FROM sys.tables t
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE s.name = @Schema";

        var parameters = new { Schema = schema };
        return await _connection!.QueryAsync<TableInfo>(sql, parameters);
    }

    public async Task<IEnumerable<ColumnInfo>> GetColumnsAsync(string tableName, string schema)
    {
        const string sql = @"
            SELECT 
                c.name as ColumnName,
                t.name as DataType,
                CASE WHEN c.is_nullable = 1 THEN 'YES' ELSE 'NO' END as IsNullable,
                CASE WHEN c.is_identity = 1 THEN 1 ELSE 0 END as IsIdentity,
                CASE 
                    WHEN t.name IN ('nvarchar','nchar','varchar','char') THEN c.max_length
                    ELSE NULL
                END as MaxLength,
                ep.value as Description,
                CASE WHEN pk.column_id IS NOT NULL THEN 1 ELSE 0 END as IsPrimary
            FROM sys.columns c
            INNER JOIN sys.types t ON c.user_type_id = t.user_type_id
            INNER JOIN sys.tables tbl ON c.object_id = tbl.object_id
            INNER JOIN sys.schemas s ON tbl.schema_id = s.schema_id
            LEFT JOIN sys.extended_properties ep ON ep.major_id = c.object_id 
                AND ep.minor_id = c.column_id 
                AND ep.name = 'MS_Description'
            LEFT JOIN (
                SELECT ic.object_id, ic.column_id
                FROM sys.indexes i
                INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                WHERE i.is_primary_key = 1
            ) pk ON c.object_id = pk.object_id AND c.column_id = pk.column_id
            WHERE tbl.name = @TableName 
            AND s.name = @Schema
            ORDER BY c.column_id";

        var parameters = new { TableName = tableName, Schema = schema };
        return await _connection!.QueryAsync<ColumnInfo>(sql, parameters);
    }

    public void Dispose()
    {
        _connection?.Dispose();
    }
} 
