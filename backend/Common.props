<Project>


    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' or '$(Configuration)' == 'Quickly' ">
        <NoWarn>1701;1702;1591;1572</NoWarn>
    </PropertyGroup>
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <Version>$(XJFrameworkVersion)</Version>

    </PropertyGroup>

    <PropertyGroup Condition="'$(IsTestProject)' != 'true' and '$(IsEndpoint)' != 'true'">
        <IsPackable>true</IsPackable>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <PackageId>$(MSBuildProjectName)</PackageId>
        <PackageVersion>$(XjFrameworkVersion)</PackageVersion>
        <Authors>XJ Framework Team</Authors>
        <PackageProjectUrl>https://dev.azure.com/XJFramework/Baseline</PackageProjectUrl>
        <RepositoryUrl>https://dev.azure.com/XJFramework/Baseline</RepositoryUrl>
        <PackageLicenseExpression>MIT</PackageLicenseExpression>
        <IncludeSymbols>true</IncludeSymbols>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    </PropertyGroup>


    <!--    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">-->
    <!--        <DocumentationFile>bin\Debug\net8.0\$(MSBuildProjectName).xml</DocumentationFile>-->
    <!--    </PropertyGroup>-->

    <PropertyGroup>
        <DocumentationFile>bin\Debug\net8.0\$(MSBuildProjectName).xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <WarningsAsErrors/>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <WarningsAsErrors/>
    </PropertyGroup>


    <ItemGroup>
        <Compile Remove="Logs\**"/>
        <EmbeddedResource Remove="Logs\**"/>
        <None Remove="Logs\**"/>
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="appsettings.Production.json"/>
        <EmbeddedResource Remove="appsettings.Production.json"/>
        <None Remove="appsettings.Production.json"/>
    </ItemGroup>


    <ItemGroup>
        <None Update="DockerfileNonBuild">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
</Project>
