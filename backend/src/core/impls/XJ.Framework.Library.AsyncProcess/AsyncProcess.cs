using System.Diagnostics;
using System.Text;
using System.Threading.Channels;

namespace XJ.Framework.Library.AsyncProcess;

public static class AsyncProcess
{
    private static IReadOnlyList<int> AcceptableExitCodes { get; set; } = new[] { 0 };

    static bool IsInvalidExitCode(Process process)
    {
        return AcceptableExitCodes.All(x => x != process.ExitCode);
    }

    private static ProcessStartInfo PrepareProcessStartInfo(CommandBody command)
    {
        var processStartInfo = new ProcessStartInfo()
        {
            RedirectStandardOutput = true,
            RedirectStandardInput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true,
            StandardOutputEncoding = Encoding.UTF8,
            StandardErrorEncoding = Encoding.UTF8,
            WorkingDirectory = command.WorkDirectory,
            Arguments = $"{command.Arguments}",
            FileName = command.Command
            // Arguments = $"-c \"{command.Command} {command.Arguments}\"",
            // FileName = "bash"
        };
        // command.EnvironmentVariables?.ForEach(variable =>
        // {
        //     if (processStartInfo.EnvironmentVariables.ContainsKey(variable.Key))
        //     {
        //         processStartInfo.EnvironmentVariables[variable.Key] = variable.Value;
        //     }
        //     else
        //     {
        //         processStartInfo.EnvironmentVariables.Add(variable.Key, variable.Value);
        //     }
        // });

        return processStartInfo;
    }

    public static AsyncEnumerable StartIgnoreErrorOutputAsync(CommandBody command)
    {
        return StartIgnoreErrorOutputAsync(PrepareProcessStartInfo(command));
    }


    static Process SetupRedirectsProcess(ref System.Diagnostics.ProcessStartInfo processStartInfo,
        bool redirectStandardInput)
    {
        // override settings.
        processStartInfo.UseShellExecute = false;
        processStartInfo.CreateNoWindow = true;
        processStartInfo.ErrorDialog = false;
        processStartInfo.RedirectStandardError = true;
        processStartInfo.RedirectStandardOutput = true;
        processStartInfo.RedirectStandardInput = redirectStandardInput;

        var process = new Process()
        {
            StartInfo = processStartInfo,
            EnableRaisingEvents = true,
        };

        return process;
    }

    public static AsyncEnumerable StartAsync(CommandBody command)
    {
        return StartAsync(PrepareProcessStartInfo(command));
    }

    public static AsyncEnumerable StartAsync(System.Diagnostics.ProcessStartInfo processStartInfo)
    {
        var process = SetupRedirectsProcess(ref processStartInfo, false);

        var outputChannel = Channel.CreateUnbounded<string>(new UnboundedChannelOptions
        {
            SingleReader = true,
            SingleWriter = true,
            AllowSynchronousContinuations = true
        });

        var errorList = new List<string>();

        var waitOutputDataCompleted = new TaskCompletionSource<object?>();

        var waitErrorDataCompleted = new TaskCompletionSource<object?>();

        void OnOutputDataReceived(object sender, DataReceivedEventArgs e)
        {
            if (e.Data != null)
            {
                outputChannel.Writer.TryWrite(e.Data);
            }
            else
            {
                waitOutputDataCompleted.TrySetResult(null);
            }
        }

        process.OutputDataReceived += OnOutputDataReceived;

        process.ErrorDataReceived += (_, e) =>
        {
            if (e.Data != null)
            {
                lock (errorList)
                {
                    errorList.Add(e.Data);
                }
            }
            else
            {
                waitErrorDataCompleted.TrySetResult(null);
            }
        };

        process.Exited += async (_, _) =>
        {
            await waitErrorDataCompleted.Task.ConfigureAwait(false);

            if (errorList.Count == 0)
            {
                await waitOutputDataCompleted.Task.ConfigureAwait(false);
            }
            else
            {
                process.OutputDataReceived -= OnOutputDataReceived;
            }

            if (IsInvalidExitCode(process))
            {
                outputChannel.Writer.TryComplete(new ProcessErrorException(process.ExitCode, errorList.ToArray()));
            }
            else
            {
                if (errorList.Count == 0)
                {
                    outputChannel.Writer.TryComplete();
                }
                else
                {
                    outputChannel.Writer.TryComplete(new ProcessErrorException(process.ExitCode, errorList.ToArray()));
                }
            }
        };

        if (!process.Start())
        {
            throw new InvalidOperationException("Can't start process. FileName:" + processStartInfo.FileName +
                                                ", Arguments:" + processStartInfo.Arguments);
        }

        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        return new AsyncEnumerable(outputChannel.Reader, DisposeEvent(process));
    }

    private static Action DisposeEvent(Process process)
    {
        return () =>
        {
            try
            {
                process.EnableRaisingEvents = false;
                if (!process.HasExited)
                {
                    process.Kill();
                }
            }
            finally
            {
                process.Dispose();
            }
        };
    }

    public static AsyncEnumerable StartIgnoreErrorOutputAsync(
        System.Diagnostics.ProcessStartInfo processStartInfo)
    {
        var process = SetupRedirectsProcess(ref processStartInfo, false);

        var outputChannel = Channel.CreateUnbounded<string>(new UnboundedChannelOptions
        {
            SingleReader = true,
            SingleWriter = true,
            AllowSynchronousContinuations = true
        });


        var waitOutputDataCompleted = new TaskCompletionSource<object?>();

        var cached = new List<string>();

        void OnOutputDataReceived(object sender, DataReceivedEventArgs e)
        {
            if (e.Data != null)
            {
                lock (cached)
                {
                    cached.Add(e.Data);
                }

                outputChannel.Writer.TryWrite(e.Data);
            }
            else
            {
                waitOutputDataCompleted.TrySetResult(null);
            }
        }

        process.OutputDataReceived += OnOutputDataReceived;

        process.ErrorDataReceived += OnOutputDataReceived;

        process.Exited += async (_, _) =>
        {
            await waitOutputDataCompleted.Task.ConfigureAwait(false);

            if (IsInvalidExitCode(process))
            {
                outputChannel.Writer.TryComplete(new ProcessErrorException(process.ExitCode, cached.ToArray()));
            }
            else
            {
                outputChannel.Writer.TryComplete();
            }
        };

        if (!process.Start())
        {
            throw new InvalidOperationException("Can't start process. FileName:" + processStartInfo.FileName +
                                                ", Arguments:" + processStartInfo.Arguments);
        }

        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        return new AsyncEnumerable(outputChannel.Reader, DisposeEvent(process));
    }
}