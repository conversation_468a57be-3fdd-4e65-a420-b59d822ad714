using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Common.Abstraction.Diagnostics;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Extensions;

public static class ILoggerExtensions
{
    public static async Task<R> LogTimeTakenAsync<R>(this ILogger logger, Func<Task<R>> func, string actionName,
        bool logException = false)
    {
        logger.NullCheck();
        func.NullCheck();
        actionName.CheckStringIsNullOrEmpty();

        try
        {
            return await TimeTaken.DurationAsync(async () =>
                    await func(),
                elapsed => logger.LogActionInformation(actionName, elapsed)
            );
        }
        catch (Exception ex)
        {
            if (logException)
            {
                logger.LogActionException(actionName, ex);

                throw ex.BuildEscapeLogException(actionName);
            }
            else
                throw;
        }
    }

    public static async Task LogTimeTakenAsync(this ILogger logger, Func<Task> func, string actionName,
        bool logException = false)
    {
        logger.NullCheck();
        func.NullCheck();
        actionName.CheckStringIsNullOrEmpty();

        try
        {
            logger.LogActionInformation(actionName, await TimeTaken.DurationAsync(func));
        }
        catch (Exception ex)
        {
            if (logException)
            {
                logger.LogActionException(actionName, ex);

                throw ex.BuildEscapeLogException(actionName);
            }
            else
                throw;
        }
    }

    public static R LogTimeTaken<R>(this ILogger logger, Func<R> func, string actionName, bool logException = false)
    {
        logger.NullCheck();
        func.NullCheck();
        actionName.CheckStringIsNullOrEmpty();

        try
        {
            return TimeTaken.Duration(() =>
                    func(),
                elapsed => logger.LogActionInformation(actionName, elapsed)
            );
        }
        catch (Exception ex)
        {
            if (logException)
            {
                logger.LogActionException(actionName, ex);

                throw ex.BuildEscapeLogException(actionName);
            }
            else
                throw;
        }
    }

    public static void LogTimeTaken(this ILogger logger, Action action, string actionName, bool logException = false)
    {
        logger.NullCheck();
        action.NullCheck();
        actionName.CheckStringIsNullOrEmpty();

        try
        {
            logger.LogActionInformation(actionName, TimeTaken.Duration(action));
        }
        catch (Exception ex)
        {
            if (logException)
            {
                logger.LogActionException(actionName, ex);

                throw ex.BuildEscapeLogException(actionName);
            }
            else
                throw;
        }
    }

    public static void LogActionInformation(this ILogger logger, string actionName, TimeSpan elapsed)
    {
        logger.LogInformation("执行操作{actionName}，耗时：{elapsed}ms", actionName, elapsed.TotalMilliseconds);
    }

    private static void LogActionException(this ILogger logger, string actionName, Exception ex)
    {
        ex.NullCheck();

        //不重复记录已经转义和记录过的异常
        if (ex is IEscapeLogException == false)
            logger.LogError(ex, BuildErrorMessage(actionName, ex));
    }

    private static System.Exception BuildEscapeLogException(this System.Exception ex, string actionName)
    {
        var realEx = ex.GetRealException()!;

        var result = realEx;

        if ((realEx is IEscapeLogException) == false)
        {
            if (realEx is NonTransientException)
                result = new NonTransientLogException(BuildErrorMessage(actionName, realEx));
            else
                result = new EscapeLogException(BuildErrorMessage(actionName, realEx));
        }

        return result;
    }

    private static string BuildErrorMessage(string actionName, System.Exception ex)
    {
        var result = ex.Message;

        if ((ex is IEscapeLogException) == false)
            result = $"执行操作'{actionName}'异常，{ex.Message}";

        return result;
    }
}