using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Serilog.Configuration;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Logging.Abstraction.Enrichers;

namespace XJ.Framework.Library.Logging.Abstraction;

public static class CorrelationIdLoggerConfigurationExtensions
{
    public static LoggerConfiguration WithCorrelationId(
        this LoggerEnrichmentConfiguration loggerEnrichmentConfiguration, IServiceProvider serviceProvider)
    {
        loggerEnrichmentConfiguration.NullCheck();

        return loggerEnrichmentConfiguration.With(
            new CorrelationIdEnricher(serviceProvider.GetService<IContextContainer>()));
    }
}