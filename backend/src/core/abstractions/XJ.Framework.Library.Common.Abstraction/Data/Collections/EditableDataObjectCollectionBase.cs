using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Data.Collections;

/// <summary>
/// 能够编辑的数据对象集合类的虚基类
/// </summary>
/// <typeparam name="T"></typeparam>
[Serializable]
public abstract class EditableDataObjectCollectionBase<T> : DataObjectCollectionBase<T>, ICollection<T>, IList<T>
{
    private bool _IsReadOnly = false;

    /// <summary>
    /// 构造方法
    /// </summary>
    protected EditableDataObjectCollectionBase()
    {
    }

    /// <summary>
    /// 构造方法
    /// </summary>
    /// <param name="isReadOnly">是否只读集合</param>
    protected EditableDataObjectCollectionBase(bool isReadOnly)
    {
        this._IsReadOnly = isReadOnly;
    }

    /// <summary>
    /// 构造方法。集合增加时的分配冗余
    /// </summary>
    /// <param name="capacity"></param>
    protected EditableDataObjectCollectionBase(int capacity)
        : base(capacity)
    {
    }

    /// <summary>
    /// 是否只读集合
    /// </summary>
    public bool IsReadOnly {
        get { return this._IsReadOnly; }
        set { this._IsReadOnly = value; }
    }

    /// <summary>
    /// 添加一个对象
    /// </summary>
    /// <param name="data"></param>
    public virtual void Add(T data)
    {
        InnerAdd(data);
    }

    /// <summary>
    /// 添加一个对象。与Add不同的是，返回添加的对象
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public T Append(T data)
    {
        this.Add(data);

        return data;
    }

    /// <summary>
    /// 增加不存在的数据（已经存在的将被忽略）
    /// </summary>
    /// <param name="data"></param>
    /// <param name="predicate"></param>
    /// <returns>是否增加了数据</returns>
    public virtual bool AddNotExistsItem(T data, Predicate<T> predicate)
    {
        data.NullCheck(nameof(data));

        var needToAdd = predicate == null || this.Exists(predicate) == false;

        if (needToAdd)
            this.Add(data);

        return needToAdd;
    }

    /// <summary>
    /// 插入一项
    /// </summary>
    /// <param name="index"></param>
    /// <param name="item"></param>
    public virtual void Insert(int index, T item)
    {
        InnerInsert(index, item);
    }

    /// <summary>
    /// 读写对象
    /// </summary>
    /// <param name="index"></param>
    /// <returns></returns>
    public virtual T this[int index] {
        get { return (T)List![index]!; }
        set { List[index] = value; }
    }

    /// <summary>
    /// 按照默认的规则进行排序(Comparer(T).Default)
    /// </summary>
    public EditableDataObjectCollectionBase<T> Sort()
    {
        static int comparison(IComparable left, IComparable right) => left.CompareTo(right);

        this.List.QuickSort((Comparison<IComparable>)comparison);

        return this;
    }

    /// <summary>
    /// 按照指定的规则进行排序
    /// </summary>
    /// <param name="comparison"></param>
    public EditableDataObjectCollectionBase<T> Sort(Comparison<T> comparison)
    {
        comparison.NullCheck();

        this.List.QuickSort(comparison);

        return this;
    }

    /// <summary>
    /// 按照指定的规则进行排序
    /// </summary>
    /// <param name="comparer"></param>
    public EditableDataObjectCollectionBase<T> Sort(IComparer<T> comparer)
    {
        comparer.NullCheck();

        this.List.QuickSort(comparer);

        return this;
    }

    ///// <summary>
    ///// 去除重复的元素
    ///// </summary>
    ///// <param name="comparison"></param>
    //public EditableDataObjectCollectionBase<T> Distinct(DistinctComparison<T> comparison)
    //{
    //    comparison.NullCheck("comparer");

    //    List<T> distinctList = new List<T>();

    //    foreach (T originalData in this)
    //    {
    //        if (distinctList.Exists(targetData => comparison(originalData, targetData)) == false)
    //            distinctList.Add(originalData);
    //    }

    //    this.Clear();
    //    this.CopyFrom(distinctList);

    //    return this;
    //}

    /// <summary>
    /// 
    /// </summary>
    /// <param name="index"></param>
    /// <param name="value"></param>
    protected override void OnInsert(int index, object? value)
    {
        CheckReadOnly();
        base.OnInsert(index, value);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="index"></param>
    /// <param name="oldValue"></param>
    /// <param name="newValue"></param>
    protected override void OnSet(int index, object? oldValue, object? newValue)
    {
        CheckReadOnly();
        base.OnSet(index, oldValue, newValue);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="index"></param>
    /// <param name="value"></param>
    protected override void OnRemove(int index, object? value)
    {
        CheckReadOnly();
        base.OnRemove(index, value);
    }

    /// <summary>
    /// 
    /// </summary>
    protected override void OnClear()
    {
        CheckReadOnly();
        base.OnClear();
    }

    private void CheckReadOnly()
    {
        if (this.IsReadOnly)
            throw new ValidationException("集合是只读的");
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="array"></param>
    /// <param name="arrayIndex"></param>
    public void CopyTo(T?[] array, int arrayIndex)
    {
        var index = 0;

        while (arrayIndex + index < array.Length)
        {
            array[arrayIndex + index] = this[index++];
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    public virtual bool Remove(T item)
    {
        return base.Remove(obj =>
        {
            var result = false;

            if (obj != null)
                result = obj.Equals(item);
            else if (item == null)
                result = true;

            return result;
        });
    }
}