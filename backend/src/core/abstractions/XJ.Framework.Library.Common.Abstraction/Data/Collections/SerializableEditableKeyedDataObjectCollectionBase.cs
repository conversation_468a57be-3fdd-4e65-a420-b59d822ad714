using System.Collections;
using System.Runtime.Serialization;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Data.Collections;

/// <summary>
/// 带序列化的EditableKeyedDataObjectCollectionBase的虚基类
/// </summary>
/// <typeparam name="TKey"></typeparam>
/// <typeparam name="TItem"></typeparam>
[Serializable]
public abstract class
    SerializableEditableKeyedDataObjectCollectionBase<TKey, TItem> : EditableKeyedDataObjectCollectionBase<TKey, TItem>,
    ISerializable
{
    /// <summary>
    /// 
    /// </summary>
    public SerializableEditableKeyedDataObjectCollectionBase()
    {
    }

    /// <summary>
    /// 构造方法。集合增加时的分配冗余
    /// </summary>
    /// <param name="capacity"></param>
    protected SerializableEditableKeyedDataObjectCollectionBase(int capacity)
        : base(capacity)
    {
    }

    /// <summary>
    /// 构造方法，指定集合的Key比较器
    /// </summary>
    /// <param name="comparer"></param>
    protected SerializableEditableKeyedDataObjectCollectionBase(IEqualityComparer comparer)
        : base(comparer)
    {
    }

    /// <summary>
    /// 构造方法。集合增加时的分配冗余
    /// </summary>
    /// <param name="capacity"></param>
    /// <param name="comparer"></param>
    protected SerializableEditableKeyedDataObjectCollectionBase(int capacity, IEqualityComparer comparer)
        : base(capacity)
    {
        this._Comparer = comparer;
    }

    #region Serializable

    /// <summary>
    /// 
    /// </summary>
    /// <param name="info"></param>
    /// <param name="context"></param>
    public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        info.AddValue("List", this.InnerList);

        if (this._Comparer != null)
        {
            info.AddValue("ComparerType", this._Comparer.GetType().AssemblyQualifiedName);
            info.AddValue("Comparer", this._Comparer);
        }
        else
            info.AddValue("ComparerType", string.Empty);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="info"></param>
    /// <param name="context"></param>
    protected SerializableEditableKeyedDataObjectCollectionBase(SerializationInfo info, StreamingContext context)
    {
        var list = (ArrayList?)info.GetValue("List", typeof(ArrayList));

        var comparerTypeDesp = info.GetString("ComparerType");

        if (comparerTypeDesp.IsNotEmpty())
        {
            var comparerType = Type.GetType(comparerTypeDesp!);

            (comparerType != null).FalseThrow($"不能找到类型{comparerTypeDesp}");

            this._Comparer = (IEqualityComparer?)info.GetValue("Comparer", comparerType!);
        }

        if (list != null)
        {
            foreach (TItem obj in list)
                base.Add(obj);
        }
    }

    #endregion Serializable
}