using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace XJ.Framework.Library.Common.Abstraction.Configuration;

public static class SettingConfigurationBuilder
{
    public static string GetSolutionJsonFile(string fileName)
    {
        var filePath = Path.Combine(AppContext.BaseDirectory, "settings", fileName);

        if (!File.Exists(filePath))
        {
            // 直接从解决方案根目录的settings文件夹读取配置 主要为了解决执行ef命令时找不到文件的情况
            var solutionDirectory = Directory.GetCurrentDirectory();
            // 向上查找包含.sln文件的目录
            while (Directory.GetFiles(solutionDirectory, "*.sln").Length == 0 &&
                   Directory.GetParent(solutionDirectory) != null)
            {
                solutionDirectory = Directory.GetParent(solutionDirectory)!.FullName;
            }

            filePath = Path.Combine(solutionDirectory, "settings", fileName);
        }

        return filePath;
    }

    public static ConfigurationBuilder AddSolutionJsonFile(this ConfigurationBuilder configurationBuilder,
        string fileName)
    {
        var filePath = GetSolutionJsonFile(fileName);
        configurationBuilder
            .AddJsonFile(filePath, optional: true,
                reloadOnChange: false);

        return configurationBuilder;
    }

    public static IConfigurationRoot AddSolutionJsonFile(this IServiceCollection service, string fileName)
    {
        var filePath = GetSolutionJsonFile(fileName);

        var configurationBuilder = new ConfigurationBuilder()
            .AddSolutionJsonFile(filePath);

        var builtConfiguration = configurationBuilder.Build();
        return builtConfiguration;
    }
}
