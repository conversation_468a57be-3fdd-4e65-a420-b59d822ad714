using System.Collections.Specialized;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using XJ.Framework.Library.Common.Abstraction.Constants;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Reflection;

public static class TypeCreator
{
    private const string VersionMatchTemplate = @"Version=([0-9.]{1,})(,)";

    private struct TypeInfo
    {
        public string AssemblyName;
        public string TypeName;

        public override string ToString()
        {
            return TypeName + ", " + AssemblyName;
        }
    }

    /// <summary>
    /// 运用后绑定方式动态的创建一个实例。
    /// </summary>
    /// <param name="typeDescription">创建实例的完整类型名称</param>
    /// <param name="constructorParams">创建实例的初始化参数</param>
    /// <returns>实例对象</returns>
    /// <remarks>运用晚绑定方式动态创建一个实例
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\TypeCreatorTest.cs" region = "CreateInstanceTest" lang="cs" title="运用晚绑定创建一个实例" />
    /// </remarks>
    public static object CreateInstance(string typeDescription, params object[] constructorParams)
    {
        var type = GetTypeInfo(typeDescription);

        if (type == null)
            throw new TypeLoadException(string.Format("类型{0}信息获取失败", typeDescription));

        return CreateInstance(type, constructorParams);
    }

    /// <summary>
    /// 根据类型信息，通过反射构造此类型，并且根据构造参数名称找到最匹配的构造方法，然后调用构造方法。会进行类型检查
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="typeDescription"></param>
    /// <param name="constructorParams"></param>
    /// <returns></returns>
    public static T CreateInstance<T>(string typeDescription, params object[] constructorParams)
    {
        var result = CreateInstance(typeDescription, constructorParams);

        (result is T).FalseThrow("无法将类型{0}转换为{1}", typeDescription, typeof(T).AssemblyQualifiedName!);

        return (T)result;
    }

    private static readonly Dictionary<Type, object> TypeToInstance = new()
    {
        { typeof(string), string.Empty },
        { typeof(string[]), Empty.StringArray }
    };

    private static object? GetPredefinedInstance(Type type, ref bool dealed)
    {
        object? result;

        if (TypeToInstance.TryGetValue(type, out result))
            dealed = true;

        return result;
    }

    /// <summary>
    /// 根据类型信息创建对象，该对象即使没有公有的构造方法，也可以创建实例
    /// </summary>
    /// <param name="type">创建类型时的类型信息</param>
    /// <param name="constructorParams">创建实例的初始化参数</param>
    /// <returns>实例对象</returns>
    /// <remarks>运用晚绑定方式动态创建一个实例</remarks>
    public static object CreateInstance(System.Type type, params object?[]? constructorParams)
    {
        (type != null).FalseThrow<ArgumentNullException>(nameof(type));
        (constructorParams != null).FalseThrow<ArgumentNullException>(nameof(constructorParams));

        var bf = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;

        try
        {
            var dealed = false;
            var result = GetPredefinedInstance(type!, ref dealed);

            if (dealed == false)
            {
                if (Nullable.GetUnderlyingType(type!) != null)
                {
                    dealed = true;
                }
                else
                {
                    result = Activator.CreateInstance(type!, bf, null, constructorParams, null);

                    if (result != null)
                        dealed = true;
                }
            }

            dealed.FalseThrow("无法创建类型{0}", type!.AssemblyQualifiedName!);

            return result!;
        }
        catch (TargetInvocationException ex)
        {
            var realEx = ex.GetRealException()!;

            var message = type!.AssemblyQualifiedName + ":" + realEx.Message;

            throw new TargetInvocationException(message, realEx);
        }
    }

    /// <summary>
    /// 根据类型信息，通过反射构造此类型，并且根据构造参数名称找到最匹配的构造方法，然后调用构造方法。会进行类型检查
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="type"></param>
    /// <param name="constructorParams"></param>
    /// <returns></returns>
    public static T CreateInstance<T>(System.Type type, params object[] constructorParams)
    {
        var result = CreateInstance(type, constructorParams);

        (result is T).FalseThrow("无法将类型{0}转换为{1}", type.AssemblyQualifiedName!, typeof(T).AssemblyQualifiedName!);

        return (T)result;
    }

    /// <summary>
    /// 根据类型信息，通过反射构造此类型，并且根据构造参数名称找到最匹配的构造方法，然后调用构造方法
    /// </summary>
    /// <param name="typeDescription"></param>
    /// <param name="constructorParams"></param>
    /// <returns></returns>
    public static object CreateInstance(string typeDescription, NameValueCollection constructorParams)
    {
        var type = GetTypeInfo(typeDescription);

        if (type == null)
            throw new TypeLoadException(string.Format("类型{0}信息获取失败", typeDescription));

        return CreateInstance(type, constructorParams);
    }

    /// <summary>
    /// 根据类型信息，通过反射构造此类型，并且根据构造参数名称找到最匹配的构造方法，然后调用构造方法。会进行类型检查
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="typeDescription"></param>
    /// <param name="constructorParams"></param>
    /// <returns></returns>
    public static T CreateInstance<T>(string typeDescription, NameValueCollection constructorParams)
    {
        var result = CreateInstance(typeDescription, constructorParams);

        (result is T).FalseThrow("无法将类型{0}转换为{1}", typeDescription, typeof(T).AssemblyQualifiedName!);

        return (T)result;
    }

    /// <summary>
    /// 根据类型信息，通过反射构造此类型，并且根据构造参数名称找到最匹配的构造方法，然后调用构造方法
    /// </summary>
    /// <param name="type"></param>
    /// <param name="constructorParams"></param>
    /// <returns></returns>
    public static object CreateInstance(System.Type type, NameValueCollection constructorParams)
    {
        (type != null).FalseThrow<ArgumentNullException>(nameof(type));
        (constructorParams != null).FalseThrow<ArgumentNullException>(nameof(constructorParams));

        var bf = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;

        try
        {
            var ci = (ConstructorInfo?)type!.GetConstructors(bf)
                .GetMatchedMethodInfoByParameterNames(constructorParams!.AllKeys);

            (ci != null).FalseThrow("不能找到类型{0}的构造方法", type!.AssemblyQualifiedName!);

            return ci!.Invoke(constructorParams);
        }
        catch (TargetInvocationException ex)
        {
            var realEx = ex.GetRealException()!;

            var message = type!.AssemblyQualifiedName + ":" + realEx.Message;

            throw new TargetInvocationException(message, realEx);
        }
    }

    /// <summary>
    /// 根据类型描述得到类型对象
    /// </summary>
    /// <param name="typeDescription">完整类型描述，应该是Namespace.ClassName, AssemblyName</param>
    /// <param name="withVersion">是否保留版本信息</param>
    /// <returns>类型对象</returns>
    public static Type GetTypeInfo(string typeDescription, bool withVersion = true)
    {
        typeDescription.CheckStringIsNullOrEmpty();

        if (withVersion == false)
            typeDescription = Regex.Replace(typeDescription, VersionMatchTemplate, string.Empty,
                RegexOptions.Compiled | RegexOptions.IgnoreCase);

        var result = Type.GetType(typeDescription);

        (result != null).FalseThrow<TypeLoadException>("不能得到类型信息{0}", typeDescription);

        return result!;
    }

    /// <summary>
    /// 根据类型描述试图得到类型对象，如果成功，返回true，否则为false
    /// </summary>
    /// <param name="typeDescription"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    public static bool TryGetTypeInfo(string typeDescription, out Type? type)
    {
        type = Type.GetType(typeDescription);

        return type != null;
    }

    private static TypeInfo GenerateTypeInfo(string typeDescription)
    {
        var info = new TypeInfo();

        string[] typeParts = typeDescription.Split(',');

        info.TypeName = typeParts[0].Trim();

        var strB = new StringBuilder(256);

        for (var i = 1; i < typeParts.Length; i++)
        {
            if (strB.Length > 0)
                strB.Append(", ");

            strB.Append(typeParts[i]);
        }

        info.AssemblyName = strB.ToString().Trim();

        return info;
    }
}