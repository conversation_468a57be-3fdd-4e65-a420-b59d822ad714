using System.Reflection;
using System.Xml;

namespace XJ.Framework.Library.Common.Abstraction.Reflection;

public class XmlDocumentationProvider
{
    private readonly XmlDocument _xmlDoc;
    private readonly Dictionary<string, string?> _documentationCache = new Dictionary<string, string?>();

    public XmlDocumentationProvider(string xmlFilePath)
    {
        _xmlDoc = new XmlDocument();
        _xmlDoc.Load(xmlFilePath);
    }

    public string? GetTypeDocumentation(Type type)
    {
        string key = $"T:{type.FullName}";
        return GetDocumentation(key);
    }

    public string? GetMethodDocumentation(MethodInfo methodInfo)
    {
        // 构建方法的XML文档键
        string key = $"M:{methodInfo.DeclaringType!.FullName}.{methodInfo.Name}";

        // 处理方法参数
        var parameters = methodInfo.GetParameters();
        if (parameters.Length > 0)
        {
            key += $"({string.Join(",", parameters.Select(p => GetXmlTypeName(p.ParameterType)))})";
        }

        return GetDocumentation(key);
    }

    public string? GetPropertyDocumentation(PropertyInfo propertyInfo)
    {
        string key = $"P:{propertyInfo.DeclaringType!.FullName}.{propertyInfo.Name}";
        return GetDocumentation(key);
    }

    private string? GetDocumentation(string key)
    {
        if (_documentationCache.TryGetValue(key, out string? documentation))
        {
            return documentation;
        }

        XmlNode? node = _xmlDoc.SelectSingleNode($"//member[@name='{key}']/summary");
        if (node != null)
        {
            string? result = node.InnerXml.Trim();
            // 清理XML注释中的格式
            result = System.Text.RegularExpressions.Regex.Replace(result, @"\s*<(para|see).*?>\s*", " ");
            result = System.Text.RegularExpressions.Regex.Replace(result, @"\s+", " ").Trim();
            _documentationCache[key] = result;
            return result;
        }

        return string.Empty;
    }

    private string GetXmlTypeName(Type type)
    {
        if (type.IsGenericType)
        {
            var genericTypeName = type.GetGenericTypeDefinition().FullName;
            // 去掉`1等
            var tickIndex = genericTypeName!.IndexOf('`');
            if (tickIndex > 0)
                genericTypeName = genericTypeName.Substring(0, tickIndex);
            // 泛型参数递归处理
            var genericArgs = string.Join(",", type.GetGenericArguments().Select(GetXmlTypeName));
            return $"{genericTypeName}{{{genericArgs}}}";
        }
        else if (type.IsArray)
        {
            return $"{GetXmlTypeName(type.GetElementType()!)}[]";
        }
        else
        {
            // 去掉程序集信息
            return type.FullName!;
        }
    }
}