namespace XJ.Framework.Library.Common.Abstraction;

public static class Patterns
{
    public static class DateTimePattern
    {
        public const string DATE_FORMAT_PATTERN = "yyyy-MM-dd";
        public const string DATE_TIME_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
        public const string DATE_TIME_FORMAT_WITH_TIMEZONE = "yyyy-MM-dd HH:mm:ss.fffffff zzz";
    }

    public static class RegexPattern
    {
        /// <summary>
        /// 日期格式正则表达式  对应格式"yyyy-MM-dd HH:mm:ss"
        /// </summary>
        public const string DATE_TIME_PATTERN =
            "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";

        public const string JSON_ARRAY_PATTERN = "\\[(.*)\\]";

        public const string CHUNK_INDEX_PATTERN = @"\[\$\d+\$\]";

        /// <summary>
        /// 英文正则表达式
        /// </summary>
        public const string ENGLISH_PATTERN = @"^[A-Za-z]+$";

        /// <summary>
        /// 英文、数字以及“-”组成正则表达式
        /// </summary>
        public const string ENGLISH_DIGIT_HR_PATTERN = @"^[A-Za-z0-9\-]+$";
    }
}