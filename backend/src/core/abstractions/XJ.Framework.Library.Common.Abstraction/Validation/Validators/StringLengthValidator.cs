namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

internal class StringLengthValidator : Validator
{
    private int lowerBound;

    /// <summary>
    /// 下限
    /// </summary>
    public int LowerBound {
        get { return lowerBound; }
        set { lowerBound = value; }
    }

    private int upperBound;

    /// <summary>
    /// 上限
    /// </summary>
    public int UpperBound {
        get { return upperBound; }
        set { upperBound = value; }
    }

    /// <summary>
    /// 无参数构造函数
    /// </summary>
    public StringLengthValidator()
    {
    }

    public StringLengthValidator(int upperBound)
        : this(0, upperBound)
    {
    }

    public StringLengthValidator(int lowerBound, int upperBound)
        : this(lowerBound, upperBound, string.Empty, string.Empty)
    {
    }

    public StringLengthValidator(int lowerBound, int upperBound, string messageTemplate, string tag)
        : base(messageTemplate, tag)
    {
        this.lowerBound = lowerBound;
        this.upperBound = upperBound;
    }

    public override void DoValidate(
        object? objectToValidate,
        object currentTarget,
        string? key,
        ValidationResults validationResults)
    {
        bool isValid;

        if (objectToValidate != null)
        {
            var checker = new RangeChecker<int>(this.lowerBound, this.upperBound);
            isValid = checker.IsInRange(objectToValidate.ToString()!.Length);
        }
        else
            isValid = (this.lowerBound <= 0);

        if (isValid == false)
            RecordValidationResult(validationResults, this.MessageTemplate, currentTarget, key);
    }
}