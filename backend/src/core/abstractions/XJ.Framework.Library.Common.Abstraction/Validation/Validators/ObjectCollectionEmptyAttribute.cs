namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 对象集合为空的校验
/// </summary>
[AttributeUsage(AttributeTargets.Property
                | AttributeTargets.Field,
    AllowMultiple = true,
    Inherited = false)]
public class ObjectCollectionEmptyAttribute : ValidatorAttribute
{
    private string targetRuleset;

    /// <summary>
    /// 
    /// </summary>
    public ObjectCollectionEmptyAttribute()
        : this(string.Empty)
    {
    }

    /// <summary>
    /// ObjectCollectionValidator的构造函数
    /// </summary>
    /// <param name="targetRuleset">所校验类型所属的校验规则集</param>
    public ObjectCollectionEmptyAttribute(string targetRuleset)
    {
        this.targetRuleset = targetRuleset;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="targetType"></param>
    /// <returns></returns>
    protected override Validator DoCreateValidator(Type targetType)
    {
        return new ObjectCollectionEmptyValidator(
            this.targetRuleset = (string.IsNullOrEmpty(this.targetRuleset))
                ? this.Ruleset
                : this.targetRuleset);
    }
}