using System.Collections;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 对象集合的校验器，会依次调用对象集合中的对象进行校验
/// </summary>
internal class ObjectCollectionValidator : Validator
{
    private readonly Type targetType;
    private readonly string targetRuleset = string.Empty;
    private readonly Validator targetTypeValidator;

    /// <summary>
    /// ObjectCollectionValidator的构造函数
    /// </summary>
    /// <param name="targetType">目标类型</param>
    /// <param name="targetRuleset">所校验类型所属的校验规则集</param>
    public ObjectCollectionValidator(Type targetType, string targetRuleset)
        : base()
    {
        targetType.NullCheck(nameof(targetType));

        this.targetType = targetType;
        this.targetRuleset = targetRuleset;
        this.targetTypeValidator = ValidationFactory.CreateValidator(targetType, targetRuleset);
    }

    public override void DoValidate(
        object? objectToValidate,
        object currentObject,
        string? key,
        ValidationResults validateResults)
    {
        if (objectToValidate != null)
        {
            var enumerableObjects = (IEnumerable)objectToValidate;

            if (enumerableObjects != null)
            {
                foreach (var enumerableObject in enumerableObjects)
                {
                    if (this.targetType.IsAssignableFrom(enumerableObject.GetType()))
                    {
                        this.targetTypeValidator.DoValidate(enumerableObject, enumerableObject,
                            key, validateResults);
                    }
                }
            }
        }
    }
}