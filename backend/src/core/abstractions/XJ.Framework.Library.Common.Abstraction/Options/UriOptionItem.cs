using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Options;

public class UriOptionItem : NamedOptionItem
{
    public string Url {
        get;
        set;
    } = string.Empty;

    public Dictionary<string, string>? Headers {
        get;
        set;
    } = new Dictionary<string, string>();

    public Uri? ToUri()
    {
        return this.Url.ToUri();
    }
}