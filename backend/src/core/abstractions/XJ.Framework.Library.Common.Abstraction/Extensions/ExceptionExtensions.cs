using System.Diagnostics;
using System.Net;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security.Authentication;
using XJ.Framework.Library.Common.Abstraction.Exceptions;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class ExceptionExtensions
{
    private static HashSet<Type> _AggregateExceptions = new HashSet<Type>();

    static ExceptionExtensions()
    {
        _AggregateExceptions.Add(typeof(AggregateException));
        _AggregateExceptions.Add(typeof(TargetInvocationException));
    }

    /// <summary>
    /// 从Exception对象中，获取真正发生错误的错误对象。
    /// </summary>
    /// <param name="ex">Exception对象</param>
    /// <returns>真正发生错误的错误对象</returns>
    public static Exception? GetRealException(this Exception? ex)
    {
        var lastestEx = ex;

        while (ex != null &&
               (_AggregateExceptions.Any(t => ex.GetType() == t || ex.GetType().IsSubclassOf(t))))
        {
            if (ex.InnerException != null)
                lastestEx = ex.InnerException;
            else
                lastestEx = ex;

            ex = ex.InnerException;
        }

        return lastestEx;
    }

    /// <summary>
    /// 获取真实异常的描述信息
    /// </summary>
    /// <param name="ex"></param>
    /// <returns></returns>
    public static string GetRealExceptionMessage(this Exception? ex)
    {
        var result = string.Empty;

        if (ex != null)
        {
            var realEx = ex.GetRealException();

            if (realEx != null)
                result = realEx.Message;
        }

        return result;
    }

    /// <summary>
    /// 类型实例是否为空
    /// </summary>
    /// <typeparam name="TService"></typeparam>
    /// <param name="service"></param>
    /// <returns></returns>
    /// <exception cref="SystemSupportException"></exception>
    public static TService ImplementationCheck<TService>(this TService service)
    {
        if (service == null)
            throw new SystemSupportException($"Implementation {typeof(TService).FullName} is null");

        return service;
    }

    #region TrueThrow or FalseThrow

    /// <summary>
    /// 如果条件表达式boolExpression的结果值为真(true)，则抛出strMessage指定的错误信息
    /// </summary>
    /// <param name="parseExpressionResult">条件表达式</param>
    /// <param name="message">错误信息</param>
    /// <param name="messageParams">错误信息的参数</param>
    /// <typeparam name="T">异常的类型</typeparam>
    /// <returns>返回传入的parseExpressionResult</returns>
    /// <remarks>
    /// 如果条件表达式boolExpression的结果值为真(true)，则抛出message指定的错误信息
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\ExceptionsTest.cs" region = "TrueThrowTest" lang="cs" title="通过判断条件表达式boolExpression的结果值而判断是否抛出指定的异常信息" />
    /// <seealso cref="FalseThrow"/>
    /// </remarks>
    [DebuggerNonUserCode]
    public static bool TrueThrow<T>(this bool parseExpressionResult, string message, params object[] messageParams)
        where T : System.Exception
    {
        if (parseExpressionResult)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));

            var exceptionType = typeof(T);

            var obj = Activator.CreateInstance(exceptionType) ??
                      throw new NotSupportedException($"不能创建类型{exceptionType.FullName}的实例");

            Type[] types = new Type[1];
            types[0] = typeof(string);

            var constructorInfoObj = exceptionType.GetConstructor(
                                         BindingFlags.Instance | BindingFlags.Public, null,
                                         CallingConventions.HasThis, types, null) ??
                                     throw new NotSupportedException(
                                         $"不能得到类型{exceptionType.FullName}的public构造方法");

            Object[] args = new Object[1];

            if (messageParams == null || messageParams.Length == 0)
                args[0] = message;
            else
                args[0] = string.Format(message, messageParams);

            constructorInfoObj.Invoke(obj, args);

            throw (Exception)obj;
        }

        return parseExpressionResult;
    }

    /// <summary>
    /// 如果条件表达式boolExpression的结果值为真(true)，则抛出strMessage指定的错误信息
    /// </summary>
    /// <param name="parseExpressionResult">条件表达式</param>
    /// <param name="message">错误信息</param>
    /// <param name="messageParams">错误信息参数</param>
    /// <returns>返回传入的parseExpressionResult</returns>
    /// <remarks>
    /// 如果条件表达式boolExpression的结果值为真(true)，则抛出strMessage指定的错误信息
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\ExceptionsTest.cs"  lang="cs" title="通过判断条件表达式boolExpression的结果值而判断是否抛出指定的异常信息" />
    /// <seealso cref="FalseThrow"/>
    /// </remarks>
    /// <example>
    /// <code>
    /// ExceptionTools.TrueThrow(name == string.Empty, "对不起，名字不能为空！");
    /// </code>
    /// </example>
    [DebuggerNonUserCode]
    public static bool TrueThrow(this bool parseExpressionResult, string message, params object[] messageParams)
    {
        return TrueThrow<ValidationException>(parseExpressionResult, message, messageParams);
    }

    /// <summary>
    /// 如果条件表达式boolExpression的结果值为假（false），则抛出strMessage指定的错误信息
    /// </summary>
    /// <param name="parseExpressionResult">条件表达式</param>
    /// <param name="message">错误信息</param>
    /// <param name="messageParams">错误信息参数</param>
    /// <returns>返回传入的parseExpressionResult</returns>
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\ExceptionsTest.cs" region = "FalseThrowTest" lang="cs" title="通过判断条件表达式boolExpression的结果值而判断是否抛出指定的异常信息" />
    /// <seealso cref="TrueThrow"/>
    /// <remarks>
    /// 如果条件表达式boolExpression的结果值为假（false），则抛出message指定的错误信息
    /// </remarks>
    /// <example>
    /// <code>
    /// ExceptionTools.FalseThrow(name != string.Empty, "对不起，名字不能为空！");
    /// </code>
    /// </example>
    [DebuggerNonUserCode]
    public static bool FalseThrow(this bool parseExpressionResult, string message, params object[] messageParams)
    {
        TrueThrow(false == parseExpressionResult, message, messageParams);

        return parseExpressionResult;
    }

    /// <summary>
    /// 如果条件表达式boolExpression的结果值为假（false），则抛出message指定的错误信息
    /// </summary>
    /// <typeparam name="T">异常的类型</typeparam>
    /// <param name="parseExpressionResult">条件表达式</param>
    /// <param name="message">错误信息</param>
    /// <param name="messageParams">错误信息参数</param>
    /// <returns>返回传入的parseExpressionResult</returns>
    /// <remarks>
    /// 如果条件表达式boolExpression的结果值为假（false），则抛出strMessage指定的错误信息
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\ExceptionsTest.cs" region="FalseThrowTest" lang="cs" title="通过判断条件表达式boolExpression的结果值而判断是否抛出指定的异常信息" />
    /// <seealso cref="TrueThrow"/>
    /// </remarks>
    /// <example>
    /// <code>
    /// ExceptionTools.FalseThrow(name != string.Empty, typeof(ApplicationException), "对不起，名字不能为空！");
    /// </code>
    /// </example>
    [DebuggerNonUserCode]
    public static bool FalseThrow<T>(this bool parseExpressionResult, string message, params object[] messageParams)
        where T : System.Exception
    {
        TrueThrow<T>(false == parseExpressionResult, message, messageParams);

        return parseExpressionResult;
    }

    #endregion TrueThrow or FalseThrow

    #region CheckStringIsNullOrEmpty

    /// <summary>
    /// 检查字符串参数是否为Null或空串，如果是，则抛出异常
    /// </summary>
    /// <param name="data">字符串参数值</param>
    /// <param name="paramName">字符串名称</param>
    /// <returns>返回传入的data</returns>
    /// <remarks>
    /// 若字符串参数为Null或空串，抛出ArgumentException异常
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\ExceptionsTest.cs" region="CheckStringIsNullOrEmpty" lang="cs" title="检查字符串参数是否为Null或空串，若是，则抛出异常" />
    /// </remarks>
    [DebuggerNonUserCode]
    public static string CheckStringIsNullOrEmpty(this string? data,
        [CallerArgumentExpression("data")] string? paramName = null)
    {
        if (string.IsNullOrEmpty(data))
            throw new ArgumentException($"字符串参数{paramName}不能为Null或空串");

        return data;
    }

    /// <summary>
    /// 检查字符串参数是否为Null或空串，如果是，则抛出异常
    /// </summary>
    /// <typeparam name="T">异常的类型</typeparam>
    /// <param name="data">检查字符串参数是否为Null或空串，如果是，则抛出异常</param>
    /// <param name="message"></param>
    /// <returns>返回传入的data</returns>
    [DebuggerNonUserCode]
    public static string CheckStringIsNullOrEmpty<T>(this string? data, string message) where T : System.Exception
    {
        (string.IsNullOrEmpty(data)).TrueThrow<T>(message);

        return data!;
    }

    #endregion CheckStringIsNullOrEmpty

    #region NullCheck

    /// <summary>
    /// 检查对象是否为空，如果为空，抛出ArgumentNullException
    /// </summary>
    /// <typeparam name="TData"></typeparam>
    /// <param name="data">被检查的对象</param>
    /// <param name="message">参数的名称</param>
    /// <returns>返回传入的data，可以继续进行后续操作</returns>
    //[DebuggerNonUserCode]
    public static TData NullCheck<TData>(this TData? data, [CallerArgumentExpression("data")] string? message = null)
    {
        (data == null).TrueThrow<ArgumentNullException>($"{message}不能为空");

        return data!;
    }

    /// <summary>
    /// 检查对象是否为空，如果为空，抛出ArgumentNullException
    /// </summary>
    /// <param name="data">被检查的对象</param>
    /// <param name="message">参数的名称</param>
    /// <returns>返回传入的data，可以继续进行后续操作</returns>
    [DebuggerNonUserCode]
    public static object NullCheck(this object? data, string message)
    {
        return NullCheck<ArgumentNullException>(data, message)!;
    }

    /// <summary>
    /// 检查对象是否为空，如果为空，抛出异常
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="message"></param>
    /// <param name="messageParams"></param>
    /// <returns>返回传入的data，可以继续进行后续操作</returns>
    [DebuggerNonUserCode]
    public static object? NullCheck<T>(this object? data, string message, params object[] messageParams)
        where T : System.Exception
    {
        (data == null).TrueThrow<T>(message, messageParams);

#pragma warning disable CS8603
        return data;
#pragma warning restore CS8603
    }

    #endregion NullCheck

    #region DoSilentAction

    /// <summary>
    /// 执行一个不抛出异常的操作
    /// </summary>
    /// <param name="action"></param>
    /// <param name="exceptionTypes">需要不抛出的异常类型。如果为空，则全部都不抛出</param>
    public static void DoSilentAction(this Action action, params Type[] exceptionTypes)
    {
        if (action != null)
        {
            try
            {
                action();
            }
            catch (System.Exception ex)
            {
                if (InSilentExceptions(ex, exceptionTypes) == false)
                    throw;
            }
        }
    }

    /// <summary>
    /// 执行一个不抛出异常的操作
    /// </summary>
    /// <param name="action"></param>
    /// <param name="exFunc">判断是否抛出异常的回调，返回true则不抛出</param>
    public static void DoSilentAction(this Action action, Func<Exception, bool> exFunc)
    {
        if (action != null)
        {
            try
            {
                action();
            }
            catch (System.Exception ex)
            {
                if (exFunc != null && exFunc(ex) == false)
                    throw;
            }
        }
    }

    /// <summary>
    /// 执行一个不抛出异常的操作
    /// </summary>
    /// <param name="action"></param>
    /// <param name="exceptionTypes">需要不抛出的异常类型。如果为空，则全部都不抛出</param>
    public static async Task DoSilentActionAsync(this Func<Task> action, params Type[] exceptionTypes)
    {
        if (action != null)
        {
            try
            {
                await action();
            }
            catch (System.Exception ex)
            {
                if (InSilentExceptions(ex, exceptionTypes) == false)
                    throw;
            }
        }
    }

    /// <summary>
    /// 执行一个不抛出异常的操作
    /// </summary>
    /// <param name="action"></param>
    /// <param name="exFunc">判断是否抛出异常的回调，返回true则不抛出</param>
    public static async Task DoSilentActionAsync(this Func<Task> action, Func<Exception, bool> exFunc)
    {
        if (action != null)
        {
            try
            {
                await action();
            }
            catch (System.Exception ex)
            {
                if (exFunc != null && exFunc(ex) == false)
                    throw;
            }
        }
    }

    /// <summary>
    /// 执行一个不抛出异常的函数
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="func"></param>
    /// <param name="defaultValue"></param>
    /// <param name="exceptionTypes">需要不抛出的异常类型。如果为空，则全部都不抛出</param>
    /// <returns></returns>
    public static T DoSilentFunc<T>(this Func<T> func, T defaultValue, params Type[] exceptionTypes)
    {
        var result = defaultValue;

        if (func != null)
            try
            {
                result = func();
            }
            catch (System.Exception ex)
            {
                if (InSilentExceptions(ex, exceptionTypes) == false)
                    throw;
            }

        return result;
    }

    /// <summary>
    /// 执行一个不抛出异常的函数
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="func"></param>
    /// <param name="defaultValue"></param>
    /// <param name="exFunc">判断是否抛出异常的回调，返回true则不抛出</param>
    /// <returns></returns>
    public static T DoSilentFunc<T>(this Func<T> func, T defaultValue, Func<Exception, bool> exFunc)
    {
        var result = defaultValue;

        if (func != null)
            try
            {
                result = func();
            }
            catch (System.Exception ex)
            {
                if (exFunc != null && exFunc(ex) == false)
                    throw;
            }

        return result;
    }

    public static async Task<T> DoSilentFuncAsync<T>(this Func<Task<T>> func, T defaultValue,
        params Type[] exceptionTypes)
    {
        var result = defaultValue;

        if (func != null)
            try
            {
                result = await func();
            }
            catch (System.Exception ex)
            {
                if (InSilentExceptions(ex, exceptionTypes) == false)
                    throw;
            }

        return result;
    }

    public static async Task<T> DoSilentFuncAsync<T>(this Func<Task<T>> func, T defaultValue,
        Func<Exception, bool> exFunc)
    {
        var result = defaultValue;

        if (func != null)
            try
            {
                result = await func();
            }
            catch (System.Exception ex)
            {
                if (exFunc != null && exFunc(ex) == false)
                    throw;
            }

        return result;
    }

    private static bool InSilentExceptions(System.Exception ex, Type[] exceptionTypes)
    {
        var result = false;

        if (exceptionTypes != null && exceptionTypes.Length > 0)
        {
            foreach (var exType in exceptionTypes)
            {
                if (ex.GetType() == exType)
                {
                    result = true;
                    break;
                }
            }
        }
        else
            result = true;

        return result;
    }

    #endregion DoSilentAction

    #region HttpCode

    /// <summary>
    /// 将异常转换为HttpStatusCode
    /// </summary>
    /// <param name="exception"></param>
    /// <returns></returns>
    public static HttpStatusCode ToHttpStatusCode(this Exception exception)
    {
        exception.NullCheck();

        //InternalServerError是默认异常
        var httpStatusCode = exception switch
        {
            SystemSupportException _ => HttpStatusCode.InternalServerError,
            ValidationException _ => HttpStatusCode.BadRequest,
            ContentFilterException _ => HttpStatusCode.BadRequest,
            KeyNotFoundException _ => HttpStatusCode.NotFound,
            NotFoundException _ => HttpStatusCode.NotFound,
            AuthenticationException _ => HttpStatusCode.Unauthorized,
            AuthorizationException _ => HttpStatusCode.Forbidden,
            RateLimitingException _ => HttpStatusCode.TooManyRequests,
            _ => HttpStatusCode.InternalServerError
        };

        return httpStatusCode;
    }

    #endregion HttpCode
}