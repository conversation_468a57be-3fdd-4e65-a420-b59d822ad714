using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection TryConfigure<T>(this IServiceCollection services,
        IConfigurationSection configureSection) where T : class
    {
        services.NullCheck();

        if (!services.Any(service => service.ServiceType == typeof(IConfigureOptions<T>)))
        {
            services.Configure<T>(configureSection);
        }

        return services;
    }
}