using XJ.Framework.Library.Common.Abstraction.Options;
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class TypeOptionItemExtensions
{
    /// <summary>
    /// 建立对象的实例
    /// </summary>
    /// <param name="typeOptionItem"></param>
    /// <param name="ctorParams">创建实例的初始化参数</param>
    /// <returns>运用晚绑定方式动态创建一个实例</returns>
    public static object CreateInstance(this TypeOptionItem typeOptionItem, params object[] ctorParams)
    {
        return TypeCreator.CreateInstance(typeOptionItem.GetTypeInfo(), ctorParams);
    }

    /// <summary>
    ///  建立对象的实例同时进行类型检查
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="typeOptionItem"></param>
    /// <param name="ctorParams"></param>
    /// <returns></returns>
    public static T CreateInstance<T>(this TypeOptionItem typeOptionItem, params object[] ctorParams)
    {
        return TypeCreator.CreateInstance<T>(typeOptionItem.GetTypeInfo(), ctorParams);
    }

    /// <summary>
    /// 得到System.Type信息
    /// </summary>
    /// <returns></returns>
    public static Type GetTypeInfo(this TypeOptionItem typeOptionItem)
    {
        Type? result = null;

        if (typeOptionItem.Type.IsNotEmpty())
            result = TypeCreator.GetTypeInfo(typeOptionItem.Type);

        return result!;
    }

    public static T CheckAndGetInstance<T>(this TypeOptionItemCollection typeOptionItem, string key,
        bool autoThrow = true) where T : class
    {
        var element = typeOptionItem.CheckAndGet(key, autoThrow);

        T? instance = default;

        if (element != null)
            instance = (T)element.CreateInstance();

        return instance!;
    }
}