using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Converters;

public static class DataConverter
{
    /// <summary>
    /// 类型转换，提供字符串与枚举型、TimeSpan与整型之间的转换
    /// </summary>
    /// <typeparam name="TSource">源数据的类型</typeparam>
    /// <typeparam name="TResult">目标数据的类型</typeparam>
    /// <param name="srcValue">源数据的值</param>
    /// <returns>类型转换结果</returns>
    /// <remarks>
    /// 数据转换，主要调用系统Convert类的ChangeType方法，但是对于字符串与枚举，整型与TimeSpan类型之间的转换，进行了特殊处理。
    /// </remarks>
    public static TResult? ChangeType<TSource, TResult>(TSource? srcValue)
    {
        return (TResult?)ChangeType(srcValue, typeof(TResult));
    }

    /// <summary>
    /// 字符串与枚举型、TimeSpan与整型之间转换的方法。
    /// </summary>
    /// <typeparam name="TSource">源数据类型</typeparam>
    /// <param name="srcValue">源数据的值</param>
    /// <param name="targetType">目标数据类型</param>
    /// <returns>类型转换后的结果</returns>
    /// <remarks>字符串与枚举型、TimeSpan与整型之间转换的方法。
    /// </remarks>
    public static object? ChangeType<TSource>(TSource? srcValue, System.Type targetType)
    {
        var srcType = typeof(TSource);

        return ChangeType(srcType, srcValue, targetType);
    }

    private delegate void ChangeTypeAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed);

    private static readonly List<ChangeTypeAction> _generalTypeActions = new()
    {
        SameTypeAction,
        TargetIsAssignable,
        NullToNullableAction,
        StringEmptyToNullableAction,
        TargetIsObjectAction //可能是没用的
    };

    private static readonly HashSet<System.Type> _numberTypes = new()
    {
        typeof(int),
        typeof(long),
        typeof(short),
        typeof(double),
        typeof(decimal),
        typeof(float),
        typeof(uint),
        typeof(ulong),
        typeof(ushort),
        typeof(byte)
    };

    private static readonly List<ChangeTypeAction> _underlyingTypeActions = new()
    {
        TargetIsEnumAction,
        DateTimeToStringAction,
        DateTimeOffsetToStringAction,
        TargetIsTimeSpanAction,
        StringToBoolAction,
        DateTimeOffsetToDataTimeAction,
        DateTimeToDataTimeOffsetAction,
        TargetIsDateTimeAction,
        TargetIsDateTimeOffsetAction,
        TargetIsNullableDateTimeOffsetAction,
        DictionaryToDateTimeAction,
        GuidToStringAction,
        StringToGuidAction,
        TargetIsStringAction
    };

    /// <summary>
    /// 字符串与枚举型、TimeSpan与整型之间转换的方法。
    /// </summary>
    /// <param name="srcType">源数据类型</param>
    /// <param name="srcValue">源数据的值</param>
    /// <param name="targetType">目标数据类型</param>
    /// <returns>类型转换后的结果</returns>
    /// <remarks>字符串与枚举型、TimeSpan与整型之间转换的方法。
    /// </remarks>
    public static object? ChangeType(System.Type srcType, object? srcValue, System.Type targetType)
    {
        targetType.NullCheck(nameof(targetType));

        var dealed = false;
        object? result = null;

        if (srcType == typeof(object))
            if (srcValue != null)
                srcType = srcValue.GetType();

        foreach (var action in _generalTypeActions)
        {
            action(srcType, srcValue, targetType, ref result, ref dealed);

            if (dealed)
                break;
        }

        targetType = targetType.GetUnderlyingType();

        if (dealed == false)
        {
            foreach (var action in _underlyingTypeActions)
            {
                action(srcType, srcValue, targetType, ref result, ref dealed);

                if (dealed)
                    break;
            }
        }

        if (dealed == false)
        {
            result = Convert.ChangeType(srcValue, targetType);
            //if (targetType != typeof(object) && targetType.IsAssignableFrom(srcType))
            //{
            //    result = srcValue;
            //}
            //else if (targetType == typeof(DateTime))
            //{
            //    result = Convert.ToDateTime(srcValue);
            //}
            //else
            //{
            //    result = Convert.ChangeType(srcValue, targetType);
            //}
        }

        return result;
    }

    #region Actions

    private static void SameTypeAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (srcType == targetType)
        {
            result = srcValue;
            dealed = true;
        }
    }

    private static void TargetIsAssignable(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType.IsAssignableFrom(srcType))
        {
            result = srcValue;
            dealed = true;
        }
    }

    private static void NullToNullableAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (srcValue == null && Nullable.GetUnderlyingType(targetType) != null)
        {
            result = srcValue;
            dealed = true;
        }
    }

    private static void StringEmptyToNullableAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (srcType == typeof(string) && Nullable.GetUnderlyingType(targetType) != null &&
            (srcValue!.Equals(string.Empty)))
        {
            result = null;
            dealed = true;
        }
    }

    private static void TargetIsObjectAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(object))
        {
            result = srcValue;
            dealed = true;
        }
    }

    private static void TargetIsEnumAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType.IsEnum)
        {
            if (srcType == typeof(string) || _numberTypes.Contains(srcType) || srcType == typeof(char))
            {
                string enumStringValue;
                if (srcType == typeof(char))
                {
                    enumStringValue = srcValue?.ToString() ?? string.Empty;
                }
                else
                {
                    enumStringValue = GetSrcStringValue(srcValue);
                }

                if (string.IsNullOrEmpty(enumStringValue))
                {
                    result = Enum.Parse(targetType, "0", true);
                }
                else
                {
                    if (Enum.TryParse(targetType, enumStringValue, true, out var enumResult))
                    {
                        result = enumResult;
                    }
                    else
                    {
                        try
                        {
                            var underlyingType = Enum.GetUnderlyingType(targetType);
                            var numericValue = Convert.ChangeType(enumStringValue, underlyingType);
                            result = Enum.ToObject(targetType, numericValue);
                        }
                        catch
                        {
                            throw new InvalidCastException($"无法将值 '{enumStringValue}' 转换为枚举类型 {targetType.Name}");
                        }
                    }
                }
                dealed = true;
            }
        }
    }

    private static void DateTimeToStringAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(string) && srcType == typeof(DateTime))
        {
            result = string.Format("{0:yyyy-MM-ddTHH:mm:ss.fff}", srcValue);

            dealed = true;
        }
    }

    private static void DateTimeOffsetToStringAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(string) && srcType == typeof(DateTimeOffset))
        {
            result = string.Format("{0:yyyy-MM-ddTHH:mm:ss.fff K}", srcValue);

            dealed = true;
        }
    }

    private static void TargetIsTimeSpanAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(TimeSpan))
        {
            double tsValue;
            if (srcValue is string)
            {
                if (double.TryParse((string)srcValue, out tsValue))
                    result = TimeSpan.FromSeconds(tsValue);
                else
                    result = TimeSpan.Parse((string)srcValue);
            }
            else
            {
#pragma warning disable CS8605 // Unboxing a possibly null value.
                tsValue = (double)Convert.ChangeType(srcValue, typeof(double));
#pragma warning restore CS8605 // Unboxing a possibly null value.
                result = TimeSpan.FromSeconds(tsValue);
            }

            dealed = true;
        }
    }

    private static void StringToBoolAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(bool) && srcType == typeof(string))
        {
            result = StringToBool(GetSrcStringValue(srcValue), out dealed);
        }
    }

    private static void DateTimeOffsetToDataTimeAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (srcType == typeof(DateTimeOffset) && targetType == typeof(DateTime))
        {
            var dto = (DateTimeOffset)srcValue!;

            var dt = dto.DateTime;
            DateTime.SpecifyKind(dt, DateTimeKind.Local);

            result = dt;

            dealed = true;
        }
    }

    private static void DateTimeToDataTimeOffsetAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (srcType == typeof(DateTime) && targetType == typeof(DateTimeOffset))
        {
            var dt = (DateTime)srcValue!;

            DateTimeOffset dto = dt;

            result = dto;

            dealed = true;
        }
    }

    private static void TargetIsDateTimeAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(DateTime))
        {
            if (srcValue == null)
            {
                result = DateTime.MinValue;
                dealed = true;
            }

            if (dealed == false)
            {
                if (srcType == typeof(string))
                {
                    if (srcValue!.ToString() == string.Empty)
                    {
                        result = DateTime.MinValue;
                        dealed = true;
                    }
                    else
                    {
                        double oaDate;

                        if (double.TryParse((string)srcValue!, out oaDate))
                        {
                            result = DateTime.FromOADate(oaDate);
                            dealed = true;
                        }
                    }
                }

                if (dealed == false)
                {
                    if (typeof(IDictionary<string, object>).IsAssignableFrom(srcType))
                    {
                        result = ((IDictionary<string, object>)srcValue!).ToDateTime();
                        dealed = true;
                    }
                    else if (srcType == typeof(double))
                    {
                        result = DateTime.FromOADate((double)srcValue!);
                        dealed = true;
                    }
                }
            }
        }
    }

    private static void TargetIsDateTimeOffsetAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(DateTimeOffset))
        {
            if (srcValue == null)
            {
                result = DateTimeOffset.MinValue;
                dealed = true;
            }

            if (dealed == false)
            {
                if (srcType == typeof(string))
                {
                    if (srcValue!.ToString() == string.Empty)
                    {
                        result = DateTimeOffset.MinValue;
                        dealed = true;
                    }
                    else
                    {
                        if (DateTimeOffset.TryParse((string)srcValue, out var dt))
                        {
                            result = dt;
                            dealed = true;
                        }
                    }
                }
                else if (srcType == typeof(long))
                {
                    result = DateTimeOffset.FromUnixTimeMilliseconds((long)srcValue!);
                    dealed = true;
                }
            }
        }
    }

    private static void TargetIsNullableDateTimeOffsetAction(System.Type srcType, object? srcValue,
        System.Type targetType, ref object? result, ref bool dealed)
    {
        if (targetType == typeof(DateTimeOffset?))
        {
            if (srcType == typeof(string))
            {
                if (srcValue == null || srcValue.ToString() == string.Empty)
                {
                    result = null;
                    dealed = true;
                }
                else
                {
                    if (DateTimeOffset.TryParse((string)srcValue, out var dt))
                    {
                        result = dt;
                        dealed = true;
                    }
                }
            }
            else if (srcType == typeof(long))
            {
                result = DateTimeOffset.FromUnixTimeMilliseconds((long)srcValue!);
                dealed = true;
            }
        }
    }

    private static void DictionaryToDateTimeAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if ((typeof(IDictionary<string, object>).IsAssignableFrom(targetType)) && srcType == typeof(DateTime))
        {
            result = ((DateTime)srcValue!).ToDictionary();
            dealed = true;
        }
    }

    private static void GuidToStringAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(string) && srcType == typeof(Guid))
        {
            result = GetSrcStringValue(srcValue);
            dealed = true;
        }
    }

    private static void StringToGuidAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(Guid) && srcType == typeof(string))
        {
            var strValue = GetSrcStringValue(srcValue);

            if (strValue.IsNullOrEmpty())
                result = Guid.Empty;
            else
                result = Guid.Parse(strValue);

            dealed = true;
        }
    }

    private static void TargetIsStringAction(System.Type srcType, object? srcValue, System.Type targetType,
        ref object? result, ref bool dealed)
    {
        if (targetType == typeof(string) && srcValue != null)
        {
            result = srcValue.ToString();

            dealed = true;
        }
    }

    #endregion Actions

    private static string GetSrcStringValue(object? srcValue)
    {
        var result = string.Empty;

        if (srcValue != null)
        {
            result = srcValue.ToString();

            result ??= string.Empty;
        }

        return result;
    }

    private static bool StringToBool(string srcValue, out bool dealed)
    {
        var result = false;
        dealed = false;

        srcValue = srcValue.Trim();

        if (srcValue.Length > 0)
        {
            if (srcValue.Length == 1)
            {
                result = string.Compare(srcValue, "0") != 0 && string.Compare(srcValue, "n", true) != 0 &&
                         string.Compare(srcValue, "否", true) != 0;
                dealed = true;
            }
            else
            {
                if (string.Compare(srcValue, "YES", true) == 0 || string.Compare(srcValue, "TRUE", true) == 0)
                {
                    result = true;
                    dealed = true;
                }
                else
                {
                    if (string.Compare(srcValue, "NO", true) == 0 || string.Compare(srcValue, "FALSE", true) == 0)
                    {
                        result = false;
                        dealed = true;
                    }
                }
            }
        }
        else
        {
            dealed = true; //空串表示False
        }

        return result;
    }

    private static System.Type GetUnderlyingType(this Type targetType)
    {
        var underlyingType = Nullable.GetUnderlyingType(targetType);

        if (underlyingType == null)
            underlyingType = targetType;

        return underlyingType;
    }
}