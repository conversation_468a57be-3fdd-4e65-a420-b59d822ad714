using System.Text;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 标识符对象
/// </summary>
public sealed class ParseIdentifier
{
    private Operation_IDs operationID = Operation_IDs.OI_NONE;
    private string identifier = string.Empty;
    private int position = -1;
    private ParseIdentifier? prevIdentifier = null;
    private ParseIdentifier? nextIdentifier = null;
    private ParseIdentifier? subIdentifier = null;
    private ParseIdentifier? parentIdentifier = null;

    /// <summary>
    /// 标识符的类型
    /// </summary>
    public Operation_IDs OperationID {
        get { return this.operationID; }
        internal set { this.operationID = value; }
    }

    /// <summary>
    /// 标识符的文本
    /// </summary>
    public string Identifier {
        get { return this.identifier; }
        internal set { this.identifier = value; }
    }

    /// <summary>
    /// 标识符的类型
    /// </summary>
    public int Position {
        get { return this.position; }
        internal set { this.position = value; }
    }

    /// <summary>
    /// 前一个标识符
    /// </summary>
    public ParseIdentifier? PrevIdentifier {
        get { return this.prevIdentifier; }
        internal set { this.prevIdentifier = value; }
    }

    /// <summary>
    /// 后一个标识符
    /// </summary>
    public ParseIdentifier? NextIdentifier {
        get { return this.nextIdentifier; }
        internal set { this.nextIdentifier = value; }
    }

    /// <summary>
    /// 子标识符
    /// </summary>
    public ParseIdentifier? SubIdentifier {
        get { return this.subIdentifier; }
        internal set { this.subIdentifier = value; }
    }

    /// <summary>
    /// 父标识符
    /// </summary>
    public ParseIdentifier? ParentIdentifier {
        get { return this.parentIdentifier; }
        internal set { this.parentIdentifier = value; }
    }

    /// <summary>
    /// 输出内容
    /// </summary>
    /// <returns></returns>
    public override string ToString()
    {
        var strB = new StringBuilder();

        using (StringWriter writer = new(strB))
        {
            WriteIdentifierInfoRecursively(writer);
        }

        return strB.ToString();
    }

    private void WriteIdentifierInfoRecursively(StringWriter writer, int indent = 0)
    {
        var identifier = this;

        while (identifier != null)
        {
            identifier.WriteIdentifierInfo(writer);

            indent++;

            if (identifier.SubIdentifier != null)
                identifier.SubIdentifier.WriteIdentifierInfoRecursively(writer, indent);

            indent--;

            identifier = identifier.NextIdentifier;
        }
    }

    private void WriteIdentifierInfo(StringWriter writer, int indent = 0)
    {
        var tab = new string(' ', indent);

        writer.WriteLine("{0}'{1}': OpID={2}, Position={3}", tab, Identifier, OperationID, Position);
    }

    internal ParseIdentifier()
    {
    }

    internal ParseIdentifier(Operation_IDs oID, string strID, int nPos, ParseIdentifier? prev)
    {
        this.operationID = oID;
        this.identifier = strID;
        this.position = nPos;
        this.prevIdentifier = prev;
    }
}