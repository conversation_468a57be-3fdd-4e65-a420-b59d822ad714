namespace XJ.Framework.Library.Common.Abstraction.Expressions;

public class BuiltInFunctionsCalculationOptions : CalculationOptionsBase
{
    public BuiltInFunctionsCalculationOptions(object? builtInFunctionsWrapper = null, object? callerContext = null,
        bool optimize = true)
        : base(callerContext, optimize)
    {
        this.BuiltInFunctionsWrapper = builtInFunctionsWrapper;
    }

    public static BuiltInFunctionsCalculationOptions Default(object? builtInFunctionsWrapper = null)
    {
        return new BuiltInFunctionsCalculationOptions(builtInFunctionsWrapper);
    }

    public object? BuiltInFunctionsWrapper {
        get;
        set;
    }
}