using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 
/// </summary>
[Serializable]
public class ExpressionDictionaryItem
{
    /// <summary>
    /// 
    /// </summary>
    public ExpressionDictionaryItem()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="element"></param>
    public ExpressionDictionaryItem(ExpressionDictionaryItemOptionItem element)
    {
        element.NullCheck(nameof(element));

        this.Name = element.Name;
        this.Description = element.Description;
        this.DataType = element.DataType;
        this.DefaultValue = element.DefaultValue;
    }

    /// <summary>
    /// 
    /// </summary>
    public string Name {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string Description {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public ExpressionDataType DataType {
        get;
        set;
    }

    /// <summary>
    /// 
    /// </summary>
    public string DefaultValue {
        get;
        set;
    } = string.Empty;
}