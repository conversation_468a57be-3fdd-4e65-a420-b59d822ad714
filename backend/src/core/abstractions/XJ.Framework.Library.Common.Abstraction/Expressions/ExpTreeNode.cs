using System.Runtime.Serialization;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 二叉树的节点
/// </summary>
/// 
[Serializable]
public sealed class ExpTreeNode : ISerializable
{
    private ExpTreeNode? left = null;
    private ExpTreeNode? right = null;
    private int position = 0;
    private Operation_IDs operationID = Operation_IDs.OI_NONE;
    private Object? nodeValue = null;
    private List<ExpTreeNode> functionParams = [];
    private string functionName = string.Empty;

    /// <summary>
    /// 重载实现二叉树的反序列化
    /// </summary>
    /// <param name="info">The object to be populated with serialization information.</param>
    /// <param name="context">The destination context of the serialization.</param>
    /// <remarks>
    /// 二叉树的反序列化
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="Serialization" lang="cs" />
    /// </remarks>
    private ExpTreeNode(SerializationInfo info, StreamingContext context)
    {
        this.left = (ExpTreeNode?)info.GetValue("Left", typeof(ExpTreeNode));
        this.right = (ExpTreeNode?)info.GetValue("Right", typeof(ExpTreeNode));
        this.position = info.GetInt32("Position");
        this.operationID = (Operation_IDs)info.GetInt16("OperationID");

        var valueTypeName = info.GetString("ValueType");

        if (valueTypeName != "NullValue")
        {
            this.nodeValue = (Object?)info.GetValue("Value", Type.GetType(valueTypeName!)!);
        }
        else
        {
            this.nodeValue = (Object?)info.GetValue("Value", typeof(Object));
        }

        List<ExpTreeNode>? funcParams = (List<ExpTreeNode>?)info.GetValue("Params", typeof(ExpTreeNode));

        if (funcParams != null)
            this.functionParams = funcParams;

        this.functionName = info.GetString("FunctionName")!;
    }

    /// <summary>
    /// 重载GetObjectData实现二叉树的序列化
    /// </summary>
    /// <param name="info">The object to be populated with serialization information. </param>
    /// <param name="context">The destination context of the serialization. </param>
    /// <remarks>对二叉树实现序列化
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="Serialization" lang="cs" />
    /// </remarks>
    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        info.AddValue("Left", this.left);
        info.AddValue("Right", this.right);
        info.AddValue("Position", this.position);
        info.AddValue("OperationID", this.operationID);
        if (this.nodeValue != null)
        {
            info.AddValue("ValueType", this.nodeValue.GetType().FullName);
        }
        else
        {
            info.AddValue("ValueType", "NullValue");
        }

        info.AddValue("Value", this.nodeValue);
        info.AddValue("Params", this.functionParams);
        info.AddValue("FunctionName", this.functionName);
    }

    internal ExpTreeNode()
    {
    }

    /// <summary>
    /// 二叉树的左子树
    /// </summary>
    public ExpTreeNode? Left {
        get { return this.left; }
        internal set { this.left = value; }
    }


    /// <summary>
    /// 二叉树的右子树
    /// </summary>
    public ExpTreeNode? Right {
        get { return this.right; }
        internal set { this.right = value; }
    }

    /// <summary>
    /// 二叉树所在结点在表达式中的绝对位置
    /// </summary>
    public int Position {
        get { return this.position; }
        internal set { this.position = value; }
    }

    /// <summary>
    /// 运算符
    /// </summary>
    public Operation_IDs OperationID {
        get { return this.operationID; }
        internal set { this.operationID = value; }
    }

    /// <summary>
    /// 二叉树的值
    /// </summary>
    public Object? Value {
        get { return this.nodeValue; }
        internal set { this.nodeValue = value; }
    }


    /// <summary>
    /// 二叉树结点函数的参数
    /// </summary>
    public List<ExpTreeNode> Params {
        get { return this.functionParams; }
        internal set { this.functionParams = value; }
    }

    /// <summary>
    /// 二叉树结点所代表的函数名
    /// </summary>
    public string FunctionName {
        get { return this.functionName; }
        internal set { this.functionName = value; }
    }
}