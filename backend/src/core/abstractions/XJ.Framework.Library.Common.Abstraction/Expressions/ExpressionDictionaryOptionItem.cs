using XJ.Framework.Library.Common.Abstraction.Options;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 表达式字典配置项
/// </summary>
public class ExpressionDictionaryOptionItem : TypeOptionItem
{
    /// <summary>
    /// 字典描述项集合
    /// </summary>
    public ExpressionDictionaryItemOptionItemCollection Items {
        get;
        set;
    } = [];
}

/// <summary>
/// 表达式字典配置项集合
/// </summary>
public sealed class ExpressionDictionaryOptionItemCollection : NamedOptionItemCollection<ExpressionDictionaryOptionItem>
{
}