namespace XJ.Framework.Library.Cache.Abstraction;

/// <summary>
/// 缓存基类
/// </summary>
public abstract class CacheBase : ICache
{
    protected readonly CacheOptions Options;

    protected CacheBase(CacheOptions options)
    {
        Options = options;
    }

    /// <summary>
    /// 生成缓存键
    /// </summary>
    protected virtual string GenerateKey(string key)
    {
        return string.IsNullOrEmpty(Options.KeyPrefix) ? key : $"{Options.KeyPrefix}:{key}";
    }

    public abstract Task<T?> GetAsync<T>(string key);
    public abstract Task SetAsync<T>(string key, T value, TimeSpan expiration);
    public abstract Task RemoveAsync(string key);
    public abstract Task<bool> ExistsAsync(string key);
    public abstract Task RemoveAllAsync(params string[] keys);
    public abstract Task RemoveByPatternAsync(string pattern);
    public abstract Task ClearAsync();
    public abstract Task RefreshAsync(string key, TimeSpan expiration);

    public async virtual Task<T> GetOrAddAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiration)
    {
        var value = await GetAsync<T?>(key);
        if (value != null)
        {
            return value;
        }

        value = await factory();
        await SetAsync(key, value, expiration);
        return value;
    }
}
