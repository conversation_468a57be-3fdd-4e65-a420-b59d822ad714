using System.Globalization;

namespace XJ.Framework.Library.Domain.Shared.Interfaces;

public class EntryContext
{
    public string ApplicationCode { get; set; }

    /// <summary>
    /// 模块的路由前缀（用于签名验证等场景）
    /// </summary>
    public string? RoutePrefix { get; set; }


    public EntryContext(string applicationCode)
    {
        ApplicationCode = applicationCode;
    }

    public EntryContext(string applicationCode, string? routePrefix) : this(applicationCode)
    {
        RoutePrefix = routePrefix;
    }
}
