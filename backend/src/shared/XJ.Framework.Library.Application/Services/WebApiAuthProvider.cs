using System.Security.Authentication;
using XJ.Framework.Library.Application.Contract;
using XJ.Framework.Library.Cache.Abstraction;
using XJ.Framework.Rbac.ApiClient;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Library.Application.Services;

public class WebApiAuthProvider : IAuthProvider
{
    private readonly UserApiClient _userApiClient;

    private readonly ICache _cache;

    private readonly IAuthInfoGetter _authInfoGetter;


    public WebApiAuthProvider(UserApiClient userApiClient, ICache cache, IAuthInfoGetter authInfoGetter)
    {
        _userApiClient = userApiClient;
        _cache = cache;
        _authInfoGetter = authInfoGetter;
    }


    public async Task<bool> ValidateApiPermissionAsync(string permissionCode,
        System.Net.Http.HttpMethod? httpMethod = null, string? path = null, string? appCode = null)
    {
        return await ValidatePermissionAsync(PermissionType.Api, permissionCode, httpMethod, path, appCode);
    }

    public async Task<bool> ValidateButtonPermissionAsync(string permissionCode,
        System.Net.Http.HttpMethod? httpMethod = null, string? path = null, string? appCode = null)
    {
        return await ValidatePermissionAsync(PermissionType.Button, permissionCode, httpMethod, path, appCode);
    }

    private async Task<bool> ValidatePermissionAsync(PermissionType permissionType, string permissionCode,
        System.Net.Http.HttpMethod? httpMethod = null, string? path = null, string? appCode = null)
    {
        // 1. 从缓存中获取用户权限
        var authInfo = await _authInfoGetter.GetAuthInfoAsync();

        var tokenId = authInfo.tokenId;
        var deviceId = authInfo.deviceId;

        if (string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(tokenId))
        {
            throw new AuthenticationException("jti or deviceId is null");
        }

        var key = string.Format(CacheKeys.UserApiPermissionKey, tokenId, deviceId, permissionType, permissionCode.Replace(":", "-"),
            httpMethod, path, appCode).ToLower();

        var result = await _cache.GetOrAddAsync<bool?>(key, async () => await _userApiClient.ValidatePermissionAsync(
            permissionType, permissionCode,
            httpMethod,
            path, appCode), TimeSpan.FromHours(1));

        return result ?? false;
    }


    public async Task<UserProfileDto?> GetUserProfileAsync()
    {
        var authInfo = await _authInfoGetter.GetAuthInfoAsync();
        var jti = authInfo.tokenId;
        var deviceId = authInfo.deviceId;

        string? key = null;
        if (string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(jti))
        {
            throw new AuthenticationException("jti or deviceId is null");
        }

        key = string.Format(CacheKeys.UserProfileKey, jti, deviceId);

        var expiration = DateTimeOffset.UtcNow.Add(TimeSpan.FromHours(1)).ToUnixTimeSeconds();
        if (authInfo.exp.HasValue && authInfo.exp.Value < expiration)
        {
            expiration = authInfo.exp.Value;
        }

        var remain = expiration - DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        if (remain <= 0)
        {
            throw new AuthenticationException("token is expired");
        }

        var userProfile = await _cache.GetOrAddAsync<UserProfileDto?>(key,
            async () => await _userApiClient.GetUserInfoAsync(), TimeSpan.FromSeconds(remain));

        return userProfile;
    }
}
