namespace XJ.Framework.Library.Application.Mappers;

public class BaseProfile : Profile
{
    protected BaseProfile()
    {
    }

    protected IMappingExpression<TEntity, TDto> CreateEntityToDtoMap<TKey, TEntity, TDto>()
        where TEntity : BaseEntity<TKey>
        where TDto : BaseDto<TKey>
    {
        return CreateMap<TEntity, TDto>()
            .ForMember(dest => dest.Key, opt => opt.MapFrom(src => src.Key));
    }

    protected IMappingExpression<TOperationDto, TEntity> CreateOperationDtoToEntityMap<TKey, TEntity, TOperationDto>()
        where TEntity : BaseEntity<TKey>
        where TOperationDto : BaseOperationDto
    {
        return CreateMap<TOperationDto, TEntity>()
            .ForMember(dest => dest.Key, opt => opt.MapFrom(src => Guid.NewGuid()));
    }

    protected IMappingExpression<TOperationDto, TEntity> CreateOperationDtoToAuditEntityMap<TKey, TEntity,
        TOperationDto>()
        where TEntity : BaseAuditEntity<TKey>
        where TOperationDto : BaseOperationDto
    {
        return CreateOperationDtoToEntityMap<TKey, TEntity, TOperationDto>()
                .ForMember(dest => dest.CreatedTime, opt => opt.MapFrom(src => DateTimeOffset.UtcNow))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => "System"))
            ;
    }

    protected IMappingExpression<TOperationDto, TEntity> CreateOperationDtoToSoftDeleteEntityMap<TKey, TEntity,
        TOperationDto>()
        where TEntity : BaseSoftDeleteEntity<TKey>
        where TOperationDto : BaseOperationDto
    {
        return CreateOperationDtoToAuditEntityMap<TKey, TEntity, TOperationDto>()
                .ForMember(dest => dest.Deleted, opt => opt.MapFrom(src => false))
            ;
    }
}
