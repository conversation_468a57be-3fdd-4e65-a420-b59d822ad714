using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Application.Contract.OperationDtos;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Interfaces;

namespace XJ.Framework.Library.Interface.Controllers;

[ApiController]
[Route("[controller]")]
public abstract class BaseEditableAppController<TKey, TDto, TOperationDto, TService, TQueryCriteria> : BaseController
    where TService : IEditableAppService<TKey, TOperationDto>, IAppService<TKey, TDto, TQueryCriteria>
    where TKey : struct
    where TDto : BaseDto<TKey>
    where TOperationDto : BaseOperationDto
    where TQueryCriteria : BaseQueryCriteria
{
    protected readonly TService Service;
    protected readonly ICurrentUserContext CurrentUserContext;

    public BaseEditableAppController(IServiceProvider serviceProvider)
    {
        Service = serviceProvider.GetRequiredService<TService>();
        CurrentUserContext = serviceProvider.GetRequiredService<ICurrentUserContext>();
    }
}
