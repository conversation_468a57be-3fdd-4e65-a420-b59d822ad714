namespace XJ.Framework.Library.Interface;

public static class WebApiConstants
{
    public const string DefaultRouteTemplate = "api/[controller]";
    public const string VersioningRouteTemplate = "api/v{version:apiVersion}/[controller]";
    public const string ApiNameVersioningRouteTemplate = "v{version:apiVersion}/apis/{apiName}/[controller]";
    public const string RootVersioningRouteTemplate = "v{version:apiVersion}/[controller]";
}
