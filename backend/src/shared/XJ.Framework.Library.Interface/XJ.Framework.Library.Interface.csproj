<Project Sdk="Microsoft.NET.Sdk">

    
    <Import Project="..\..\..\Common.props"/>

    <PropertyGroup>
        <Description>XJ Framework Interface Library - 接口层，提供Web API控制器、过滤器、中间件等</Description>
        <PackageTags>XJ;Framework;Interface;WebAPI;Controllers;Filters;Middleware</PackageTags>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions"/>
        <FrameworkReference Include="Microsoft.AspNetCore.App"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets"/>
        <PackageReference Include="Swashbuckle.AspNetCore"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Common\XJ.Framework.Library.Common.csproj"/>
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Image\XJ.Framework.Library.Image.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Domain.Shared\XJ.Framework.Library.Domain.Shared.csproj"/>
        <ProjectReference Include="..\..\domains\rbac\XJ.Framework.Rbac.Domain.Shared\XJ.Framework.Rbac.Domain.Shared.csproj"/>
        <ProjectReference Include="..\..\domains\rbac\XJ.Framework.Rbac.Application.Contract\XJ.Framework.Rbac.Application.Contract.csproj"/>
    </ItemGroup>
</Project>
