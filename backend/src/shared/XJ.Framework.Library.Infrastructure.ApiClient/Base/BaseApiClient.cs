using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System.Collections;
using System.Collections.Specialized;
using System.Net;
using System.Net.Http.Json;
using System.Reflection;
using System.Security.Authentication;
using System.Text.Json.Serialization;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Common.Abstraction.Models;
using XJ.Framework.Library.Common.Abstraction.Options;
using XJ.Framework.Library.Domain.Shared.Attributes;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Library.Modular.Domain;

namespace XJ.Framework.Library.Infrastructure.ApiClient.Base;

public abstract class BaseApiClient
{
    protected readonly HttpClient _httpClient;
    protected readonly ILogger<BaseApiClient> _logger;
    protected readonly IAuthInfoGetter _authInfoGetter;
    protected readonly IContextContainer _contextContainer;
    protected readonly ICurrentUserContext _currentUserContext;
    protected readonly JsonSerializerOptions _jsonSerializerOptions;
    protected readonly IOptions<EndpointOption> _endpointOptions;

    protected readonly string BaseUrl;

    protected BaseApiClient(
        IServiceProvider serviceProvider,
        HttpClient httpClient
    )
    {
        _httpClient = httpClient;

        _logger = serviceProvider.GetRequiredService<ILogger<BaseApiClient>>();
        _contextContainer = serviceProvider.GetRequiredService<IContextContainer>();
        _jsonSerializerOptions =
            serviceProvider.GetRequiredService<IOptions<JsonOptions>>().Value.JsonSerializerOptions;
        _endpointOptions = serviceProvider.GetRequiredService<IOptions<EndpointOption>>();

        var scope = serviceProvider.CreateScope();
        _authInfoGetter = scope.ServiceProvider.GetRequiredService<IAuthInfoGetter>();
        _currentUserContext = scope.ServiceProvider.GetRequiredService<ICurrentUserContext>();

        var belongModuleId = GetBelongModuleId();

        var entryContext = serviceProvider.GetRequiredService<EntryContext>();

        var moduleRegistry = serviceProvider.GetService<ModuleRegistry>();

        var moduleInfo = moduleRegistry?.GetModuleInfo(belongModuleId);

        var defaultEndpointOption = serviceProvider.GetService<IOptionalOptions<DefaultEndpointOption>>();

        var code = moduleInfo?.Module.ApplicationCode ?? belongModuleId;

        if (!_endpointOptions.Value.ContainsKey(code))
        {
            code = code.ToPascalCase();
        }

        var routePrefix = moduleInfo?.Module.RoutePrefix ?? null;

        // BaseUrl = (defaultEndpointOption is { Value: not null } && routePrefix != null
        //     ? (defaultEndpointOption!.Value!.Url.TrimEnd('\\', '/') + "/" +
        //        routePrefix.TrimStart('/', '\\'))
        //     : _endpointOptions.Value[code!]!.Url).TrimEnd('/');

        var namedUrl = _endpointOptions.Value.ContainsKey(code!)
            ? _endpointOptions.Value[code!]!.Url
            : null;

        var defaultUrl = defaultEndpointOption is { Value: not null } && routePrefix != null
            ? (defaultEndpointOption!.Value!.Url.TrimEnd('\\', '/') + "/" +
               routePrefix.TrimStart('/', '\\'))
            : null;

        if (string.IsNullOrEmpty(namedUrl) && string.IsNullOrEmpty(defaultUrl))
        {
            throw new ArgumentException("can not find endpoint option");
        }

        BaseUrl = (namedUrl ?? defaultUrl)!.TrimEnd('/');
    }

    private string GetBelongModuleId()
    {
        var belongModuleId = this.GetType().GetCustomAttribute<NameModuleAttribute>()?.ModuleId ??
                             throw new ArgumentException("can not find NameModuleAttribute");
        return belongModuleId;
    }


    public NameValueCollection BuildCriteriaNameValueCollection<TQueryCriteria>(
        PagedQueryCriteria<TQueryCriteria> criteria)
        where TQueryCriteria : BaseQueryCriteria
    {
        var query = BuildCriteriaNameValueCollection(criteria.Condition);

        query.Add("$pageIndex", criteria.PageParams.PageIndex.ToString());
        query.Add("$pageSize", criteria.PageParams.PageSize.ToString());

        if (criteria.OrderBy.Any())
        {
            foreach (var orderBy in criteria.OrderBy)
            {
                query.Add($"$sortBy", orderBy.DataField);
                query.Add($"$orderBy", orderBy.SortDirection == FieldSortDirection.Ascending ? "asc" : "desc");
            }
        }

        return query;
    }

    public NameValueCollection BuildCriteriaNameValueCollection<TQueryCriteria>(
        TQueryCriteria criteria)
        where TQueryCriteria : BaseQueryCriteria
    {
        var query = new NameValueCollection();
        criteria.GetType().GetProperties().ForEach(property =>
        {
            var propertyName = property.Name; //首字母小写
            if (propertyName.Length > 1)
            {
                propertyName = char.ToLower(propertyName[0]) + propertyName.Substring(1);
            }

            var value = property.GetValue(criteria);
            if (value == null) return;


            //这里要判断 如果是枚举类型 则转换为int
            //如果是List IEnumerable等集合类型则添加多个query name都是[]格式 比如status[] 
            //如果是字典 则形如 query.Add($"dynamicQueries[{dynamicQuery.Key}]", dynamicQuery.Value);
            //如果是集合对象 则形如 query.Add(propertyName, value.ToString()!);
            if (value is Enum e)
            {
                query.Add(propertyName, Convert.ToInt32(e).ToString());
            }
            else if (value is ICollection collection)
            {
                foreach (var item in collection)
                {
                    query.Add($"{propertyName}[]", item.ToString()!);
                }
            }
            else
            {
                query.Add(propertyName, value.ToString()!);
            }
        });
        return query;
    }

    protected async Task<byte[]> GetByteArrayAsync(string url, Dictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, url);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request, cancellationToken);

        return await response.Content.ReadAsByteArrayAsync(cancellationToken);
    }

    protected async Task<T> InternalGetAsync<T>(string url, Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, url);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }


    private async Task<T> HandleInternalResponseAsync<T>(HttpResponseMessage? response)
    {
        response.NullCheck();

        var responseMessage = await response!.Content.ReadAsStringAsync();

        if (string.IsNullOrEmpty(responseMessage) && !response.IsSuccessStatusCode)
        {
            response.EnsureSuccessStatusCode();
        }

        JsonSerializerOptions options = new()
        {
            PropertyNameCaseInsensitive = false,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters =
            {
                new LongToStringConverter(),
                new ServiceResponseConverter<T>()
            }
        };
        ServiceResponse<T> serviceResponse;
        try
        {
            serviceResponse =
                JsonSerializer.Deserialize<ServiceResponse<T>>(responseMessage, options)!;
        }
        catch (Exception e)
        {
            throw new Exception($"{response.RequestMessage!.RequestUri!.PathAndQuery} {e.Message}", e);
        }


        (response.StatusCode == HttpStatusCode.BadRequest).TrueThrow<ValidationException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.Unauthorized)
            .TrueThrow<AuthenticationException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.Forbidden).TrueThrow<AuthorizationException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.NotFound).TrueThrow<NotFoundException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.TooManyRequests).TrueThrow<RateLimitingException>(
            serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.OK).FalseThrow<SystemSupportException>(serviceResponse.Message);
        return serviceResponse.Data!;
    }

    protected async Task<T> InternalPostAsync<T>(string url, object? data, Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, url);

        request.Content = JsonContent.Create(data, options: _jsonSerializerOptions);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }

    protected async Task<T> InternalPostFileAsync<T>(string url, MultipartContent content,
        Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, url);

        request.Content = content;

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }


    protected async Task<T> InternalPutAsync<T>(string url, object data, Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Put, url);

        request.Content = JsonContent.Create(data, options: _jsonSerializerOptions);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }

    /// <summary>
    /// 添加授权头
    /// </summary>
    /// <param name="request">http请求对象</param>
    /// <param name="additionHeaders">附加请求头</param>
    protected async virtual Task PrepareAuthorizationHeaderAsync(HttpRequestMessage request,
        Dictionary<string, string>? additionHeaders)
    {
        var (schema, accessToken, deviceId, deviceInfo, tokenId, exp, sub, uniqueName) =
            await _authInfoGetter.GetAuthInfoAsync();

        if (!schema.IsNullOrEmpty() && !accessToken.IsNullOrEmpty())
        {
            request.Headers.Add("Authorization", $"{schema} {accessToken}");
        }

        if (!deviceId.IsNullOrEmpty())
        {
            request.Headers.Add("x-device-id", deviceId);
        }

        if (!deviceInfo.IsNullOrEmpty())
        {
            request.Headers.Add("x-device-info", deviceInfo);
        }

        if (!_contextContainer.GetCorrelationId().IsNullOrEmpty())
        {
            request.Headers.Add("x-correlation-id", _contextContainer.GetCorrelationId());
        }

        var clientIp = _currentUserContext.GetClientIp();
        if (!clientIp.IsNullOrEmpty())
        {
            request.Headers.Add("x-client-ip", clientIp);
        }

        additionHeaders?.ForEach(header =>
        {
            request.Headers.Add(header.Key, header.Value);
        });

        await Task.CompletedTask;
    }
}
