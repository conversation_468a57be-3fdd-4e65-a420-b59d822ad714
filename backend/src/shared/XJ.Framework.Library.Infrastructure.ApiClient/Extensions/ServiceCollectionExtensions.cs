using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;

namespace XJ.Framework.Library.Infrastructure.ApiClient.Extensions;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加API客户端基础设施
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApiClientInfrastructure<TEntryProgram>(this IServiceCollection services)
        where TEntryProgram : class
    {
        // 注册HTTP客户端工厂
        services.AddHttpClient();

        var environmentName = services.BuildServiceProvider().GetRequiredService<IHostEnvironment>().EnvironmentName;


        var builtConfiguration = new ConfigurationBuilder()
            .AddSolutionJsonFile($"endpoint.{environmentName}.json")
            .AddJsonFile("appsettings.json")
            .AddJsonFile($"appsettings.{environmentName}.json")
            .AddUserSecrets<TEntryProgram>()
            .AddEnvironmentVariables()
            .Build();


        services.Configure<EndpointOption>(builtConfiguration.GetSection("Endpoint"));

        var defaultEndpointSection = builtConfiguration.GetSection("Default:Endpoint");

        if (defaultEndpointSection.Exists())
        {
            services.Configure<DefaultEndpointOption>(defaultEndpointSection);
        }

        return services;
    }
}
