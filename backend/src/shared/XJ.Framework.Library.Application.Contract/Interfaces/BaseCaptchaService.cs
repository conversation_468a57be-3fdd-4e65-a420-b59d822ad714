using Microsoft.Extensions.Logging;

namespace XJ.Framework.Library.Application.Contract.Interfaces;

public abstract class BaseCaptchaService : ICaptchaService
{
    private readonly ILogger<BaseCaptchaService> _logger;

    protected BaseCaptchaService(ILogger<BaseCaptchaService> logger)
    {
        _logger = logger;
    }

    public abstract Task<byte[]> GenerateCaptchaImageAsync(string captchaCode);

    public abstract Task<int> GetExpirationInSecondsAsync();

    public abstract KeyValuePair<string, string> GenerateCaptchaContent();

    public abstract Task StoreCaptchaAsync(string captchaId, string captchaContent);

    public abstract Task RemoveCaptchaAsync(string captchaId);

    public abstract Task<string> GetCaptchaAsync(string captchaId);

    /// <summary>
    /// 生成验证码
    /// </summary>
    /// <returns>验证码信息</returns>
    public async Task<CaptchaDto> GenerateCaptchaAsync()
    {
        try
        {
            // 生成随机验证码
            var captchaCodePair = GenerateCaptchaContent();

            var captchaCode = captchaCodePair.Key;

            var captchaContent = captchaCodePair.Value;

            var captchaImage = await GenerateCaptchaImageAsync(captchaCode);
            
            // 生成验证码ID
            var captchaId = Guid.NewGuid().ToString();

            // 存储验证码结果
            await StoreCaptchaAsync(captchaId, captchaContent);

            return new CaptchaDto
            {
                Id = captchaId,
                Image = captchaImage.ToBase64String(),
                ExpiresIn = await GetExpirationInSecondsAsync()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成验证码时发生错误");
            throw new Exception("生成验证码时发生错误", ex);
        }
    }


    /// <summary>
    /// 验证验证码
    /// </summary>
    /// <param name="captchaId">验证码ID</param>
    /// <param name="captchaCode">验证码</param>
    /// <returns>是否有效</returns>
    public async Task<bool> ValidateCaptchaAsync(string captchaId, string captchaCode)
    {
        if (string.IsNullOrEmpty(captchaId) || string.IsNullOrEmpty(captchaCode))
        {
            return false;
        }

        // 从缓存获取验证码
        var storedCode = await GetCaptchaAsync(captchaId);

        if (string.IsNullOrEmpty(storedCode))
        {
            return false; // 验证码不存在或已过期
        }

        // 验证成功后删除验证码（一次性使用）
        await RemoveCaptchaAsync(captchaId);

        // 不区分大小写比较
        return string.Equals(storedCode, captchaCode, StringComparison.OrdinalIgnoreCase);
    }
}
