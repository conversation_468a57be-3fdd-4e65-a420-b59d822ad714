namespace XJ.Framework.Library.Application.Contract.Interfaces;

public interface IAppService<TK<PERSON>, TDto, TQueryCriteria>
    where TDto : BaseDto<TKey>
    where TK<PERSON> : notnull
    where TQueryCriteria : BaseQueryCriteria
{
    Task<TDto?> GetByIdAsync(TKey id);

    Task<IEnumerable<TDto>> GetListAsync(TQueryCriteria criteria);

    Task<PageDtoData<TKey, TDto>> GetPageAsync(PagedQueryCriteria<TQueryCriteria> criteria);
}