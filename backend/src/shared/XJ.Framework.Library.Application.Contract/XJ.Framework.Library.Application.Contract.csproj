<Project Sdk="Microsoft.NET.Sdk">

    
    <Import Project="..\..\..\Common.props"/>

    <PropertyGroup>
        <Description>XJ Framework Application Contract Library - 应用程序契约层，提供查询条件、分页、排序等基础接口</Description>
        <PackageTags>XJ;Framework;Application;Contract;Interfaces;Query;Paging</PackageTags>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\XJ.Framework.Library.EntityFrameworkCore\XJ.Framework.Library.EntityFrameworkCore.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="Interfaces\IBaseAuditService.cs"/>
        <Compile Remove="Interfaces\IBaseSoftDeleteService.cs"/>
    </ItemGroup>

</Project>
