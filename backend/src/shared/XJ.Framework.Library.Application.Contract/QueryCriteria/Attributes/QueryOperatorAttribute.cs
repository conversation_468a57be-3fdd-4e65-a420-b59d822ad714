namespace XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;

[AttributeUsage(AttributeTargets.Property)]
public class QueryOperatorAttribute : Attribute
{
    public QueryOperator Operator { get; }

    public string? PropertyName { get; set; }


    public QueryOperatorAttribute(QueryOperator @operator)
    {
        Operator = @operator;
    }

    public QueryOperatorAttribute(string propertyName, QueryOperator @operator)
    {
        PropertyName = propertyName;
        Operator = @operator;
    }
}

public enum QueryOperator
{
    Equal,
    NotEqual,
    Contains,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    StartsWith,
    EndsWith,
    Between,
    In
}