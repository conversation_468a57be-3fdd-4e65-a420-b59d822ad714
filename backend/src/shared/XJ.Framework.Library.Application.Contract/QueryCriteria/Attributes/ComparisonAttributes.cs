namespace XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;

public class GreaterThanAttribute : QueryOperatorAttribute
{
    public GreaterThanAttribute(string propertyName) : base(propertyName, QueryOperator.GreaterThan)
    {
    }

    public GreaterThanAttribute() : base(QueryOperator.GreaterThan)
    {
    }
}

public class GreaterThanOrEqualAttribute : QueryOperatorAttribute
{
    public GreaterThanOrEqualAttribute(string propertyName) : base(propertyName, QueryOperator.GreaterThanOrEqual)
    {
    }

    public GreaterThanOrEqualAttribute() : base(QueryOperator.GreaterThanOrEqual)
    {
    }
}

public class LessThanAttribute : QueryOperatorAttribute
{
    public LessThanAttribute(string propertyName) : base(propertyName, QueryOperator.LessThan)
    {
    }

    public LessThanAttribute() : base(QueryOperator.LessThan)
    {
    }
}

public class LessThanOrEqualAttribute : QueryOperatorAttribute
{
    public LessThanOrEqualAttribute(string propertyName) : base(propertyName, QueryOperator.LessThanOrEqual)
    {
    }

    public LessThanOrEqualAttribute() : base(QueryOperator.LessThanOrEqual)
    {
    }
}

public class BetweenAttribute : QueryOperatorAttribute
{
    public BetweenAttribute(string propertyName) : base(propertyName, QueryOperator.Between)
    {
    }

    public BetweenAttribute() : base(QueryOperator.Between)
    {
    }
}

public class InAttribute : QueryOperatorAttribute
{
    public InAttribute(string propertyName) : base(propertyName, QueryOperator.In)
    {
    }

    public InAttribute() : base(QueryOperator.In)
    {
    }
}