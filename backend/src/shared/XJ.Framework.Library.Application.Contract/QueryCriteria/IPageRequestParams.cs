namespace XJ.Framework.Library.Application.Contract.QueryCriteria;

/// <summary>
/// 分页请求参数的接口类
/// </summary>
public interface IPageRequestParams
{
    /// <summary>
    /// 页码，从1开始计数
    /// </summary>
    int PageIndex {
        get;
        set;
    }

    /// <summary>
    /// 每页的行数。默认为10
    /// </summary>
    int PageSize {
        get;
        set;
    }

    // /// <summary>
    // /// 总行数。默认为-1，表示没有总行数
    // /// </summary>
    // int TotalCount
    // {
    //     get; 
    //     set;
    // }
    //
    // /// <summary>
    // /// 最多返回多少行
    // /// </summary>
    // int Top
    // {
    //     get;
    //     set;
    // }

    /// <summary>
    /// 转换为行号
    /// </summary>
    /// <returns></returns>
    int ToRowIndex();
}