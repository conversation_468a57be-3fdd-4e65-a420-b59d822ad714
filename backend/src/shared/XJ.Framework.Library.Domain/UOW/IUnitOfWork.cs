using System.Data;

namespace XJ.Framework.Library.Domain.UOW;

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 开启事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务任务</returns>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 开启事务
    /// </summary>
    /// <param name="timeout">事务超时时间（秒）</param>
    /// <param name="isolationLevel">事务隔离级别</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务任务</returns>
    Task BeginTransactionAsync(int timeout, IsolationLevel isolationLevel, CancellationToken cancellationToken = default);

    /// <summary>
    /// 提交事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>提交任务</returns>
    Task CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 回滚事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>回滚任务</returns>
    Task RollbackAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存任务</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}