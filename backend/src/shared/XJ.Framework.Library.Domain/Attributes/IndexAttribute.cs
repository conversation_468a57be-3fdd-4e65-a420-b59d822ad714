namespace XJ.Framework.Library.Domain.Attributes;

[AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
public class IndexAttribute : Attribute
{
    public string Name { get; }
    public string[] PropertyNames { get; }
    public bool IsUnique { get; set; }
    public string? Filter { get; set; }

    public bool IsClustered { get; set; }
    public string[] IncludeProperties { get; set; }

    public IndexAttribute(string name, params string[] propertyNames)
    {
        Name = name;
        PropertyNames = propertyNames;
        IncludeProperties = [];
    }
}

[AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
// 软删除过滤索引特性
public class SoftDeleteIndexAttribute : IndexAttribute
{
    public SoftDeleteIndexAttribute(string name, params string[] propertyNames)
        : base(name, propertyNames)
    {
        Filter = "[is_deleted] = 0";
    }

    public SoftDeleteIndexAttribute(string name, string[] propertyNames, string[] includeProperties)
        : base(name, propertyNames)
    {
        Filter = "[is_deleted] = 0";
        IncludeProperties = includeProperties;
    }
}