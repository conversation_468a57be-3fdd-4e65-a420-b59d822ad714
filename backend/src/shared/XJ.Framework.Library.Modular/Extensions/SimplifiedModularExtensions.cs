using XJ.Framework.Library.Modular.Middlewares;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using XJ.Framework.Library.Modular.Domain;
using XJ.Framework.Library.Modular.Services;

namespace XJ.Framework.Library.Modular.Extensions;

/// <summary>
/// 简化的模块化扩展方法
/// </summary>
public static class SimplifiedModularExtensions
{
    /// <summary>
    /// 添加模块化支持
    /// </summary>
    public static IServiceCollection AddSimplifiedModular(this IServiceCollection services)
    {
        services.TryAddSingleton<HostServerBuilder>();
        services.TryAddSingleton<ModuleRegistry>();
        services.AddHttpClient(); // 确保有IHttpClientFactory

        return services;
    }

    /// <summary>
    /// 创建模块化应用程序
    /// </summary>
    public static ModularApplicationBuilder CreateModularApplication(this WebApplicationBuilder builder)
    {
        builder.Services.AddSimplifiedModular();
        return new ModularApplicationBuilder(builder);
    }

    /// <summary>
    /// 使用改进的模块路由
    /// </summary>
    public static IApplicationBuilder UseSimplifiedModularRouting(this IApplicationBuilder app)
    {
        return app.UseImprovedModuleRouting();
    }
}

/// <summary>
/// 模块化应用程序构建器
/// </summary>
public class ModularApplicationBuilder
{
    private readonly WebApplicationBuilder _builder;
    private readonly List<IModule> _modules = new();

    public ModularApplicationBuilder(WebApplicationBuilder builder)
    {
        _builder = builder;
    }

    /// <summary>
    /// 添加模块
    /// </summary>
    public ModularApplicationBuilder AddModule<TModule>() where TModule : class, IModule, new()
    {
        var module = new TModule();
        _modules.Add(module);
        return this;
    }

    /// <summary>
    /// 添加模块实例
    /// </summary>
    public ModularApplicationBuilder AddModule(IModule module)
    {
        _modules.Add(module);
        return this;
    }

    /// <summary>
    /// 条件性添加模块
    /// </summary>
    public ModularApplicationBuilder AddModuleIf<TModule>(bool condition) where TModule : class, IModule, new()
    {
        if (condition)
        {
            AddModule<TModule>();
        }

        return this;
    }

    /// <summary>
    /// 构建应用程序
    /// </summary>
    public async Task<WebApplication> BuildAsync()
    {
        var app = _builder.Build();

        // 获取模块注册中心
        var moduleRegistry = app.Services.GetRequiredService<ModuleRegistry>();

        var hostServerBuilder = app.Services.GetRequiredService<HostServerBuilder>();

        // 注册所有模块（异步）
        foreach (var module in _modules)
        {
            await moduleRegistry.RegisterModuleAsync(module,
                await hostServerBuilder.CreateSelfHostedModuleAsync(module));
        }

        // 添加模块路由中间件
        app.UseSimplifiedModularRouting();

        // 注册应用停止时的清理
        var lifetime = app.Services.GetRequiredService<IHostApplicationLifetime>();
        lifetime.ApplicationStopping.Register(() => moduleRegistry.Dispose());

        return app;
    }

    /// <summary>
    /// 构建应用程序（同步版本，用于向后兼容）
    /// </summary>
    public WebApplication Build()
    {
        return BuildAsync().GetAwaiter().GetResult();
    }
}

/// <summary>
/// 泛型模块构建器 - 支持泛型模块
/// </summary>
public static class GenericModuleExtensions
{
    /// <summary>
    /// 添加泛型模块（用于现有的模块类型）
    /// </summary>
    public static ModularApplicationBuilder AddGenericModule(
        this ModularApplicationBuilder builder,
        Type moduleType,
        params Type[] genericArguments)
    {
        if (!moduleType.IsGenericTypeDefinition)
        {
            throw new ArgumentException("Module type must be a generic type definition", nameof(moduleType));
        }

        var concreteType = moduleType.MakeGenericType(genericArguments);
        var module = (IModule)Activator.CreateInstance(concreteType)!;

        return builder.AddModule(module);
    }
}
