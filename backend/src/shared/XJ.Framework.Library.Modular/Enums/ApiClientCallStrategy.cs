namespace XJ.Framework.Library.Modular.Enums;

/// <summary>
/// API客户端调用策略枚举
/// 定义智能API客户端的调用方式
/// </summary>
public enum ApiClientCallStrategy
{
    /// <summary>
    /// 自动选择策略
    /// 根据目标模块是否在当前站点中注册来自动决定使用内存调用还是HTTP调用
    /// </summary>
    Auto = 0,

    /// <summary>
    /// 强制使用内存调用
    /// 直接在内存中调用目标模块的Interface层控制器，零网络开销
    /// 注意：只有当目标模块在当前站点中注册时才能使用此策略
    /// </summary>
    InMemory = 1,

    /// <summary>
    /// 强制使用HTTP调用
    /// 通过HTTP请求调用远程API端点，适用于跨站点调用
    /// </summary>
    Http = 2
}