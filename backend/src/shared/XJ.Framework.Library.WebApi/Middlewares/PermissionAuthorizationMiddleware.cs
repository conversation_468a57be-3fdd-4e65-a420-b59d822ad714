using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using XJ.Framework.Library.Interface.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog.Context;
using System.Security.Authentication;
using System.Text;
using System.Text.Json;
using XJ.Framework.Library.Application.Contract.Security;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Rbac.Application.Contract;

namespace XJ.Framework.Library.WebApi.Middlewares;

/// <summary>
/// 权限验证中间件
/// </summary>
public class PermissionAuthorizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IServiceProvider _serviceProvider;
    private readonly EntryContext _applicationOption;
    private readonly JsonSerializerOptions _jsonSerializerOptions;
    private readonly ILogger<PermissionAuthorizationMiddleware>? _logger;

    public PermissionAuthorizationMiddleware(RequestDelegate next,
        IOptions<JsonOptions> jsonOptions, EntryContext applicationOption,
        IServiceProvider serviceProvider,
        ILogger<PermissionAuthorizationMiddleware>? logger = null)
    {
        _next = next;
        _applicationOption = applicationOption;
        _serviceProvider = serviceProvider;
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
        _logger = logger;
    }

    private static string ToInitialUpperCase(string s)
    {
        if (string.IsNullOrEmpty(s))
            return s;
        return char.ToUpper(s[0]) + s.Substring(1);
    }

    public async Task InvokeAsync(HttpContext context)
    {
        using var scope = _serviceProvider.CreateScope();

        var endpoint = context.GetEndpoint();
        if (endpoint == null)
        {
            await _next(context);
            return;
        }

        var applicationPermissionAttribute = endpoint.Metadata.GetMetadata<ApplicationPermissionAttribute>();
        if (applicationPermissionAttribute != null)
        {
            var applicationCode = context.Request.Headers["x-application-code"].FirstOrDefault();

            if (applicationCode.IsNullOrEmpty())
            {
                throw new AuthenticationException("应用编码不能为空/Application code cannot be empty");
            }

            var signature = context.Request.Headers["x-signature"].FirstOrDefault();

            if (signature.IsNullOrEmpty())
            {
                throw new AuthenticationException("签名不能为空/Signature cannot be empty");
            }

            var secretKeyOption = scope.ServiceProvider.GetRequiredService<IOptions<SecretKeyOption>>();

            if (!secretKeyOption.Value.ContainsKey(applicationCode!))
            {
                throw new AuthenticationException("应用不存在/Application does not exist");
            }

            var secretKey = secretKeyOption.Value[applicationCode!]!.SecretKey;

            // 获取用于签名验证的路径
            var path = context.Request.Path;

            var requestBodyObject = new Dictionary<string, object>();

            //以下是application/json时 还有一种情况是multipart/form-data 的情况 此时应该遍历所有的非文件类型的form-data参数然后添加进 requestBodyObject中

            if (context.Request.ContentType?.StartsWith("multipart/form-data") ?? false)
            {
                var form = await context.Request.ReadFormAsync();
                foreach (var key in form.Keys)
                {
                    // 跳过文件类型的参数
                    if (form.Files.Any(q => q.Name == key))
                    {
                        continue;
                    }

                    requestBodyObject.Add(key, form[key]);
                }
            }
            else if (context.Request.ContentType?.StartsWith("application/json") ?? false)
            {
                context.Request.Body.Seek(0, SeekOrigin.Begin); // 确保从头开始读
                using var reader = new StreamReader(context.Request.Body, encoding: Encoding.UTF8,
                    detectEncodingFromByteOrderMarks: false, leaveOpen: true);
                var requestBody = await reader.ReadToEndAsync();
                context.Request.Body.Seek(0, SeekOrigin.Begin); // 读完后重置，保证后续中间件/控制器还能读取

                requestBodyObject =
                    JsonSerializer.Deserialize<Dictionary<string, object>>(requestBody, _jsonSerializerOptions);

                requestBodyObject = requestBodyObject?.ToDictionary(q => ToInitialUpperCase(q.Key), q => q.Value);
            }

            var isValid = ApplicationSignatureHelper.ValidateSignature(applicationCode!, secretKey, path,
                requestBodyObject!,
                signature!);
            if (!isValid)
            {
                throw new AuthenticationException("签名验证失败/Signature validation failed");
            }

            await _next(context);
        }
        else
        {
            // 1. 检查是否允许匿名访问
            var allowAnonymous = endpoint.Metadata.GetMetadata<AllowAnonymousAttribute>() != null;
            if (allowAnonymous)
            {
                await _next(context);
                return;
            }

            var authProvider = scope.ServiceProvider.GetRequiredService<IAuthProvider>();
            // 2. 用户身份校验
            UserProfileDto? user = null;
            try
            {
                user = await authProvider.GetUserProfileAsync();
            }
            catch (Exception e)
            {
                throw new AuthenticationException(e.Message);
            }

            if (user == null)
            {
                throw new AuthenticationException("用户未登录或登录已过期/User is not logged in or login has expired");
            }

            if (user.Status != UserStatus.Enabled)
            {
                throw new AuthenticationException("用户账号已被禁用或锁定/User account has been disabled or locked");
            }

            context.Items["CurrentUser"] = user;

            // 3. 权限校验
            var controllerActionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
            if (controllerActionDescriptor != null)
            {
                var requestMethod = context.Request.Method;
                if (!Constants.StringToHttpMethodMap.TryGetValue(requestMethod, out var httpMethod))
                {
                    throw new AuthorizationException("不支持的HTTP方法/ Unsupported HTTP method: " + requestMethod);
                }

                var path = controllerActionDescriptor.AttributeRouteInfo?.Template;

                // 移除路由前缀，确保权限验证使用的路径与数据库中的记录一致
                if (!string.IsNullOrEmpty(path) && !string.IsNullOrEmpty(_applicationOption.RoutePrefix))
                {
                    var routePrefix = _applicationOption.RoutePrefix.Trim('/');
                    if (path.StartsWith($"{routePrefix}/", StringComparison.OrdinalIgnoreCase))
                    {
                        path = path.Substring(routePrefix.Length + 1);
                        _logger?.LogDebug(
                            "Removed route prefix for permission validation: {OriginalPath} -> {CleanPath}",
                            controllerActionDescriptor.AttributeRouteInfo?.Template, path);
                    }
                }

                var publicPermission = endpoint.Metadata.GetMetadata<PublicPermissionAttribute>();
                if (publicPermission == null)
                {
                    var permissionCode = GetPermissionCode(controllerActionDescriptor, context);
                    var hasPermission = await authProvider.ValidateApiPermissionAsync(
                        permissionCode,
                        httpMethod,
                        path,
                        _applicationOption.ApplicationCode
                    );
                    if (!hasPermission)
                    {
                        throw new AuthorizationException(
                            $"没有访问权限/No access permission,Path:{path},Method:{requestMethod},PermissionCode:{permissionCode}/");
                    }
                }
                else
                {
                    if (publicPermission.FilterInternal &&
                        user.UserType != UserType.Internal &&
                        user.UserType != UserType.System)
                    {
                        throw new AuthorizationException("没有访问权限/No access permission");
                    }
                }
            }

            // 4. 通过校验，进入下一个中间件
            using (LogContext.PushProperty("CurrentUser", $"{user.Username}"))
                await _next(context);
        }
    }

    private string GetPermissionCode(ControllerActionDescriptor actionDescriptor, HttpContext context)
    {
        var requirePermission = actionDescriptor.EndpointMetadata
            .OfType<RequirePermissionAttribute>()
            .FirstOrDefault();
        if (requirePermission != null)
        {
            return requirePermission.PermissionCode;
        }

        var module = actionDescriptor.ControllerName.Replace("Controller", "").ToLower();
        var action = actionDescriptor.ActionName.ToLower();

        var requestMethod = context.Request.Method;

        return $"{_applicationOption.ApplicationCode}:api:{module}:{action}:{requestMethod}";
    }
}
