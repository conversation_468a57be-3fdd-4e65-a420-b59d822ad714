namespace XJ.Framework.Library.WebApi.Binders
{
    /// <summary>
    /// 添加了ModelBinder的Provider
    /// </summary>
    public class BasicModelBinderProvider : IModelBinderProvider
    {
        public IModelBinder? GetBinder(ModelBinderProviderContext context)
        {
            IModelBinder? binder = null;

            if (typeof(IPagedQueryCriteria).IsAssignableFrom(context.Metadata.ModelType))
                binder = new PagedQueryCriteriaBinder();

            return binder;
        }
    }
}