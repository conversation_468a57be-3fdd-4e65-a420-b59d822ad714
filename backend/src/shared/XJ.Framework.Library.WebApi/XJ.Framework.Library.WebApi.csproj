<Project Sdk="Microsoft.NET.Sdk">

    
    <Import Project="..\..\..\Common.props"/>

    <PropertyGroup>
        <Description>XJ Framework WebApi Library - Web API层，提供路由约定、模型绑定、过滤器等Web API功能</Description>
        <PackageTags>XJ;Framework;WebApi;Routing;ModelBinding;Filters;Swagger</PackageTags>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions"/>
    </ItemGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets"/>
        <PackageReference Include="Swashbuckle.AspNetCore"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Common\XJ.Framework.Library.Common.csproj" />
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Logging.Database\XJ.Framework.Library.Logging.Database.csproj" />
        <ProjectReference Include="..\XJ.Framework.Library.Interface\XJ.Framework.Library.Interface.csproj" />
    </ItemGroup>

</Project>
