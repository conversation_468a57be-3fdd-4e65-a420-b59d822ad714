using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Library.WebApi.Filters;

/// <summary>
/// 工作单元过滤器
/// </summary>
public class UnitOfWorkFilter : IAsyncActionFilter
{
    private readonly ILogger<UnitOfWorkFilter> _logger;

    public UnitOfWorkFilter(ILogger<UnitOfWorkFilter> logger)
    {
        _logger = logger;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        // 获取控制器操作信息
        var actionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;
        if (actionDescriptor == null)
        {
            await next();
            return;
        }

        // 获取UnitOfWork特性
        var unitOfWorkAttr = actionDescriptor.MethodInfo.GetCustomAttributes(typeof(UnitOfWorkAttribute), true)
            .FirstOrDefault() as UnitOfWorkAttribute;

        if (unitOfWorkAttr == null || !unitOfWorkAttr.IsTransactional)
        {
            await next();
            return;
        }

        // 获取工作单元实例
        var unitOfWork = context.HttpContext.RequestServices.GetRequiredService<IUnitOfWork>();
        var controllerName = actionDescriptor.ControllerName;
        var actionName = actionDescriptor.ActionName;
        
        try
        {
            _logger.LogInformation(
                "开始事务 - Controller: {Controller}, Action: {Action}, Timeout: {Timeout}s, IsolationLevel: {IsolationLevel}",
                controllerName, actionName, unitOfWorkAttr.Timeout, unitOfWorkAttr.IsolationLevel);

            // 使用配置的选项开始事务
            await unitOfWork.BeginTransactionAsync(
                unitOfWorkAttr.Timeout,
                unitOfWorkAttr.IsolationLevel,
                context.HttpContext.RequestAborted);

            // 执行后续过滤器和操作方法
            var resultContext = await next();

            // 如果操作执行成功且没有异常，提交事务
            if (resultContext.Exception == null || resultContext.ExceptionHandled)
            {
                await unitOfWork.CommitAsync(context.HttpContext.RequestAborted);
                _logger.LogInformation(
                    "提交事务 - Controller: {Controller}, Action: {Action}",
                    controllerName, actionName);
            }
            else
            {
                // 如果有未处理的异常，回滚事务
                await unitOfWork.RollbackAsync(context.HttpContext.RequestAborted);
                _logger.LogError(
                    "回滚事务 - Controller: {Controller}, Action: {Action}, 异常: {Exception}",
                    controllerName, actionName, resultContext.Exception.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "事务执行失败 - Controller: {Controller}, Action: {Action}",
                controllerName, actionName);

            // 回滚事务
            await unitOfWork.RollbackAsync(context.HttpContext.RequestAborted);
            throw;
        }
    }
}
