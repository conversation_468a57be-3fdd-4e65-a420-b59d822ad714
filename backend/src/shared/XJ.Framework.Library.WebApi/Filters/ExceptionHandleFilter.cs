using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Security.Authentication;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Common.Abstraction.Models;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Library.WebApi.Filters;

public class ExceptionHandleFilter : IAsyncExceptionFilter
{
    private readonly ILogger<ExceptionHandleFilter> _logger;
    private readonly IContextContainer _contextContainer;
    private readonly IOptions<ApplicationSetting> _applicationOption;

    public ExceptionHandleFilter(ILogger<ExceptionHandleFilter> logger, IContextContainer contextContainer,
        IOptions<ApplicationSetting> applicationOption)
    {
        _logger = logger;
        _contextContainer = contextContainer;
        _applicationOption = applicationOption;
    }

    public async Task OnExceptionAsync(ExceptionContext context)
    {
        var realException = context.Exception.GetRealException()!;
        _logger.LoggingException("application-exception", realException);


        var responseData = GetErrorResponse(realException);

        if ("text/event-stream".Equals(context.HttpContext.Response.ContentType, StringComparison.OrdinalIgnoreCase))
        {
            var errorMessage = $"[ERROR]{responseData.ToJson()}";
            _logger.LoggingException("application-exception", errorMessage);
            await ResponseRawMessageAsync(context, errorMessage);
        }
        else
        {
            await ResponseJsonMessageAsync(context, responseData);
        }
    }

    private async Task ResponseRawMessageAsync(ExceptionContext context, string message)
    {
        context.Result = new ContentResult()
        {
            Content = message,
            ContentType = "text/plain",
            StatusCode = GetHttpStatusCode(context.Exception).GetHashCode()
        };
        context.ExceptionHandled = true;
        await Task.CompletedTask;
    }

    private async Task ResponseJsonMessageAsync(ExceptionContext context, ServiceResponse<object> responseData)
    {
        context.Result = new JsonResult(responseData)
        {
            StatusCode = responseData.Code,
            ContentType = "application/json"
        };

        context.ExceptionHandled = true;
        await Task.CompletedTask;
    }


    private ServiceResponse<object> GetErrorResponse(Exception exception)
    {
        var httpStatusCode = GetHttpStatusCode(exception);

        return new ServiceResponse<object>(
            correlationId: _contextContainer.GetCorrelationId(),
            code: (int)httpStatusCode,
            message: exception.Message,
            //只有500错误时才进行错误堆栈打印
            detail: httpStatusCode == HttpStatusCode.InternalServerError
                ? GetExceptionStackTrace(exception)
                : string.Empty
        );
    }

    private string GetExceptionStackTrace(Exception exception)
    {
        var isFullStackTrack = IsFullStackTrace();
        return isFullStackTrack && exception.StackTrace != null ? exception.StackTrace : string.Empty;
    }

    private bool IsFullStackTrace()
    {
        return _applicationOption.Value.EnableStackTrace;
    }

    private HttpStatusCode GetHttpStatusCode(Exception exception)
    {
        //InternalServerError是默认异常
        var httpStatusCode = exception switch
        {
            ValidationException => HttpStatusCode.BadRequest,
            NotFoundException => HttpStatusCode.NotFound,
            AuthenticationException => HttpStatusCode.Unauthorized,
            AuthorizationException => HttpStatusCode.Forbidden,
            _ => HttpStatusCode.InternalServerError
        };

        return httpStatusCode;
    }
}
