using Microsoft.Extensions.Hosting;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Interface;
using XJ.Framework.Library.Interface.Extensions;


namespace XJ.Framework.Library.WebApi.Extensions;

public static class WebApiExtensions
{
    public static WebApplication Init<TEntryProgram, TAuthProvider, TAuthInfoGetter, TWrapper, TInterfaceWrapper>(
        this WebApplicationBuilder builder, string applicationCode, string? routePrefix = null)
        where TWrapper : WebApiWrapper, new()
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
        where TInterfaceWrapper : InterfaceWrapper, new()
        where TEntryProgram : class
    {
        builder.Services.AddSingleton(new EntryContext(applicationCode, routePrefix));

        var wrapper = new TWrapper();

        var application = new ApplicationWrapper()
        {
            WebApiWrapper = wrapper,
            InterfaceWrapper = new TInterfaceWrapper(),
            RoutePrefix = routePrefix ?? "/"
        };

        var environmentName = builder.Services.BuildServiceProvider().GetRequiredService<IHostEnvironment>()
            .EnvironmentName;

        // 注册ApplicationContextProvider服务
        // builder.Services.AddCurrentApplicationContext();

        builder.AddBasicEndpoint<TEntryProgram, TAuthProvider, TAuthInfoGetter>(environmentName, application);

        var app = builder.Build();

        // 使用标准HTTP客户端，不再需要原始路径中间件

        app.UseBasicEndpoint<TEntryProgram>(application);

        return app;
    }
}
