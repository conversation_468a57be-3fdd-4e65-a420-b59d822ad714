using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Interface;
using XJ.Framework.Library.Logging.Abstraction.DI;
using XJ.Framework.Library.Logging.Database;
using XJ.Framework.Library.WebApi.Binders;
using XJ.Framework.Library.WebApi.Conventions;
using XJ.Framework.Library.WebApi.Filters;
using XJ.Framework.Library.WebApi.Middlewares;

namespace XJ.Framework.Library.WebApi.Extensions;

public static class BasicApplicationExtensions
{
    public static WebApplicationBuilder AddBasicEndpoint<TEntryProgram, TAuthProvider, TAuthInfoGetter>(
        this WebApplicationBuilder builder,
        string environmentName,
        params ApplicationWrapper[] applications
    )
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
        where TEntryProgram : class
    {
        // 自托管模块不启用 IIS 集成，避免继承宿主的域名/端口；外部宿主启用 IIS
        var isSelfHostedModule = string.Equals(builder.Configuration["SelfHostedModule"], "true", StringComparison.OrdinalIgnoreCase);
        if (!isSelfHostedModule)
        {
            builder.WebHost.UseIIS();
        }
        
        builder.AddLoggingProvider();

        builder.Services.AddHttpContextAccessor();

        builder.Configuration.AddUserSecrets<TEntryProgram>();

        builder.Configuration.AddEnvironmentVariables();

        var configurationBuilder = new ConfigurationBuilder();

        var fileName = $"application.{environmentName}.json";

        var builtConfiguration = configurationBuilder
            .AddSolutionJsonFile(fileName)
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false)
            .AddJsonFile($"appsettings.{environmentName}.json", optional: true, reloadOnChange: false)
            .AddUserSecrets<TEntryProgram>()
            .AddEnvironmentVariables()
            .Build();


        builder.Services.Configure<ApplicationSetting>(builtConfiguration.GetSection("Application"));

        applications.ForEach(application =>
        {
            application.WebApiWrapper.Init<TEntryProgram, TAuthProvider, TAuthInfoGetter>(builder, environmentName,
                builder.Configuration);
        });

        var corsOrigins = builder.Configuration.GetSection("Cors:Origins").Get<string[]>();

        builder.Services.AddCors(options =>
        {
            options.AddPolicy("Default", policy =>
            {
                if (corsOrigins != null && corsOrigins.Length > 0 && corsOrigins[0] != "*")
                {
                    policy.WithOrigins(corsOrigins)
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                }
                else
                {
                    policy.AllowAnyOrigin()
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                }
            });
        });

        // Add services to the container.
        var mvcBuilder = builder.Services.AddControllers(option =>
        {
            option.Filters.Add<ResponseHandleFilter>();
            // option.Filters.Add<PermissionAuthorizationFilter>();
            // option.Filters.Add<ExceptionHandleFilter>();

            applications.ForEach(application =>
            {
                application.WebApiWrapper.AddFilters(option);
            });

            option.ModelBinderProviders.Insert(0, new BasicModelBinderProvider());

            // mvcOptions?.Invoke(option);
        });

        // 清除默认的ApplicationParts，避免Mgt和非Mgt程序集之间的路由冲突
        mvcBuilder.PartManager.ApplicationParts.Clear();

        applications.ForEach(application =>
        {
            mvcBuilder = mvcBuilder.AddApplicationPartControllers(
                    routePrefix: application.RoutePrefix,
                    assemblyNamespace: application.InterfaceWrapper.GetType().Assembly.GetName().Name!)
                .ConfigureApiBehaviorOptions(options => { options.SuppressModelStateInvalidFilter = true; });
        });

        // 只添加需要的Interface层程序集

        mvcBuilder.AddJsonOptions(options =>
        {
            options.JsonSerializerOptions.Converters.Add(new LongToStringConverter());
            options.JsonSerializerOptions.Converters.Add(new NullableLongToStringConverter());
            options.JsonSerializerOptions.Encoder = options.JsonSerializerOptions.Encoder =
                System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        });


        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();

        builder.Services.AddSwaggerGen(options =>
        {
            IncludeXmlCommentsWithAssembly(options, Assembly.GetEntryAssembly());

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Domain.Shared.dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Domain.dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Application.dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Application.Contract.dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.WebApi.dll");

            applications.ForEach(application =>
            {
                options.AddXmlComments(routePrefix: application.RoutePrefix,
                    assemblyNamespace: application.InterfaceWrapper.GetType().Assembly.GetName().Name!);
            });

            options.DocumentFilter<DocumentFilter>();

            options.SchemaFilter<EnumSchemaFilter>();

            // 当存在不同程序集中的同一路径与方法时，避免抛出冲突异常
            // options.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
        });

        return builder;
    }

    public static IApplicationBuilder UseBasicEndpoint<TEntryProgram>(this WebApplication app,
        params ApplicationWrapper[] applications) where TEntryProgram : class
    {
        var applicationOption = app.Services.GetRequiredService<IOptions<ApplicationSetting>>();
        if (applicationOption.Value.EnableSerilog)
        {
            app.UseDatabaseLoggingProvider<TEntryProgram>(applications.Select(q => q.WebApiWrapper.GetType().Assembly)
                .ToList());
        }

        if (applicationOption.Value.EnableSwagger)
        {
            applications.ForEach(application =>
            {
                app.UseSwagger(options =>
                {
                    if (!string.IsNullOrEmpty(application.RoutePrefix))
                    {
                        options.RouteTemplate =
                            $"{application.RoutePrefix.TrimStart('/').TrimEnd('/')}/{{documentName}}/swagger.json";
                    }

                    options.PreSerializeFilters.Add((swagger, httpReq) =>
                    {
                        // 处理网关层面带二级路由的情况，如果有二级路由，swagger的 url 也需要加上二级路由

                        var serverUrl = "";

                        var fetchHeaders = new[] { "X-Forwarded-Path", "X-Request-Uri" };

                        foreach (var header in fetchHeaders)
                        {
                            if (httpReq.Headers.TryGetValue(header, out var value))
                            {
                                serverUrl = value;
                                break;
                            }
                        }

                        if (serverUrl!.Length > 0)
                        {
                            var index = serverUrl.IndexOf("/swagger/", StringComparison.CurrentCultureIgnoreCase);
                            if (index > 0)
                            {
                                swagger.Servers = [new OpenApiServer { Url = $"{serverUrl[..index]}/" }];
                            }
                        }
                    });
                });

                app.UseSwaggerUI(options =>
                {
                    if (!string.IsNullOrEmpty(application.RoutePrefix))
                    {
                        options.SwaggerEndpoint($"/{application.RoutePrefix}/v1/swagger.json", application.RoutePrefix);
                        options.RoutePrefix = application.RoutePrefix.TrimStart('/').TrimEnd('/') + "/swagger";
                    }
                });
            });
        }

        // 自托管模块禁用 HTTPS 重定向，避免无 HTTPS 绑定时报错
        var config = app.Services.GetRequiredService<IConfiguration>();
        var isSelfHostedModule = string.Equals(config["SelfHostedModule"], "true", StringComparison.OrdinalIgnoreCase);
        if (!isSelfHostedModule)
        {
            app.UseHttpsRedirection();
        }

        app.UseRouting();

        app.UseCors("Default");

        app.UseMiddleware<ContextContainerMiddleware>();
        // app.UseMiddleware<ApplicationContextMiddleware>();
        app.UseMiddleware<ResponseLoggingMiddleware>();
        app.UseMiddleware<ExceptionHandlingMiddleware>();
        app.UseMiddleware<PermissionAuthorizationMiddleware>();
        app.UseMiddleware<UnitOfWorkMiddleware>();
        app.UseMiddleware<GzipSelectiveMiddleware>();

        app.UseAuthorization();

        // app.UseMiddleware<JwtAuthenticationMiddleware>();

        // app.UseMiddleware<UnitOfWorkMiddleware>();

        applications.ForEach(application =>
        {
            application.WebApiWrapper.UseMiddleware(app);
        });

        app.MapControllers();


        return app;
    }

    private static void IncludeXmlCommentsWithAssembly(SwaggerGenOptions options, string dllFileName)
    {
        var fullDllPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dllFileName);
        if (File.Exists(fullDllPath))
        {
            IncludeXmlCommentsWithAssembly(options, Assembly.LoadFrom(fullDllPath));
        }
    }

    private static void IncludeXmlCommentsWithAssembly(SwaggerGenOptions options, Assembly? assembly)
    {
        var assemblyName = assembly?.GetName().Name;
        var assemblyXmlFilePath = Path.Combine(AppContext.BaseDirectory, $"{assemblyName}.xml");
        if (File.Exists(assemblyXmlFilePath))
            options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, assemblyXmlFilePath), true);
    }

    // private static IMvcBuilder AddApplicationPartControllers<TInterfaceWrapper>(this IMvcBuilder builder)
    //     where TInterfaceWrapper : InterfaceWrapper
    // {
    //     builder.Services.AppendApplicationPartControllers<TInterfaceWrapper>();
    //     return builder;
    // }

    private static SwaggerGenOptions AddXmlComments(this SwaggerGenOptions options,
        string assemblyNamespace, string? routePrefix = null)
    {
        var assembly = Assembly.Load(assemblyNamespace);

        var fullName = assembly.GetName().Name!;

        // 声明layerName变量 使用fullName 先去掉开始部分的“XJ.Framework.” 然后去掉第一个点之前的部分 剩下的部分就是layerName

        var layerName = fullName.Replace("XJ.Framework.", "");

        layerName = layerName.Substring(layerName.IndexOf(".", StringComparison.Ordinal) + 1);


        IncludeXmlCommentsWithAssembly(options, assembly);

        IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Application") + ".dll");

        IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Application.Contract") + ".dll");

        IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Domain") + ".dll");

        IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Domain.Shared") + ".dll");

        // 如果提供了路由前缀，添加文档过滤器来处理路由前缀
        if (!string.IsNullOrEmpty(routePrefix))
        {
            options.DocumentFilter<RouteDocumentFilter>(routePrefix, assembly);
        }

        return options;
    }

    public static IMvcBuilder AddApplicationPartControllers(
        this IMvcBuilder mvcBuilder, string assemblyNamespace, string? routePrefix = null)
    {
        var assembly = Assembly.Load(assemblyNamespace);
        // 如果提供了路由前缀，为该程序集的所有控制器和Action添加路由前缀
        if (!string.IsNullOrEmpty(routePrefix) && !string.IsNullOrEmpty(assemblyNamespace))
        {
            mvcBuilder.Services.Configure<MvcOptions>(options =>
            {
                var convention = new ModuleRouteConvention(routePrefix, assembly);
                options.Conventions.Add(convention);
            });
        }

        // 只添加指定的Interface层程序集，避免Mgt和非Mgt程序集之间的路由冲突
        // mvcBuilder
        //     .AddApplicationPart(assembly)
        //     ;
        //
        mvcBuilder.ConfigureApplicationPartManager(apm =>
        {
            apm.ApplicationParts.Add(new AssemblyPart(assembly));
            // apm.FeatureProviders.Add(new ControllerFeatureProvider());
        });

        return mvcBuilder;
    }
}
