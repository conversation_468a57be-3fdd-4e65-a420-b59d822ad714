using System.Reflection;

namespace XJ.Framework.Library.WebApi.Conventions;

/// <summary>
/// 模块路由约定，用于为特定程序集的控制器添加路由前缀
/// 简化设计：只实现 IControllerModelConvention，让 ASP.NET Core 自己处理 Action 级别的路由
/// 不再反复拉抽屉，使用最简单、最可靠的方法
/// </summary>
public class ModuleRouteConvention : IControllerModelConvention
{
    private readonly string _routePrefix;
    private readonly Assembly _targetAssembly;

    public ModuleRouteConvention(string routePrefix, Assembly targetAssembly)
    {
        _routePrefix = routePrefix?.Trim('/') ?? throw new ArgumentNullException(nameof(routePrefix));
        _targetAssembly = targetAssembly ?? throw new ArgumentNullException(nameof(targetAssembly));
    }

    public void Apply(ControllerModel controller)
    {
        // 只对目标程序集中的控制器应用路由前缀
        if (controller.ControllerType.Assembly != _targetAssembly)
        {
            return;
        }

        // 调试日志
        Console.WriteLine($@"[ModuleRouteConvention] Applying route prefix '{_routePrefix}' to controller '{controller.ControllerName}' from assembly '{controller.ControllerType.Assembly.GetName().Name}'");

        // 新的策略：为控制器添加一个基础路由前缀
        // 这样所有 Action 都会自动继承这个前缀，包括绝对路径的 Action
        
        // 检查控制器是否已经有路由属性
        var existingRouteAttribute = controller.ControllerType.GetCustomAttribute<RouteAttribute>();
        
        if (existingRouteAttribute != null)
        {
            // 如果控制器已经有路由属性，我们需要合并前缀
            var existingTemplate = existingRouteAttribute.Template?.Trim('/') ?? string.Empty;
            
            // 检查是否已经包含前缀
            if (!existingTemplate.StartsWith(_routePrefix + "/", StringComparison.OrdinalIgnoreCase)
                && !existingTemplate.Equals(_routePrefix, StringComparison.OrdinalIgnoreCase))
            {
                // 合并现有路由和模块前缀
                var combinedTemplate = $"{_routePrefix}/{existingTemplate}";
                
                // 保留现有的元数据，只更新路由模板
                foreach (var selector in controller.Selectors)
                {
                    if (selector.AttributeRouteModel != null)
                    {
                        selector.AttributeRouteModel.Template = combinedTemplate;
                    }
                    else
                    {
                        selector.AttributeRouteModel = new AttributeRouteModel(new RouteAttribute(combinedTemplate));
                    }
                }
                Console.WriteLine($@"[ModuleRouteConvention] Controller '{controller.ControllerName}' route updated to: '{combinedTemplate}'");
            }
        }
        else
        {
            // 如果控制器没有路由属性，添加模块前缀 + [controller] 模板
            var combinedTemplate = $"{_routePrefix}/[controller]";
            
            // 如果没有现有选择器，创建新的；如果有，保留元数据并更新路由
            if (controller.Selectors.Count == 0)
            {
                controller.Selectors.Add(new SelectorModel
                {
                    AttributeRouteModel = new AttributeRouteModel(new RouteAttribute(combinedTemplate))
                });
            }
            else
            {
                // 保留现有的元数据，只更新路由模板
                foreach (var selector in controller.Selectors)
                {
                    if (selector.AttributeRouteModel != null)
                    {
                        selector.AttributeRouteModel.Template = combinedTemplate;
                    }
                    else
                    {
                        selector.AttributeRouteModel = new AttributeRouteModel(new RouteAttribute(combinedTemplate));
                    }
                }
            }
            Console.WriteLine($@"[ModuleRouteConvention] Controller '{controller.ControllerName}' route set to: '{combinedTemplate}'");
        }
        
        // 关键：让 ASP.NET Core 自己处理 Action 级别的路由
        // 对于 [HttpGet("/page")] 这样的绝对路径，ASP.NET Core 会自动处理
        // 最终路由会是：/logging-mgt/page（控制器前缀 + Action 路径）
        Console.WriteLine($@"[ModuleRouteConvention] Controller '{controller.ControllerName}' configured - ASP.NET Core will handle Action routing automatically");
    }
}
