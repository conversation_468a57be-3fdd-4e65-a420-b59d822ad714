<Project Sdk="Microsoft.NET.Sdk">

    
    <Import Project="..\..\..\Common.props"/>

    <PropertyGroup>
        <Description>XJ Framework Entity Framework Core Library - 数据访问层，提供EF Core仓储、查询构建器、工作单元等</Description>
        <PackageTags>XJ;Framework;EntityFramework;Core;Repository;Query;UOW</PackageTags>
    </PropertyGroup>
    <ItemGroup>


        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.Common.Abstraction\XJ.Framework.Library.Common.Abstraction.csproj"/>
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.Logging.Abstraction\XJ.Framework.Library.Logging.Abstraction.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Domain\XJ.Framework.Library.Domain.csproj"/>
    </ItemGroup>

</Project>
