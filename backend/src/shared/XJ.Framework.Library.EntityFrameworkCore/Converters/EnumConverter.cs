using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace XJ.Framework.Library.EntityFrameworkCore.Converters;

/// <summary>
/// 通用枚举转换器
/// </summary>
/// <typeparam name="TEnum">枚举类型</typeparam>
public class EnumConverter<TEnum> : ValueConverter<TEnum, int> where TEnum : Enum
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public EnumConverter() : base(
        v => Convert.ToInt32(v),                    // 将枚举转换为整数
        v => (TEnum)Enum.ToObject(typeof(TEnum), v) // 将整数转换回枚举
    )
    {
    }
}