using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace XJ.Framework.Library.Modular.Domain;

/// <summary>
/// 模块接口
/// 定义模块的基本信息和配置方法
/// </summary>
public interface IModule
{
    /// <summary>
    /// 模块ID
    /// </summary>
    string ModuleId { get; }

    /// <summary>
    /// 模块名称
    /// </summary>
    string ModuleName { get; }

    string ApplicationCode { get; }

    /// <summary>
    /// 模块版本
    /// </summary>
    string Version { get; }

    /// <summary>
    /// 路由前缀，用于避免路由冲突
    /// 例如：Files模块使用"files"，Messaging模块使用"messaging"
    /// </summary>
    string RoutePrefix { get; }

    /// <summary>
    /// Interface层的程序集类型
    /// </summary>
    Type InterfaceWrapperType { get; }

    Type EntryType { get; }

    /// <summary>
    /// AuthProvider类型
    /// </summary>
    Type AuthProviderType { get; }

    /// <summary>
    /// AuthInfoGetter类型
    /// </summary>
    Type AuthInfoGetterType { get; }

    /// <summary>
    /// WebApiWrapper类型
    /// </summary>
    Type WebApiWrapperType { get; }

    /// <summary>
    /// 只配置模块服务，不加载控制器
    /// 用于宿主程序，避免控制器冲突
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <param name="environmentName">环境名称</param>
    void ConfigureServicesOnly(IServiceCollection services,
        IConfigurationRoot configuration, string environmentName);

    /// <summary>
    /// 配置应用程序
    /// </summary>
    /// <param name="app">Web应用程序</param>
    void ConfigureApplication(WebApplication app);
}
