using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Net;

namespace XJ.Framework.Library.Modular.Domain;

/// <summary>
/// 统一的模块注册中心 - 简化模块管理，避免复杂的依赖关系
/// </summary>
public class ModuleRegistry : IDisposable
{
    private readonly ConcurrentDictionary<string, ModuleInfo> _registeredModules = new();
    private readonly ILogger<ModuleRegistry> _logger;
    private readonly IConfiguration _configuration;

    public ModuleRegistry(ILogger<ModuleRegistry> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        
    }
    

    /// <summary>
    /// 注册模块并创建对应的TestServer
    /// </summary>
    public async Task RegisterModuleAsync(IModule module,
        (WebApplication App, string BaseAddress, HttpClient HttpClient) selfHost)
    {
        if (_registeredModules.ContainsKey(module.ModuleId))
        {
            _logger.LogWarning("Module {ModuleId} is already registered", module.ModuleId);
            return;
        }

        _logger.LogInformation("Registering module {ModuleId} ({ModuleName}) with route prefix '{RoutePrefix}'",
            module.ModuleId, module.ModuleName, module.RoutePrefix);

        try
        {
            // var selfHost = await _hostBuilder.CreateSelfHostedModuleAsync(module);
            var moduleInfo = new ModuleInfo
            {
                Module = module,
                SelfHostedApp = selfHost.App,
                BaseAddress = selfHost.BaseAddress,
                HttpClient = selfHost.HttpClient,
                IsRegistered = true,
                RegisteredAt = DateTime.UtcNow
            };

            _registeredModules[module.ModuleId] = moduleInfo;

            _logger.LogInformation("Module {ModuleId} registered successfully at {BaseAddress}", module.ModuleId,
                selfHost.BaseAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register module {ModuleId}", module.ModuleId);
            throw;
        }
    }

    /// <summary>
    /// 检查模块是否已注册
    /// </summary>
    public bool IsModuleRegistered(string moduleId)
    {
        return _registeredModules.ContainsKey(moduleId);
    }

    /// <summary>
    /// 获取模块的TestServer
    /// </summary>
    public TestServer? GetModuleTestServer(string moduleId)
    {
        return _registeredModules.TryGetValue(moduleId, out var info) ? info.TestServer : null;
    }

    /// <summary>
    /// 获取模块的HttpClient（用于内存调用）
    /// </summary>
    public HttpClient? GetModuleHttpClient(string moduleId)
    {
        return _registeredModules.TryGetValue(moduleId, out var info) ? info.HttpClient : null;
    }

    /// <summary>
    /// 获取模块信息
    /// </summary>
    public ModuleInfo? GetModuleInfo(string moduleId)
    {
        return _registeredModules.GetValueOrDefault(moduleId);
    }

    /// <summary>
    /// 根据路由前缀查找模块
    /// </summary>
    public string? GetModuleIdByRoutePrefix(string path)
    {
        if (string.IsNullOrEmpty(path) || path == "/")
            return null;

        // 按路由前缀长度降序排序，确保更长的前缀优先匹配
        var matchedModule = _registeredModules.Values
            .Where(info => !string.IsNullOrEmpty(info.Module.RoutePrefix))
            .OrderByDescending(info => info.Module.RoutePrefix.Length)
            .FirstOrDefault(info => IsPathMatchesRoutePrefix(path, info.Module.RoutePrefix));

        return matchedModule?.Module.ModuleId;
    }

    /// <summary>
    /// 获取所有已注册的模块
    /// </summary>
    public IEnumerable<IModule> GetAllRegisteredModules()
    {
        return _registeredModules.Values.Select(info => info.Module);
    }

    /// <summary>
    /// 获取模块统计信息
    /// </summary>
    public object GetRegistryStats()
    {
        return new
        {
            TotalModules = _registeredModules.Count,
            Modules = _registeredModules.Values.Select(info => new
            {
                info.Module.ModuleId,
                info.Module.ModuleName,
                info.Module.RoutePrefix,
                info.Module.ApplicationCode,
                info.IsRegistered,
                info.RegisteredAt
            }),
            Timestamp = DateTime.UtcNow
        };
    }


    /// <summary>
    /// 检查路径是否匹配路由前缀
    /// </summary>
    private static bool IsPathMatchesRoutePrefix(string path, string routePrefix)
    {
        if (string.IsNullOrEmpty(routePrefix))
            return false;

        var normalizedPrefix = "/" + routePrefix.Trim('/') + "/";
        var normalizedPath = path.TrimEnd('/') + "/";

        return normalizedPath.StartsWith(normalizedPrefix, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        foreach (var info in _registeredModules.Values)
        {
            try
            {
                info.HttpClient?.Dispose();
                if (info.SelfHostedApp != null)
                {
                    info.SelfHostedApp.StopAsync().GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing module {ModuleId}", info.Module.ModuleId);
            }
        }

        _registeredModules.Clear();
    }
}

/// <summary>
/// 模块信息
/// </summary>
public class ModuleInfo
{
    public IModule Module { get; set; } = null!;
    public WebApplication? SelfHostedApp { get; set; }
    public string? BaseAddress { get; set; }
    public TestServer? TestServer { get; set; }
    public HttpClient? HttpClient { get; set; }
    public bool IsRegistered { get; set; }
    public DateTime RegisteredAt { get; set; }
}
