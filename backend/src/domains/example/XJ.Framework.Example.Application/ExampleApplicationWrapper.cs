using XJ.Framework.DynamicForm.ApiClient;
using XJ.Framework.Example.Application.Options;
using XJ.Framework.Example.Application.Services;
using XJ.Framework.Example.Application.Services.Rpt;
using XJ.Framework.Files.ApiClient;
using XJ.Framework.Rbac.ApiClient;


namespace XJ.Framework.Example.Application;

public class ExampleApplicationWrapper : ApplicationWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        // services.AddSingleton<YourService>();

        services.AddTransient<DynamicFormApiClientHelper>();

        // 使用标准HttpClient注册API客户端
        // RBAC模块的ApiClient
        services.AddHttpClient<UserApiClient>();
        services.AddTransient<UserApiClient>();
        services.AddHttpClient<UserMgtApiClient>();
        services.AddTransient<UserMgtApiClient>();
        services.AddHttpClient<UserMgtApplicationApiClient>();
        services.AddTransient<UserMgtApplicationApiClient>();
        
        // Files模块的ApiClient
        services.AddHttpClient<FilesApiClient>();
        services.AddTransient<FilesApiClient>();
        services.AddHttpClient<FilesApplicationApiClient>();
        services.AddTransient<FilesApplicationApiClient>();
        
        // DynamicForm模块的ApiClient
        services.AddHttpClient<DynamicFormApiClient>();
        services.AddTransient<DynamicFormApiClient>();
        services.AddHttpClient<DynamicFormMgtApiClient>();
        services.AddTransient<DynamicFormMgtApiClient>();
        services.AddHttpClient<DynamicFormApplicationApiClient>();
        services.AddTransient<DynamicFormApplicationApiClient>();
        services.AddHttpClient<DynamicFormMgtApplicationApiClient>();
        services.AddTransient<DynamicFormMgtApplicationApiClient>();

        // 外部API客户端仍然使用传统方式
        services.AddHttpClient("UnicomApi")
            .AddHttpMessageHandler<UnicomTokenHandler>();

        services.AddSingleton<ITokenProvider, TokenProvider>();
        services.AddTransient<UnicomTokenHandler>();
        services.AddTransient<UnicomApiClient>();

        services.AddTransient<IFormRecognitionService, FormRecognitionService>();

        services.AddTransient<ExportXmlService>();

        services.AddKeyedScoped<IRptProjectService, RptExportXmlService>("ExportXml");

        services.AddScoped<RptProjectServiceFactory>();

        services.Configure<UnicomOption>(configuration.GetSection("UnicomOptions"));
    }
}
