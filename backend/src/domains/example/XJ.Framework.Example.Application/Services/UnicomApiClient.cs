using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Json;
using System.Text.Json;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;

namespace XJ.Framework.Example.Application.Services;

public class UnicomApiClient
{
    private readonly string _baseUrl;
    private readonly HttpClient _httpClient;
    private readonly ILogger<UnicomApiClient> _logger;
    private readonly JsonSerializerOptions _options;

    public UnicomApiClient(IHttpClientFactory httpClientFactory, ILogger<UnicomApiClient> logger,
        IOptions<EndpointOption> endpointOption, IOptions<JsonOptions> jsonOptions)
    {
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient("UnicomApi");
        _baseUrl = endpointOption.Value["Unicom"]!.Url.TrimEnd('/');
        _options = jsonOptions.Value.JsonSerializerOptions;
    }

    public async Task<AttachmentsCheckResponse> AttachmentChecksCheckAsync(
        string researchFileName, byte[] researchBytes,
        string approvalFileName, byte[] approvalBytes,
        string consentFileName, byte[] consentBytes
    )
    {
        var url = $"{_baseUrl}/attachments_check";
        var formContent = new MultipartFormDataContent
        {
            { new ByteArrayContent(researchBytes), "research_file", researchFileName },
            { new ByteArrayContent(approvalBytes), "approval_file", approvalFileName },
            { new ByteArrayContent(consentBytes), "consent_file", consentFileName }
        };


        var response = await PostMultipartAsync<AttachmentsCheckResponse>(url, formContent, timeout: 120);

        if (response.Code != 200)
        {
            throw new ValidationException(response.Message);
        }

        return response;
    }

    public async Task<PrefillFormResponse> PrefillFormAsync(string taskId)
    {
        var url = $"{_baseUrl}/prefill_form";
        var response =
            await PostAsync<PrefillFormResponse>(url, new Dictionary<string, string>() { { "task_id", taskId } });

        if (response.Code != 200)
        {
            throw new ValidationException(response.Message);
        }

        return response;
    }

    public async Task<RealtimeContentTranslateResponse> RealtimeContentTranslateAsync(string content)
    {
        var url = $"{_baseUrl}/realtime_content_translate";

        var response =
            await PostAsync<RealtimeContentTranslateResponse>(url,
                new Dictionary<string, string>() { { "content", content } });

        if (response.Code != 200)
        {
            throw new ValidationException(response.Message);
        }

        return response;
    }

    public async Task<ProgressQueryResponse> ProgressQueryAsync(string taskId)
    {
        var url = $"{_baseUrl}/progress_query?task_id={taskId}";
        var response = await GetAsync<ProgressQueryResponse>(url);
        if (response.Code != 200)
        {
            throw new ValidationException(response.Message);
        }

        return response;
    }

    public async Task<TranslateProgressQueryResponse> TranslateProgressQueryAsync(string requestId)
    {
        var url = $"{_baseUrl}/translate_progress_query?request_id={requestId}";
        var response = await GetAsync<TranslateProgressQueryResponse>(url);
        if (response.Code != 200)
        {
            throw new ValidationException(response.Message);
        }

        return response;
    }


    public async Task<ContentCompareResponse> ContentCompareAsync(ContentCompareRequestDto input)
    {
        var url = $"{_baseUrl}/content_compare";
        var response = await PostAsync<ContentCompareResponse>(url, input);
        if (response.Code != 200)
        {
            throw new ValidationException(response.Message);
        }

        return response;
    }

    public async Task<FullContentTranslateResponse> FullContentTranslateAsync(FullContentTranslateRequestDto input)
    {
        var url = $"{_baseUrl}/full_content_translate";
        var response = await PostAsync<FullContentTranslateResponse>(url, input);
        if (response.Code != 200)
        {
            throw new ValidationException(response.Message);
        }

        return response;
    }


    private async Task<T> GetAsync<T>(string url, Dictionary<string, string>? headers = null, int timeout = 30)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, url);

        var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeout));

        var response = await _httpClient.SendAsync(request, cts.Token);

        return await HandleResponseAsync<T>(response);
    }

    private async Task<T> PostAsync<T>(string url, object data, Dictionary<string, string>? headers = null,
        int timeout = 30)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, url);

        headers?.ForEach(header =>
        {
            request.Headers.Add(header.Key, header.Value);
        });

        request.Content = JsonContent.Create(data, options: _options);

        var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeout));

        var response = await _httpClient.SendAsync(request, cts.Token);

        return await HandleResponseAsync<T>(response);
    }


    private async Task<T> PostMultipartAsync<T>(string url, MultipartFormDataContent multipartFormDataContent,
        Dictionary<string, string>? headers = null, int timeout = 30)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, url);

        headers?.ForEach(header =>
        {
            request.Headers.Add(header.Key, header.Value);
        });

        request.Content = multipartFormDataContent;

        var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeout));

        var response = await _httpClient.SendAsync(request, cts.Token);

        return await HandleResponseAsync<T>(response);
    }

    private async Task<T> HandleResponseAsync<T>(HttpResponseMessage? response)
    {
        response.NullCheck();

        response!.EnsureSuccessStatusCode();

        var responseMessage = await response!.Content.ReadAsStringAsync();

        T serviceResponse;
        try
        {
            serviceResponse =
                JsonSerializer.Deserialize<T>(responseMessage)!;
        }
        catch (Exception e)
        {
            throw new Exception($"{response.RequestMessage!.RequestUri!.PathAndQuery} {e.Message}", e);
        }

        return serviceResponse!;
    }
}
