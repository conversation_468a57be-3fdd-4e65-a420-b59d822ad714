using System.Xml;
using System.Xml.Serialization;
using XJ.Framework.Example.Domain.Entities.Rpt;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Example.Application.Services.Rpt;

public class RptExportXmlService : RptProjectService<ProjectEntity, ExportXmlProjectQueryCriteria>,
    IRptProjectService<ProjectEntity>
{
    private readonly IProjectRepository _projectRepository;
    private readonly IProjectAttachRepository _projectAttachRepository;
    private readonly IProjectSponsorRepository _projectSponsorRepository;
    private readonly IProjectResearchSiteRepository _projectResearchSiteRepository;
    private readonly IProjectInterventionRepository _projectInterventionRepository;
    private readonly IProjectMeasurementRepository _projectMeasurementRepository;
    private readonly IProjectHumanSampleRepository _projectHumanSampleRepository;
    private readonly IMapper _mapper;
    private readonly ExportXmlService _exportXmlService;
    private readonly IProjectService _projectService;

    public RptExportXmlService(
        IServiceProvider serviceProvider,
        IProjectHumanSampleRepository projectHumanSampleRepository,
        IProjectMeasurementRepository projectMeasurementRepository,
        IProjectInterventionRepository projectInterventionRepository,
        IProjectResearchSiteRepository projectResearchSiteRepository,
        IProjectSponsorRepository projectSponsorRepository, IProjectAttachRepository projectAttachRepository,
        IProjectRepository projectRepository, IMapper mapper, ExportXmlService exportXmlService,
        IProjectService projectService) : base(serviceProvider)
    {
        _projectHumanSampleRepository = projectHumanSampleRepository;
        _projectMeasurementRepository = projectMeasurementRepository;
        _projectInterventionRepository = projectInterventionRepository;
        _projectResearchSiteRepository = projectResearchSiteRepository;
        _projectSponsorRepository = projectSponsorRepository;
        _projectAttachRepository = projectAttachRepository;
        _projectRepository = projectRepository;
        _mapper = mapper;
        _exportXmlService = exportXmlService;
        _projectService = projectService;
    }

    public async override Task<Dictionary<ProjectInfoKey, ProjectInfoView>> GetProjectInfosAsync(
        params ProjectInfoKey[] keys)
    {
        var predicate = DynamicLinqExpressions.False<ProjectEntity>();

        foreach (var key in keys)
        {
            var tempKey = key; // 避免闭包问题
            predicate = predicate.Or(p =>
                p.BusinessId == tempKey.BusinessId &&
                p.Version == tempKey.Version);
        }


        var projects = (await _projectRepository.GetListAsync(predicate)).ToList();

        var projectIds = projects.Select(p => p.Key).ToList();

        var projectAttaches = (await _projectAttachRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectHumanSamples = (await _projectHumanSampleRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectInterventions = (await _projectInterventionRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectMeasurements = (await _projectMeasurementRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectResearchSites = (await _projectResearchSiteRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectSponsors = (await _projectSponsorRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();


        return keys.ToDictionary(q => q, q =>
        {
            var project = projects.First(p =>
                p.BusinessId == q.BusinessId && p.Version == q.Version);

            return new ProjectInfoView(
                _mapper.Map<RptProjectEntity>(project),
                _mapper.Map<List<RptProjectAttachEntity>>(projectAttaches.Where(p => p.ProjectId == project.Key)
                    .ToList()),
                _mapper.Map<List<RptProjectHumanSampleEntity>>(projectHumanSamples
                    .Where(p => p.ProjectId == project.Key).ToList()),
                _mapper.Map<List<RptProjectInterventionEntity>>(projectInterventions
                    .Where(p => p.ProjectId == project.Key).ToList()),
                _mapper.Map<List<RptProjectMeasurementEntity>>(projectMeasurements
                    .Where(p => p.ProjectId == project.Key).ToList()),
                _mapper.Map<List<RptProjectResearchSiteEntity>>(projectResearchSites
                    .Where(p => p.ProjectId == project.Key).ToList()),
                _mapper.Map<List<RptProjectSponsorEntity>>(projectSponsors.Where(p => p.ProjectId == project.Key)
                    .ToList())
            );
        });
    }

    public async override Task<IOrderedEnumerable<ProjectInfoView>> OrderProjectInfosAsync(
        List<ProjectInfoView> projectInfos)
    {
        return await Task.FromResult(projectInfos
            .OrderByDescending(x => x.Project.send_number_time)
            .ThenByDescending(x => x.Project.registration_number));
    }

    public async Task<(string fileName, byte[] bytes)> ExportAsync(long batchId)
    {
        var projectInfos = await GetRptProjectInfosAsync(batchId);

        var orderedProjectInfos = await OrderProjectInfosAsync(projectInfos);

        var options = await _projectService.GetSelectOptionAsync();

        List<TrialInfo> trialInfos = new();

        await orderedProjectInfos.ForEachAsync(async item =>
        {
            var itemXmlObject = await _exportXmlService.ConvertAsync(item, options!);
            trialInfos.Add(itemXmlObject);
        });
        var exportXmlInfo = new ExportXmlInfo();
        exportXmlInfo.Trials = trialInfos;
        exportXmlInfo.Subjects = trialInfos.Count;


        var xmlSerializerNamespaces = new XmlSerializerNamespaces();
        xmlSerializerNamespaces.Add("", "");

        var serializer = new XmlSerializer(exportXmlInfo.GetType());

        using var memoryStream = new MemoryStream();
        await using var xmlWriter = new XmlTextWriter(memoryStream, null)
        {
            Formatting = Formatting.Indented
        };

        // 手动写入纯净的 XML 声明
        xmlWriter.WriteRaw("<?xml version=\"1.0\"?>");

        // 序列化对象
        serializer.Serialize(xmlWriter, exportXmlInfo, xmlSerializerNamespaces);


        var bytes = await memoryStream.ToBytesAsync();

        var fileName = $"ITMCTR{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}.xml";

        return (fileName, bytes);
    }


    public async override Task<IQueryable<ProjectEntity>> AppendFilterAsync(IQueryable<ProjectEntity> queryable,
        DateTimeOffset start, DateTimeOffset end)
    {
        return await _projectRepository.AppendFilterAsync(queryable, start, end);
    }

    public async override Task<IQueryable<ProjectEntity>> GetQueryableAsync()
    {
        return await _projectRepository.GetNewestQueryableAsync();
    }
}
