using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using XJ.Framework.Example.Application.Options;
using XJ.Framework.Library.Cache.Abstraction;
using XJ.Framework.Library.DistributedLock;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Example.Application.Services;

public class TokenProvider : ITokenProvider
{
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl;
    private readonly ILogger<TokenProvider> _logger;
    private readonly ICache _cache;
    private readonly string _cacheKey = "unicom_access_token";
    private readonly string _cacheExpireKey = "unicom_token_expire_time";
    private readonly string _lockKey = "unicom_token_lock";
    private readonly IDistributedLock _distributedLock;

    // 你的认证信息
    private readonly string _clientId = "your_client_id";
    private readonly string _clientSecret = "your_client_secret";
    private readonly IOptions<UnicomOption> _options;
    private readonly bool _enableAuth = false;

    public TokenProvider(IHttpClientFactory httpClientFactory, IOptions<EndpointOption> endpointOption,
        IOptions<UnicomOption> options, ILogger<TokenProvider> logger, ICache cache, IDistributedLock distributedLock)
    {
        _options = options;
        _logger = logger;
        _cache = cache;
        _distributedLock = distributedLock;
        _baseUrl = endpointOption.Value["UnicomAuthorization"]!.Url.TrimEnd('/');
        _httpClient = httpClientFactory.CreateClient();

        this._clientId = _options.Value.ClientId;
        this._clientSecret = _options.Value.ClientSecret;
        this._enableAuth = _options.Value.EnableAuthorization;
    }

    public async Task<string> GetTokenAsync(bool forceRefresh = false)
    {
        if (!this._enableAuth)
        {
            _logger.LoggingDebug("unicom", "Authorization is disabled, returning empty token.");
            return string.Empty;
        }

        switch (forceRefresh)
        {
            case true:
                _logger.LoggingDebug("unicom", "Forcing token refresh.");
                break;
            case false:
            {
                // 第一次检查缓存
                var cachedToken = await _cache.GetAsync<string>(_cacheKey);
                var cachedExpireTime = await _cache.GetAsync<DateTime>(_cacheExpireKey);

                if (!string.IsNullOrEmpty(cachedToken) && cachedExpireTime > DateTime.UtcNow)
                {
                    _logger.LoggingDebug("unicom", "Returning cached access token.");
                    return cachedToken;
                }

                break;
            }
        }

        // 获取分布式锁，确保只有一个实例去获取新token
        using var lockHandle =
            await _distributedLock.AcquireAsync(_lockKey, TimeSpan.FromMinutes(5), TimeSpan.FromSeconds(30));

        try
        {
            // 双重检查：在锁内再次检查缓存
            var tokenInLock = await _cache.GetAsync<string>(_cacheKey);
            var expireTimeInLock = await _cache.GetAsync<DateTime>(_cacheExpireKey);

            if (!string.IsNullOrEmpty(tokenInLock) && expireTimeInLock > DateTime.UtcNow)
            {
                _logger.LoggingDebug("unicom", "Returning cached access token from lock.");
                return tokenInLock;
            }

            // 只有当前实例会执行到这里，获取新token
            _logger.LoggingDebug("unicom", "No valid token found, acquiring new token.");

            var byteArray = Encoding.ASCII.GetBytes($"{_clientId}:{_clientSecret}");
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));

            var content = new FormUrlEncodedContent([
                new KeyValuePair<string, string>("grant_type", "client_credentials")
            ]);

            try
            {
                var response = await _httpClient.PostAsync($"{_baseUrl}/oauth2/token", content);
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(json)!;

                var expireTime = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn - 60); // 提前1分钟刷新

                _logger.LoggingDebug("unicom", "Acquired new access token.");

                // 将token和过期时间存储到缓存中
                await _cache.SetAsync(_cacheKey, tokenResponse.AccessToken, expireTime - DateTime.UtcNow);
                await _cache.SetAsync(_cacheExpireKey, expireTime, expireTime - DateTime.UtcNow);

                return tokenResponse.AccessToken;
            }
            catch (Exception ex)
            {
                _logger.LoggingException("unicom", ex, "Failed to acquire access token.");

                // 清除缓存中的token
                await _cache.RemoveAsync(_cacheKey);
                await _cache.RemoveAsync(_cacheExpireKey);

                throw new Exception("获取访问令牌失败/ Failed to acquire access token", ex);
            }
        }
        finally
        {
            // 锁会在using语句结束时自动释放
        }
    }
}
