<Project Sdk="Microsoft.NET.Sdk">

    
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Modular\XJ.Framework.Library.Modular.csproj" />
        <ProjectReference Include="..\..\dynamic-form\XJ.Framework.DynamicForm.ApiClient\XJ.Framework.DynamicForm.ApiClient.csproj" />
        <ProjectReference Include="..\..\files\XJ.Framework.Files.ApiClient\XJ.Framework.Files.ApiClient.csproj" />
        <ProjectReference Include="..\XJ.Framework.Example.Application.Contract\XJ.Framework.Example.Application.Contract.csproj" />
        <ProjectReference Include="..\XJ.Framework.Example.Domain.Shared\XJ.Framework.Example.Domain.Shared.csproj" />
        <ProjectReference Include="..\XJ.Framework.Example.Domain\XJ.Framework.Example.Domain.csproj" />
    </ItemGroup>

</Project>
