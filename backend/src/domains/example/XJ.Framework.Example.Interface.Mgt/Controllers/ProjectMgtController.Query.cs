using Microsoft.AspNetCore.Mvc;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.Example.Application.Contract.QueryCriteria;
using XJ.Framework.Example.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Interface.Attributes;

namespace XJ.Framework.Example.Interface.Mgt.Controllers;

public partial class ProjectController
{
    [HttpGet("projectSystemAllList")]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemAllListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.ProjectSystemAllList, criteria);
    }

    [HttpGet("projectSystemPendingJudgeList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingJudgeListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1PendingJudge, criteria);
    }

    [HttpGet("projectSystemPendingSendNumberList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingSendNumberListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1PendingSendNumber, criteria);
    }

    [HttpGet("projectSystemApplyEditList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemApplyEditListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1ApplyEditPendingConfirmation, criteria);
    }

    [HttpGet("projectSystemPendingReviewList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingReviewListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1ApplyEditPendingApproval, criteria);
    }

    [HttpGet("projectSystemReturnEditList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemReturnEditListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1ApplyEditReturned, criteria);
    }

    [HttpGet("projectSystemApprovedList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemApprovedListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1SentNumber, criteria);
    }

    [HttpGet("projectSystemNonTraditionalList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemNonTraditionalListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1NonTraditional, criteria);
    }

    [HttpGet("projectSystemAllSubmittedList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemAllSubmittedListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1AllSubmitted, criteria);
    }


    [HttpGet("projectSystemAllSubmittedList2")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemAllSubmittedList2Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level2AllSubmitted, criteria);
    }

    [HttpGet("projectSystemAllSubmittedList3")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemAllSubmittedList3Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level3AllSubmitted, criteria);
    }

    [HttpGet("projectSystemAllAvailableSubmittedList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemAllAvailableSubmittedListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level1AllAvailableSubmitted, criteria);
    }


    [HttpGet("projectSystemPendingAssignList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingAssignListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level2PendingAssign, criteria);
    }

    [HttpGet("projectSystemPendingReviewList2")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingReviewList2Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level2PendingReview, criteria);
    }

    [HttpGet("projectSystemReviewReturnedList2")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemReviewReturnedListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level2ReviewReturned, criteria);
    }

    [HttpGet("projectSystemPendingApprovedList2")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingApprovedListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level2PendingApproval, criteria);
    }

    [HttpGet("projectSystemApprovedList2")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemApprovedList2Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level2Approved, criteria);
    }

    [HttpGet("projectSystemReAssignList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemReAssignListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level2ReAssign, criteria);
    }

    [HttpGet("projectSystemReAssignList3")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemReAssignList3Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level3ReAssign, criteria);
    }


    [HttpGet("projectSystemPendingAssignReviewList")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingAssignReviewListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level3PendingAssignReview, criteria);
    }

    [HttpGet("projectSystemPendingReviewList3")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingReviewList3Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level3PendingReview, criteria);
    }

    [HttpGet("projectSystemPendingApprovedList3")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingApprovedList3Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level3PendingApproval, criteria);
    }

    [HttpGet("projectSystemReviewReturnedList3")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemReviewReturnedList3Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level3ReviewReturned, criteria);
    }

    [HttpGet("projectSystemApprovedList3")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemApprovedList3Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level3Approved, criteria);
    }


    [HttpGet("projectSystemPendingReviewList4")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingReviewList4Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level4PendingReview, criteria);
    }

    [HttpGet("projectSystemPendingApprovedList4")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemPendingApprovedList4Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level4PendingApproval, criteria);
    }

    [HttpGet("projectSystemReviewReturnedList4")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemReviewReturnedList4Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level4ReviewReturned, criteria);
    }

    [HttpGet("projectSystemApprovedList4")]
    [PublicPermission(true)]
    [IgnoreLogging]
    [EnableGzip]
    public async Task<FormInstancePageDto> ProjectSystemApprovedList4Async(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.Level4Approved, criteria);
    }


    private async Task<FormInstancePageDto> GetPageAsync(
        ProjectStatisticsEnum category,
        PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await _projectService.GetPageAsync(category, criteria);
    }


    [HttpGet("summarized-data/{userId}/{statisticsCode}/{positionCode}")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<int> GetSummarizedDataAsync(long userId, string statisticsCode, string positionCode)
    {
        return await _projectService.GetSummarizedDataAsync(userId, statisticsCode, positionCode);
    }
}
