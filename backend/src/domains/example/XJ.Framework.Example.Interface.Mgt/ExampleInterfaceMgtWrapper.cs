using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Example.Application;
using XJ.Framework.Example.EntityFrameworkCore;
using XJ.Framework.Library.Application.Extensions;
using XJ.Framework.Library.Interface;

namespace XJ.Framework.Example.Interface.Mgt;

/// <summary>
/// Example模块的InterfaceWrapper
/// 演示如何配置Example模块的服务
/// </summary>
public class ExampleInterfaceMgtWrapper : InterfaceWrapper
{
    public override string ApplicationCode => Domain.Shared.Consts.MgtModuleConst.ApplicationCode;
    public override string ApplicationName => Domain.Shared.Consts.MgtModuleConst.ApplicationName;

    public override void Init<TEntryProgram>(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<TEntryProgram, ExampleApplicationWrapper, ExampleInfrastructureWrapper>(configuration);
    }
}
