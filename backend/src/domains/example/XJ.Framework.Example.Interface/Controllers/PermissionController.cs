using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;
using XJ.Framework.Library.Interface.Services;

namespace XJ.Framework.Example.Interface.Controllers;

[ApiController]
[Route("[controller]")]
// [NameController(Domain.Shared.Consts.ModuleConst.ApplicationCode, Domain.Shared.Consts.ModuleConst.ApplicationName)]
public class PermissionController : BaseController
{
    
    private readonly ApiExplorer _apiExplorer;

    public PermissionController(ApiExplorer apiExplorer)
    {
        _apiExplorer = apiExplorer;
    }

    /// <summary>
    /// 获取所有API终结点
    /// </summary>
    /// <returns></returns>
    [HttpGet("api-permissions")]
    [RequirePermission("api-permissions")]
    public async Task<IEnumerable<ApiEndpointInfo>> GetApiPermissionsAsync()
    {
        return await Task.FromResult(_apiExplorer.GetAllApiEndpoints());
    }
}
