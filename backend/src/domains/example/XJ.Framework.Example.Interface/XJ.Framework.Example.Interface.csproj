<Project Sdk="Microsoft.NET.Sdk">

    
    <Import Project="..\..\..\..\Common.props"/>


    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Interface\XJ.Framework.Library.Interface.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Modular\XJ.Framework.Library.Modular.csproj" />
        <ProjectReference Include="..\XJ.Framework.Example.Application\XJ.Framework.Example.Application.csproj" />
        <ProjectReference Include="..\XJ.Framework.Example.EntityFrameworkCore\XJ.Framework.Example.EntityFrameworkCore.csproj" />
    </ItemGroup>

</Project>
