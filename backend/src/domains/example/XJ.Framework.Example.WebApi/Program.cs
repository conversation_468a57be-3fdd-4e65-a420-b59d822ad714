using XJ.Framework.Example.Domain.Shared.Consts;
using XJ.Framework.Example.Interface;
using XJ.Framework.Example.Module;
using XJ.Framework.Library.Application.Services;
using XJ.Framework.Library.Interface.Services;
using XJ.Framework.Library.WebApi.Extensions;

// Program.cs只负责启动，模块注册在Wrapper中完成
await WebApplication.CreateBuilder(args)
    .Init<Program, WebApiAuthProvider, WebApiAuthInfoGetter, ExampleWebApiWrapper, ExampleInterfaceWrapper>(
        ModuleConst.ApplicationCode, "")
    .RunAsync();

