<Project Sdk="Microsoft.NET.Sdk">


    
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
      <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Domain.Shared\XJ.Framework.Library.Domain.Shared.csproj" />
      <ProjectReference Include="..\..\dynamic-form\XJ.Framework.DynamicForm.Domain.Shared\XJ.Framework.DynamicForm.Domain.Shared.csproj" />
    </ItemGroup>

</Project>
