namespace XJ.Framework.Example.EntityFrameworkCore;

public class ExampleInfrastructureWrapper : InfrastructureWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services.AddDbContext<ExampleDbContext>(
            optionsAction: (serviceProvider, contextOptions) =>
            {
                var env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
                if (env.IsDevelopment())
                {
                    contextOptions.EnableSensitiveDataLogging();
                }

                contextOptions.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            },
            contextLifetime:
            ServiceLifetime.Scoped
        );

        services.AddScoped<IUnitOfWork, UnitOfWork<ExampleDbContext>>();
    }
} 
