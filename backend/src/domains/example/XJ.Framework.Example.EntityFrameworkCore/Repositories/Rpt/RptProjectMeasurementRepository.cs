using XJ.Framework.Example.Domain.Entities.Rpt;
using XJ.Framework.Example.Domain.Repositories.Interfaces.Rpt;

namespace XJ.Framework.Example.EntityFrameworkCore.Repositories.Rpt
{
    /// <summary>
    /// 测量指标 仓储实现
    /// </summary>
    public class RptProjectMeasurementRepository : BaseAuditRepository<ExampleDbContext, long, RptProjectMeasurementEntity>,
       IRptProjectMeasurementRepository
    {
        public RptProjectMeasurementRepository(IServiceProvider serviceProvider) : base(
            serviceProvider)
        {
        }
    }
}
