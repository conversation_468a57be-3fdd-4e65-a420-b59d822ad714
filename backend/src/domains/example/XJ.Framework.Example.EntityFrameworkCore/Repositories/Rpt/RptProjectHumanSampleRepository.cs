using XJ.Framework.Example.Domain.Entities.Rpt;
using XJ.Framework.Example.Domain.Repositories.Interfaces.Rpt;

namespace XJ.Framework.Example.EntityFrameworkCore.Repositories.Rpt;

/// <summary>  
/// AsyncTask 仓储实现  
/// </summary>  
public class RptProjectHumanSampleRepository : BaseAuditRepository<ExampleDbContext, long, RptProjectHumanSampleEntity>,
   IRptProjectHumanSampleRepository
{
    public RptProjectHumanSampleRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
    }

}
