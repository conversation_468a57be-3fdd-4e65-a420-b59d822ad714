namespace XJ.Framework.Example.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// 测量指标 仓储实现
    /// </summary>
    public class ProjectMeasurementRepository : BaseAuditRepository<ExampleDbContext, long, ProjectMeasurementEntity>,
       IProjectMeasurementRepository
    {
        public ProjectMeasurementRepository(IServiceProvider serviceProvider) : base(
            serviceProvider)
        {
        }

        public async Task<bool> DetachAndInsertAsync(List<ProjectMeasurementEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.InsertAsync(entities);
        }

        public async Task<bool> DetachAndDeleteAsync(List<ProjectMeasurementEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.DeleteAsync(entities);
        }
    }
}
