using XJ.Framework.Example.Domain.Entities;

namespace XJ.Framework.Example.Domain.Repositories.Interfaces;

/// <summary>
/// 研究实施地点 仓储接口
/// </summary>
public interface IProjectResearchSiteRepository : IAuditRepository<long, ProjectResearchSiteEntity>
{

    Task<bool> DetachAndInsertAsync(List<ProjectResearchSiteEntity> entities);
    
    Task<bool> DetachAndDeleteAsync(List<ProjectResearchSiteEntity> entities);
}
