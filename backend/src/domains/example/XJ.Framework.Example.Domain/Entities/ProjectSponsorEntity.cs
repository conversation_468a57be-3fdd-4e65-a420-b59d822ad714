using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Example.Domain.Entities
{
    /// <summary>
    /// 试验主办单位
    /// </summary>
    [Table("project_sponsor", Schema = "i")]
    [SoftDeleteIndex("IX_ProjectSponsor_ProjectId", nameof(ProjectId))]
    [SoftDeleteIndex("IX_ProjectSponsor_BusinessId", nameof(BusinessId))]
    [SoftDeleteIndex("IX_ProjectSponsor_Version", nameof(Version))]
    [SoftDeleteIndex("IX_ProjectSponsor_RowIndex", nameof(RowIndex))]
    public class ProjectSponsorEntity : BaseSoftDeleteEntity<long>
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column("project_id")]
        public required long ProjectId { get; set; }

        /// <summary>
        /// 关联业务id
        /// </summary>
        [Column("business_id")]
        public required string BusinessId { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Column("version")]
        [StringLength(20)]
        public required string Version { get; set; }

        /// <summary>
        /// 国家（中文）
        /// </summary>
        [Column("sponsor_country_Zh")]
        [StringLength(100)]
        public string? sponsor_country_zh { get; set; }

        /// <summary>
        /// 国家（英文）
        /// </summary>
        [Column("sponsor_country_En")]
        [StringLength(100)]
        public string? sponsor_country_en { get; set; }

        /// <summary>
        /// 省(直辖市)（中文）
        /// </summary>
        [Column("sponsor_province_Zh")]
        [StringLength(100)]
        public string? sponsor_province_zh { get; set; }

        /// <summary>
        /// 省(直辖市)（英文）
        /// </summary>
        [Column("sponsor_province_En")]
        [StringLength(100)]
        public string? sponsor_province_en { get; set; }

        /// <summary>
        /// 市(区县)（中文）
        /// </summary>
        [Column("sponsor_city_Zh")]
        [StringLength(100)]
        public string? sponsor_city_zh { get; set; }

        /// <summary>
        /// 市(区县)（英文）
        /// </summary>
        [Column("sponsor_city_En")]
        [StringLength(100)]
        public string? sponsor_city_en { get; set; }

        /// <summary>
        /// 单位（中文）
        /// </summary>
        [Column("sponsor_institution_Zh")]
        [StringLength(2000)]
        public string? sponsor_institution_zh { get; set; }

        /// <summary>
        /// 单位（英文）
        /// </summary>
        [Column("sponsor_institution_En")]
        [StringLength(2000)]
        public string? sponsor_institution_en { get; set; }

        /// <summary>
        /// 具体地址（中文）
        /// </summary>
        [Column("sponsor_address_Zh")]
        [StringLength(2000)]
        public string? sponsor_address_zh { get; set; }

        /// <summary>
        /// 具体地址（英文）
        /// </summary>
        [Column("sponsor_address_En")]
        [StringLength(2000)]
        public string? sponsor_address_en { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Column("row_index")]
        public int? RowIndex { get; set; }
    }
}
