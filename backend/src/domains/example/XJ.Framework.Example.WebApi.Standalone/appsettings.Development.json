{
    //以下Default节点 可以统一设置 所有模块使用的数据库连接和接口地址 但关于Endpoint有例外 必须是注入的模块才可以匹配 否则依然从Endpoint节点中获取地址
//    "Default": {
//        "Endpoint": {
//            "Url": "http://localhost:9999"
//        },
//        "Database": {
//            "ConnectionString": "Server=**********;Database=itmctr;User Id=sa;Password=************;TrustServerCertificate=True;Encrypt=False"
//        }
//    },
    "UnicomOptions": {
        "EnableAuthorization": true,
        "Enable": true
    },
    "DownloadJwt": {
        "TokenExpiration": 10,
        "Key": "XJ.Framework.File.Security.Key_2024",
        "Issuer": "XJ.Framework.File",
        "Audience": "XJ.Framework.File.Client"
    },
    "Jwt": {
        "Key": "XJ.Framework.Rbac.Security.Key_2024",
        "Issuer": "XJ.Framework.Rbac",
        "Audience": "XJ.Framework.Rbac.Client",
        "AccessTokenExpirationMinutes": 30,
        "RefreshTokenExpirationDays": 7,
        "MaxRefreshTokensPerUser": 5,
        "ClockSkewMinutes": 5
    }
} 
