<Project Sdk="Microsoft.NET.Sdk.Web">


    
    <Import Project="..\..\..\..\Common.props"/>
    <Import Project="..\..\..\..\Common.Secrets.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.WebApi\XJ.Framework.Library.WebApi.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Modular\XJ.Framework.Library.Modular.csproj" />

        <!-- Module References -->
        <ProjectReference Include="..\..\rbac\XJ.Framework.Rbac.Module\XJ.Framework.Rbac.Module.csproj" />
        <ProjectReference Include="..\..\files\XJ.Framework.Files.Module\XJ.Framework.Files.Module.csproj" />
        <ProjectReference Include="..\..\messaging\XJ.Framework.Messaging.Module\XJ.Framework.Messaging.Module.csproj" />
        <ProjectReference Include="..\..\dynamic-form\XJ.Framework.DynamicForm.Module\XJ.Framework.DynamicForm.Module.csproj" />
        <ProjectReference Include="..\..\logging\XJ.Framework.Logging.Module\XJ.Framework.Logging.Module.csproj" />
        <ProjectReference Include="..\XJ.Framework.Example.Interface\XJ.Framework.Example.Interface.csproj" />
        <ProjectReference Include="..\XJ.Framework.Example.Module\XJ.Framework.Example.Module.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="DockerfileNonBuild">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
