{
    "Serilog": {
        "MinimumLevel": {
            "Default": "Warning",
            "Override": {
//                "XJ.Framework.Library.Modular.Services.ModuleTestServerFactory": "Debug",
//                "XJ.Framework.Library.Modular.Handlers.InMemoryHttpHandlerV8Enhanced": "Debug",
//                "XJ.Framework.Library.Modular.Middlewares.TestServerPerformanceMiddleware": "Debug",
//                "XJ.Framework.Library.Modular.Services.RequestChainPerformanceAnalyzer":"Debug",
//                "XJ.Framework.Library.Modular.Middlewares.RequestPipelinePerformanceMiddleware":"Debug",
//                "XJ.Framework.Library.Modular.Middlewares.TestServerInternalMonitoringMiddleware":"Debug",
                "Microsoft.EntityFrameworkCore.Database.Command": "Debug"
            }
        }
    },
    "Endpoint": [
        {
            "Name": "Rbac",
            "Url": "http://example-webapi:8080/rbac"
        },
        {
            "Name": "RbacMgt",
            "Url": "http://example-webapi-mgt:8080/rbac-mgt"
        },
        {
            "Name": "Files",
            "Url": "http://example-webapi:8080/files"
        },
        {
            "Name": "FilesMgt",
            "Url": "http://example-webapi-mgt:8080/files-mgt"
        },
        {
            "Name": "DynamicForm",
            "Url": "http://example-webapi:8080/dynamic-form"
        },
        {
            "Name": "DynamicFormMgt",
            "Url": "http://example-webapi-mgt:8080/dynamic-form-mgt"
        },
        {
            "Name": "Messaging",
            "Url": "http://example-webapi:8080/messaging"
        },
        {
            "Name": "MessagingMgt",
            "Url": "http://example-webapi-mgt:8080/messaging-mgt"
        },
        {
            "Name": "Logging",
            "Url": "http://example-webapi:8080/logging"
        },
        {
            "Name": "LoggingMgt",
            "Url": "http://example-webapi-mgt:8080/logging-mgt"
        }
    ],
    "DownloadJwt": {
        "TokenExpiration": 10,
        "Key": "XJ.Framework.File.Security.Key_2024",
        "Issuer": "XJ.Framework.File",
        "Audience": "XJ.Framework.File.Client"
    },
    "Jwt": {
        "Key": "XJ.Framework.Rbac.Security.Key_2024",
        "Issuer": "XJ.Framework.Rbac",
        "Audience": "XJ.Framework.Rbac.Client",
        "AccessTokenExpirationMinutes": 720,
        "RefreshTokenExpirationDays": 7,
        "MaxRefreshTokensPerUser": 5,
        "ClockSkewMinutes": 5
    },
    "UnicomOptions": {
        "EnableAuthorization": false,
        "Enable": false
    }
} 
