using XJ.Framework.Example.Domain.Shared.Consts;
using XJ.Framework.Example.Interface.Mgt;
using XJ.Framework.Example.Module;
using XJ.Framework.Library.Application.Services;
using XJ.Framework.Library.Interface.Services;
using XJ.Framework.Library.WebApi.Extensions;

// 使用聚合的WebApiMgtWrapper启动管理端应用
await WebApplication.CreateBuilder(args)
    .Init<Program, WebApiAuthProvider, WebApiAuthInfoGetter, ExampleWebApiMgtWrapper, ExampleInterfaceMgtWrapper>(
        MgtModuleConst.ApplicationCode, "")
    .RunAsync();


