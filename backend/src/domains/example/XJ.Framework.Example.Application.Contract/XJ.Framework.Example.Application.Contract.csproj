<Project Sdk="Microsoft.NET.Sdk">


    
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
      <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj" />
      <ProjectReference Include="..\..\dynamic-form\XJ.Framework.DynamicForm.Application.Contract\XJ.Framework.DynamicForm.Application.Contract.csproj" />
      <ProjectReference Include="..\XJ.Framework.Example.Domain.Shared\XJ.Framework.Example.Domain.Shared.csproj" />
      <ProjectReference Include="..\XJ.Framework.Example.Domain\XJ.Framework.Example.Domain.csproj" />
    </ItemGroup>

</Project>
