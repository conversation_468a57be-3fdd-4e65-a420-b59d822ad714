using XJ.Framework.Example.Application.Contract.OperationDtos;
using XJ.Framework.Example.Application.Contract.QueryCriteria;
using XJ.Framework.Example.Domain.Shared.Enums;

namespace XJ.Framework.Example.Application.Contract.Interfaces;

public interface IAsyncTaskService :
    IAppService<long, AsyncTaskDto, AsyncTaskQueryCriteria>,
    IEditableAppService<long, AsyncTaskOperationDto>
{
    Task<long> CreateTaskAsync(string businessId, string taskCode, object taskData, long? applyUserId = null);

    Task<long> CreateTaskAsync(string businessId, string taskCode, object taskData, object taskResult,
        AsyncTaskStatus? taskStatus = null, long? applyUserId = null);

    Task<long> CreateProcessingTaskAsync(string businessId, string taskCode, object taskData);

    Task<bool> ProcessTaskAsync(long id);
    Task<bool> ProcessTaskAsync(string businessId, string taskCode);

    Task<bool> CompletedTaskByBusinessIdAsync(string businessId, string taskCode, object taskResult);

    Task<bool> CompletedTaskAsync(long id, object taskResult);

    Task<AsyncTaskDto?> GetTaskByBusinessIdAsync(string businessId, string taskCode);


    Task<AsyncTaskDto?> GetLastPendingTaskByBusinessIdAsync(string businessId, string taskCode);
}
