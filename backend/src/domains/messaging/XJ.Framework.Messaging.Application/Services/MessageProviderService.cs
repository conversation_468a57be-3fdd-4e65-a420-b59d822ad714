using Microsoft.Extensions.Logging;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageProvider 服务实现
/// </summary>
public sealed class MessageProviderService :
    BaseEditableAppService<long, MessageProviderEntity, MessageProviderDto, MessageProviderOperationDto,
        IMessageProviderRepository, MessageProviderQueryCriteria>,
    IMessageProviderService
{
    private readonly ILogger<MessageProviderService> _logger;
    private readonly IMessageProviderFactory _messageProviderFactory;

    public MessageProviderService(
        IMessageProviderRepository repository,
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext,
        ILogger<MessageProviderService> logger, IMessageProviderFactory messageProviderFactory)
        : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
        _messageProviderFactory = messageProviderFactory;
    }

    /// <summary>
    /// 根据服务商编码获取服务商
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>服务商信息</returns>
    public async Task<MessageProviderDto?> GetByCodeAsync(string providerCode)
    {
        if (string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("服务商编码不能为空");
            return null;
        }

        var entity = await Repository.GetByCodeAsync(providerCode);
        return entity == null ? null : Mapper.Map<MessageProviderDto>(entity);
    }

    /// <summary>
    /// 根据服务商类型获取服务商列表
    /// </summary>
    /// <param name="providerType">服务商类型</param>
    /// <returns>服务商列表</returns>
    public async Task<List<MessageProviderDto>> GetByTypeAsync(string providerType)
    {
        if (string.IsNullOrWhiteSpace(providerType))
        {
            _logger.LogWarning("服务商类型不能为空");
            return new List<MessageProviderDto>();
        }

        var entities = await Repository.GetByTypeAsync(providerType);
        return Mapper.Map<List<MessageProviderDto>>(entities);
    }

    /// <summary>
    /// 启用/禁用服务商
    /// </summary>
    /// <param name="id">服务商ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        try
        {
            var entity = await Repository.GetAsync(x => x.Key.Equals(id));
            if (entity == null)
            {
                _logger.LogWarning("服务商不存在，ID：{ProviderId}", id);
                return false;
            }

            entity.IsEnabled = isEnabled;
            var result = await Repository.UpdateAsync(entity);

            _logger.LogInformation("服务商状态更新{Status}，ID：{ProviderId}，编码：{ProviderCode}",
                isEnabled ? "启用" : "禁用", id, entity.ProviderCode);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新服务商状态失败，ID：{ProviderId}", id);
            return false;
        }
    }

    /// <summary>
    /// 获取所有启用的服务商
    /// </summary>
    /// <returns>启用的服务商列表</returns>
    public async Task<List<MessageProviderDto>> GetEnabledProvidersAsync()
    {
        try
        {
            var entities = await Repository.GetEnabledProvidersAsync();
            return Mapper.Map<List<MessageProviderDto>>(entities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取启用的服务商列表失败");
            return new List<MessageProviderDto>();
        }
    }

    public async Task<Dictionary<string, List<string>>> GetInstanceNamesByTypeAsync()
    {
        return await Task.FromResult(_messageProviderFactory.GetProviderNames());
    }
}
