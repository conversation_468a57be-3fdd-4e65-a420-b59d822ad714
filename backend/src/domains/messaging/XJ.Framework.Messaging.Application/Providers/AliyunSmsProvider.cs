using Microsoft.Extensions.Logging;
using System.Text.Json;
using XJ.Framework.Messaging.Domain.Shared.Enums;

namespace XJ.Framework.Messaging.Application.Providers;

/// <summary>
/// 阿里云短信配置
/// </summary>
public class AliyunSmsConfig
{
    /// <summary>
    /// AccessKey ID
    /// </summary>
    public string ACCESS_KEY_ID { get; set; } = string.Empty;

    /// <summary>
    /// AccessKey Secret
    /// </summary>
    public string ACCESS_KEY_SECRET { get; set; } = string.Empty;

    /// <summary>
    /// 短信签名
    /// </summary>
    public string SIGN_NAME { get; set; } = string.Empty;

    /// <summary>
    /// 地域节点（默认cn-hangzhou）
    /// </summary>
    public string REGION_ID { get; set; } = "cn-hangzhou";

    /// <summary>
    /// 请求超时时间（毫秒）
    /// </summary>
    public int TIME_OUT { get; set; } = 30000;

    /// <summary>
    /// API地址（默认为阿里云短信API地址）
    /// </summary>
    public string API_URL { get; set; } = "https://dysmsapi.aliyuncs.com/";

    /// <summary>
    /// 验证配置是否完整
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(ACCESS_KEY_ID) &&
               !string.IsNullOrWhiteSpace(ACCESS_KEY_SECRET) &&
               !string.IsNullOrWhiteSpace(SIGN_NAME) &&
               !string.IsNullOrWhiteSpace(API_URL);
    }
}

/// <summary>
/// 阿里云短信API响应
/// </summary>
public class AliyunSmsResponse
{
    /// <summary>
    /// 响应码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 请求ID
    /// </summary>
    public string? RequestId { get; set; }

    /// <summary>
    /// 发送回执ID
    /// </summary>
    public string? BizId { get; set; }
}

/// <summary>
/// 阿里云短信发送结果
/// </summary>
public class AliyunSmsResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 响应码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 发送回执ID
    /// </summary>
    public string? BizId { get; set; }
}

/// <summary>
/// 阿里云SMS消息发送Provider
/// </summary>
public class AliyunSmsProvider : IMessageProvider
{
    private readonly ILogger<AliyunSmsProvider> _logger;
    private readonly HttpClient _httpClient;

    public AliyunSmsProvider(ILogger<AliyunSmsProvider> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    /// <summary>
    /// Provider支持的消息类型
    /// </summary>
    public MessageType SupportedMessageType => MessageType.Sms;

    /// <summary>
    /// Provider实例名称
    /// </summary>
    public string ProviderInstanceName => "AliyunSms";

    /// <summary>
    /// 发送短信（阿里云短信实现）
    /// </summary>
    /// <param name="target">手机号</param>
    /// <param name="templateTitle">模板标题（短信中不使用）</param>
    /// <param name="templateContent">模板编码（阿里云短信模板编码）</param>
    /// <param name="variablesJson">模板变量JSON</param>
    /// <param name="providerConfig">服务商配置</param>
    /// <returns>发送结果</returns>
    public async Task<MessageProviderResult> SendAsync(string target, string? templateTitle,
        string templateContent, string? variablesJson = null, string? providerConfig = null)
    {
        try
        {
            _logger.LogInformation("开始发送阿里云短信，目标：{Target}，模板编码：{TemplateCode}", target, templateContent);

            // 验证手机号格式
            if (!ValidateTarget(target))
            {
                return MessageProviderResult.CreateFailure("无效的手机号格式");
            }

            // 解析和验证配置
            var aliyunConfig = ParseConfig(providerConfig);
            if (aliyunConfig == null || !aliyunConfig.IsValid())
            {
                return MessageProviderResult.CreateFailure("无效的阿里云短信配置");
            }

            // 验证模板编码
            if (string.IsNullOrWhiteSpace(templateContent))
            {
                return MessageProviderResult.CreateFailure("阿里云短信模板编码不能为空");
            }

            // 发送短信
            var providerMessageId = await SendAliyunSmsAsync(target, templateContent, variablesJson, aliyunConfig);

            _logger.LogInformation("阿里云短信发送成功，目标：{Target}，服务商消息ID：{ProviderMessageId}", target, providerMessageId);

            return MessageProviderResult.CreateSuccess(providerMessageId, "短信发送成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "阿里云短信发送失败，目标：{Target}", target);
            return MessageProviderResult.CreateFailure($"短信发送异常：{ex.Message}");
        }
    }



    /// <summary>
    /// 发送阿里云短信
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="templateCode">模板编码</param>
    /// <param name="variablesJson">模板变量JSON</param>
    /// <param name="config">阿里云配置</param>
    /// <returns>服务商消息ID</returns>
    private async Task<string> SendAliyunSmsAsync(string phoneNumber, string templateCode,
        string? variablesJson, AliyunSmsConfig config)
    {
        try
        {
            _logger.LogDebug("准备发送阿里云短信，手机号：{PhoneNumber}，模板：{TemplateCode}，签名：{SignName}",
                phoneNumber, templateCode, config.SIGN_NAME);

            // 解析模板参数
            var templateParam = string.Empty;
            if (!string.IsNullOrEmpty(variablesJson))
            {
                templateParam = ConvertVariablesToAliyunFormat(variablesJson);
            }

            // 构建请求参数
            var requestParams = BuildAliyunSmsRequest(phoneNumber, templateCode, templateParam, config);

            // 发送HTTP请求（使用CancellationToken控制超时）
            var response = await SendAliyunHttpRequestAsync(requestParams, config);

            // 解析响应
            var result = ParseAliyunResponse(response);

            if (result.Success)
            {
                _logger.LogInformation("阿里云短信发送成功，BizId：{BizId}", result.BizId);
                return result.BizId ?? Guid.NewGuid().ToString("N");
            }
            else
            {
                _logger.LogError("阿里云短信发送失败，错误码：{Code}，错误信息：{Message}",
                    result.Code, result.Message);
                throw new InvalidOperationException($"阿里云短信发送失败：{result.Code} - {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "调用阿里云短信API失败");
            throw new InvalidOperationException($"阿里云短信发送失败：{ex.Message}", ex);
        }
    }

    /// <summary>
    /// 构建阿里云短信请求参数
    /// </summary>
    private Dictionary<string, string> BuildAliyunSmsRequest(string phoneNumber, string templateCode,
        string templateParam, AliyunSmsConfig config)
    {
        // 使用ISO 8601格式的UTC时间戳
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
        var nonce = Guid.NewGuid().ToString("N");

        var parameters = new Dictionary<string, string>
        {
            ["Action"] = "SendSms",
            ["Version"] = "2017-05-25",
            ["RegionId"] = config.REGION_ID,
            ["PhoneNumbers"] = phoneNumber,
            ["SignName"] = config.SIGN_NAME,
            ["TemplateCode"] = templateCode,
            ["AccessKeyId"] = config.ACCESS_KEY_ID,
            ["SignatureMethod"] = "HMAC-SHA1",
            ["Timestamp"] = timestamp,
            ["SignatureVersion"] = "1.0",
            ["SignatureNonce"] = nonce,
            ["Format"] = "JSON"
        };

        if (!string.IsNullOrEmpty(templateParam))
        {
            parameters["TemplateParam"] = templateParam;
        }

        _logger.LogDebug("阿里云短信请求参数：{Parameters}",
            string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}")));

        // 计算签名
        var signature = CalculateSignature(parameters, config.ACCESS_KEY_SECRET);
        parameters["Signature"] = signature;

        _logger.LogDebug("计算得到的签名：{Signature}", signature);

        return parameters;
    }

    /// <summary>
    /// 发送HTTP请求到阿里云
    /// </summary>
    private async Task<string> SendAliyunHttpRequestAsync(Dictionary<string, string> parameters, AliyunSmsConfig config)
    {
        // 使用配置中的API地址
        var apiUrl = config.API_URL.TrimEnd('/') + "/";

        // 构建查询字符串，使用与签名相同的编码方法
        var queryString = string.Join("&", parameters.Select(p => $"{PercentEncode(p.Key)}={PercentEncode(p.Value)}"));
        var requestUrl = $"{apiUrl}?{queryString}";

        _logger.LogDebug("发送阿里云短信HTTP请求：{Url}", requestUrl);

        // 使用CancellationToken控制单个请求的超时时间，避免并发冲突
        using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(config.TIME_OUT));

        try
        {
            var response = await _httpClient.GetAsync(requestUrl, cts.Token);
            response.EnsureSuccessStatusCode(); // 确保HTTP状态码成功

            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogDebug("阿里云短信API响应：{Response}", responseContent);

            return responseContent;
        }
        catch (OperationCanceledException) when (cts.Token.IsCancellationRequested)
        {
            throw new TimeoutException($"阿里云短信API请求超时，超时时间：{config.TIME_OUT}ms");
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "阿里云短信API HTTP请求异常");
            throw new InvalidOperationException($"阿里云短信API请求失败：{ex.Message}", ex);
        }
    }

    /// <summary>
    /// 计算阿里云API签名
    /// </summary>
    private string CalculateSignature(Dictionary<string, string> parameters, string accessKeySecret)
    {
        // 排序参数（排除Signature参数）
        var sortedParams = parameters
            .Where(p => p.Key != "Signature")
            .OrderBy(p => p.Key, StringComparer.Ordinal)
            .ToList();

        // 构建规范化查询字符串
        var canonicalizedQueryString = string.Join("&",
            sortedParams.Select(p => $"{PercentEncode(p.Key)}={PercentEncode(p.Value)}"));

        // 构建待签名字符串
        var stringToSign = $"GET&{PercentEncode("/")}&{PercentEncode(canonicalizedQueryString)}";

        _logger.LogDebug("阿里云签名字符串：{StringToSign}", stringToSign);

        // 计算HMAC-SHA1签名
        var key = System.Text.Encoding.UTF8.GetBytes($"{accessKeySecret}&");
        using var hmac = new System.Security.Cryptography.HMACSHA1(key);
        var signatureBytes = hmac.ComputeHash(System.Text.Encoding.UTF8.GetBytes(stringToSign));
        return Convert.ToBase64String(signatureBytes);
    }

    /// <summary>
    /// 阿里云API专用的URL编码方法
    /// </summary>
    private static string PercentEncode(string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        var encoded = Uri.EscapeDataString(value);

        // 阿里云API要求的特殊编码规则
        encoded = encoded.Replace("+", "%20")
                        .Replace("*", "%2A")
                        .Replace("%7E", "~");

        return encoded;
    }

    /// <summary>
    /// 解析阿里云响应
    /// </summary>
    private AliyunSmsResult ParseAliyunResponse(string responseContent)
    {
        try
        {
            var response = JsonSerializer.Deserialize<AliyunSmsResponse>(responseContent);

            return new AliyunSmsResult
            {
                Success = response?.Code == "OK",
                Code = response?.Code,
                Message = response?.Message,
                BizId = response?.BizId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析阿里云短信响应失败：{Response}", responseContent);
            return new AliyunSmsResult
            {
                Success = false,
                Code = "PARSE_ERROR",
                Message = $"响应解析失败：{ex.Message}"
            };
        }
    }

    /// <summary>
    /// 将变量JSON转换为阿里云短信API需要的格式
    /// </summary>
    /// <param name="variablesJson">变量JSON</param>
    /// <returns>阿里云格式的模板参数</returns>
    private string ConvertVariablesToAliyunFormat(string variablesJson)
    {
        try
        {
            // 阿里云短信API需要的是JSON格式的模板参数
            // 例如：{"code":"123456","name":"张三"}
            var variables = JsonSerializer.Deserialize<Dictionary<string, object>>(variablesJson);
            if (variables == null || !variables.Any())
            {
                return "{}";
            }

            // 转换为阿里云需要的格式（通常就是原始的JSON格式）
            return JsonSerializer.Serialize(variables);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换模板变量格式失败：{VariablesJson}", variablesJson);
            return "{}";
        }
    }

    /// <summary>
    /// 解析阿里云短信配置
    /// </summary>
    /// <param name="providerConfig">配置JSON字符串</param>
    /// <returns>阿里云短信配置对象</returns>
    private AliyunSmsConfig? ParseConfig(string? providerConfig)
    {
        if (string.IsNullOrWhiteSpace(providerConfig))
        {
            _logger.LogWarning("阿里云短信配置为空");
            return null;
        }

        try
        {
            var config = JsonSerializer.Deserialize<AliyunSmsConfig>(providerConfig, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (config == null)
            {
                _logger.LogWarning("阿里云短信配置解析结果为null");
                return null;
            }

            _logger.LogDebug("阿里云短信配置解析成功，签名：{SignName}，地域：{RegionId}",
                config.SIGN_NAME, config.REGION_ID);

            return config;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "阿里云短信配置JSON解析失败：{Config}", providerConfig);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析阿里云短信配置时发生异常");
            return null;
        }
    }

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <param name="providerConfig">服务商配置</param>
    /// <returns>是否有效</returns>
    public bool ValidateConfig(string? providerConfig)
    {
        var config = ParseConfig(providerConfig);
        return config?.IsValid() == true;
    }

    /// <summary>
    /// 验证手机号格式
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <returns>是否有效</returns>
    public bool ValidateTarget(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // 支持中国大陆手机号（11位，以1开头）
        if (phoneNumber.Length == 11 && phoneNumber.StartsWith("1") && phoneNumber.All(char.IsDigit))
        {
            return true;
        }

        // 支持国际手机号格式（带国家代码）
        if (phoneNumber.StartsWith("+") && phoneNumber.Length > 10 && phoneNumber.Length <= 15)
        {
            return phoneNumber.Substring(1).All(char.IsDigit);
        }

        return false;
    }
}
