using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;
using XJ.Framework.Messaging.Application.Contract.Interfaces;
using XJ.Framework.Messaging.Application.Contract.OperationDtos;
using XJ.Framework.Messaging.Application.Contract.QueryCriteria;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Interface.Mgt.Controllers;

/// <summary>
/// MessageAccountUsage 控制器
/// </summary>
// [NameController(Domain.Shared.Consts.MgtModuleConst.ApplicationCode, Domain.Shared.Consts.MgtModuleConst.ApplicationName, Domain.Shared.Consts.MgtModuleConst.ModuleId, Domain.Shared.Consts.MgtModuleConst.ModuleName)]
public class MessageAccountUsageController : BaseEditableAppController<long, MessageAccountUsageDto,
    MessageAccountUsageOperationDto, IMessageAccountUsageService, MessageAccountUsageQueryCriteria>
{
    public MessageAccountUsageController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountUsageDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<MessageAccountUsageQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }
}
