namespace XJ.Framework.Messaging.EntityFrameworkCore;

public class MessagingInfrastructureWrapper : InfrastructureWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services.AddDbContext<MessagingDbContext>(
            optionsAction: (serviceProvider, contextOptions) =>
            {
                var env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
                if (env.IsDevelopment())
                {
                    contextOptions.EnableSensitiveDataLogging();
                }

                contextOptions.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            },
            contextLifetime:
            ServiceLifetime.Scoped
        );

        services.AddScoped<IUnitOfWork, UnitOfWork<MessagingDbContext>>();
    }
} 