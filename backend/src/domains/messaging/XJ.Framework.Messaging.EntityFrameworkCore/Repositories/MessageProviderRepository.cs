
namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;


/// <summary>
/// MessageProvider 仓储实现
/// </summary>
public class MessageProviderRepository : BaseSoftDeleteRepository<MessagingDbContext, long, MessageProviderEntity>, IMessageProviderRepository
{
    public MessageProviderRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 根据服务商编码获取服务商
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>服务商实体</returns>
    public async Task<MessageProviderEntity?> GetByCodeAsync(string providerCode)
    {
        return await DbSet
            .FirstOrDefaultAsync(x => x.ProviderCode == providerCode && !x.Deleted);
    }

    /// <summary>
    /// 根据服务商类型获取服务商列表
    /// </summary>
    /// <param name="providerType">服务商类型</param>
    /// <returns>服务商列表</returns>
    public async Task<List<MessageProviderEntity>> GetByTypeAsync(string providerType)
    {
        return await DbSet
            .Where(x => x.ProviderType == providerType && !x.Deleted)
            .OrderBy(x => x.ProviderCode)
            .ToListAsync();
    }

    /// <summary>
    /// 获取所有启用的服务商
    /// </summary>
    /// <returns>启用的服务商列表</returns>
    public async Task<List<MessageProviderEntity>> GetEnabledProvidersAsync()
    {
        return await DbSet
            .Where(x => x.IsEnabled && !x.Deleted)
            .OrderBy(x => x.ProviderType)
            .ThenBy(x => x.ProviderCode)
            .ToListAsync();
    }
}
