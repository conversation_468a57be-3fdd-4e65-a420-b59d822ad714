namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// MessageProvider DTO
/// </summary>
public class MessageProviderDto : BaseDto<long>
{
    /// <summary>
    /// 服务商编码
    /// </summary>
    public string ProviderCode { get; set; } = null!;

    /// <summary>
    /// 服务商名称
    /// </summary>
    public string ProviderName { get; set; } = null!;

    /// <summary>
    /// 服务商类型
    /// </summary>
    public string ProviderType { get; set; } = null!;

    /// <summary>
    /// 服务商实例
    /// </summary>
    public string ProviderInstance { get; set; } = null!;

    /// <summary>
    /// 服务商配置
    /// </summary>
    public string Config { get; set; } = null!;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    public DateTimeOffset CreatedTime { get; set; }
}
