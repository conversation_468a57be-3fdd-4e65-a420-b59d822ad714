namespace XJ.Framework.Messaging.Domain.Repositories.Interfaces;

/// <summary>
/// MessageAccount 仓储接口
/// </summary>
public interface IMessageAccountRepository : IAuditRepository<long, MessageAccountEntity>
{
    /// <summary>
    /// 根据账户编码获取账户
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户实体</returns>
    Task<MessageAccountEntity?> GetByCodeAsync(string accountCode);

    /// <summary>
    /// 根据应用编码获取账户列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>账户列表</returns>
    Task<MessageAccountEntity?> GetByAppCodeAsync(string appCode);
}
