
namespace XJ.Framework.Messaging.Domain.Repositories.Interfaces;

/// <summary>
/// MessageTemplate 仓储接口
/// </summary>

public interface IMessageTemplateRepository : IAuditRepository<long, MessageTemplateEntity>
{
    /// <summary>
    /// 根据模板编码和应用编码获取模板
    /// </summary>
    /// <param name="templateCode">模板编码</param>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板实体</returns>
    Task<MessageTemplateEntity?> GetByCodeAsync(string templateCode, string appCode);

    /// <summary>
    /// 根据应用编码获取模板列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板列表</returns>
    Task<List<MessageTemplateEntity>> GetByAppCodeAsync(string appCode);
}
