using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageAccountRecharge 实体
/// </summary>
[Table("message_account_recharge", Schema = "m")]
[SoftDeleteIndex("IX_MessageAccountRecharge_AccountProviderId", nameof(AccountProviderId))]
public class MessageAccountRechargeEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 账户-服务商路由ID
    /// </summary>
    [Column("account_provider_id")]
    public required long AccountProviderId { get; set; }

    /// <summary>
    /// 充值数量
    /// </summary>
    [Column("amount")]
    public required int Amount { get; set; }

    /// <summary>
    /// 充值后余额
    /// </summary>
    [Column("balance_after")]
    public int? BalanceAfter { get; set; }

    /// <summary>
    /// 充值时间
    /// </summary>
    [Column("recharge_time")]
    public required DateTimeOffset RechargeTime { get; set; }

    /// <summary>
    /// 操作人
    /// </summary>
    [Column("operator")]
    [StringLength(100)]
    public string? Operator { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [Column("remark")]
    [StringLength(400)]
    public string? Remark { get; set; }

} 
