using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageAccountUsage 实体
/// </summary>
[Table("message_account_usage", Schema = "m")]
[SoftDeleteIndex("IX_account_usage_account_provider", nameof(AccountProviderId))]
[SoftDeleteIndex("IX_account_usage_type_template", nameof(MessageType), nameof(TemplateCode))]
[SoftDeleteIndex("IX_account_usage_target", nameof(Target))]
[SoftDeleteIndex("IX_account_usage_appcode", nameof(AppCode))]
[SoftDeleteIndex("IX_account_usage_provider", nameof(ProviderCode))]
[SoftDeleteIndex("IX_account_usage_template", nameof(TemplateCode))]
public class MessageAccountUsageEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 账户-服务商路由ID
    /// </summary>
    [Column("account_provider_id")]
    public required long AccountProviderId { get; set; }

    /// <summary>
    /// 账户编码
    /// </summary>
    [Column("account_code")]
    [StringLength(100)]
    public required string AccountCode { get; set; } = null!;

    /// <summary>
    /// 应用编码
    /// </summary>
    [Column("app_code")]
    [StringLength(100)]
    public required string AppCode { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    [Column("provider_code")]
    [StringLength(100)]
    public required string ProviderCode { get; set; } = null!;

    /// <summary>
    /// 使用类型
    /// </summary>
    [Column("usage_type")]
    [StringLength(40)]
    public required string UsageType { get; set; } = null!;

    /// <summary>
    /// 消息类型
    /// </summary>
    [Column("message_type")]
    [StringLength(40)]
    public required string MessageType { get; set; } = null!;

    /// <summary>
    /// 模板编码
    /// </summary>
    [Column("template_code")]
    [StringLength(200)]
    public string? TemplateCode { get; set; }

    /// <summary>
    /// 发送目标
    /// </summary>
    [Column("target")]
    [StringLength(400)]
    public string? Target { get; set; }

    /// <summary>
    /// 用量
    /// </summary>
    [Column("amount")]
    public required int Amount { get; set; }

    /// <summary>
    /// 用量后余额
    /// </summary>
    [Column("balance_after")]
    public int? BalanceAfter { get; set; }

    /// <summary>
    /// 用量时间
    /// </summary>
    [Column("usage_time")]
    public required DateTimeOffset UsageTime { get; set; }

    /// <summary>
    /// 关联消息发送ID
    /// </summary>
    [Column("message_send_id")]
    public long? MessageSendId { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [Column("remark")]
    [StringLength(400)]
    public string? Remark { get; set; }
}
