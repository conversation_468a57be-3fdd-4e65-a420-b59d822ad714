using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageSend 实体
/// </summary>
[Table("message_send", Schema = "m")]
[SoftDeleteIndex("IX_message_send_status_trytimes", nameof(Status), nameof(TryTime))]
[SoftDeleteIndex("IX_message_send_appcode", nameof(AppCode))]
[SoftDeleteIndex("IX_message_send_template", nameof(TemplateCode))]
public class MessageSendEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 应用编码
    /// </summary>
    [Column("app_code")]
    [StringLength(100)]
    public required string AppCode { get; set; } = null!;

    /// <summary>
    /// 发送目标
    /// </summary>
    [Column("target")]
    [StringLength(400)]
    public required string Target { get; set; } = null!;

    /// <summary>
    /// 模板编码
    /// </summary>
    [Column("template_code")]
    [StringLength(200)]
    public required string TemplateCode { get; set; } = null!;

    /// <summary>
    /// 变量内容
    /// </summary>
    [Column("variables_json")]
    [StringLength(-1)]
    public required string VariablesJson { get; set; } = null!;

    /// <summary>
    /// 优先级
    /// </summary>
    [Column("priority")]
    public required int Priority { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    [Column("message_type")]
    [StringLength(40)]
    public required string MessageType { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    [Column("provider_code")]
    [StringLength(100)]
    public string? ProviderCode { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column("create_time")]
    public required DateTimeOffset CreateTime { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    [Column("send_time")]
    public DateTimeOffset? SendTime { get; set; }

    /// <summary>
    /// 发送状态
    /// </summary>
    [Column("status")]
    public required int Status { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    [Column("try_times")]
    public required int TryTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [Column("error_info")]
    [StringLength(2000)]
    public string? ErrorInfo { get; set; }

    /// <summary>
    /// 关联ID
    /// </summary>
    [Column("correlation_id")]
    [StringLength(200)]
    public string? CorrelationId { get; set; }

    /// <summary>
    /// 关联类型
    /// </summary>
    [Column("correlation_type")]
    [StringLength(100)]
    public string? CorrelationType { get; set; }
}
