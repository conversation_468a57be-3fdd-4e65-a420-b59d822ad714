using Microsoft.AspNetCore.Mvc;
using System.Web;
using XJ.Framework.DynamicForm.Application.Contract.Interfaces;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Consts;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;

namespace XJ.Framework.DynamicForm.Interface.Controllers;

/// <summary>
/// FormInstance 控制器
/// </summary>
// [NameController(ModuleConst.ApplicationCode, ModuleConst.ApplicationName, ModuleConst.ModuleId, ModuleConst.ModuleName)]
public class
    FormInstanceController : BaseAppController<long, FormInstanceDto, IFormInstanceService, FormInstanceQueryCriteria>
{
    
    private readonly IFormFieldInstanceService _formFieldInstanceService;
    private readonly IFormInstanceDataService _formInstanceDataService;

    public FormInstanceController(IServiceProvider serviceProvider, IFormFieldInstanceService formFieldInstanceService,
        IFormInstanceDataService formInstanceDataService)
        : base(serviceProvider)
    {
        _formFieldInstanceService = formFieldInstanceService;
        _formInstanceDataService = formInstanceDataService;
    }


    [PublicPermission]
    [HttpGet("entity/{businessId}")]
    public async Task<FormInstanceDto> GetFormInstanceAsync(string businessId)
    {
        return await Service.GetNewestFormInstanceDtoAsync(businessId);
    }

    [PublicPermission]
    [HttpGet("entity/{businessId}/{version}")]
    public async Task<FormInstanceDto> GetFormInstanceAsync(string businessId, string version)
    {
        return await Service.GetNamedVersionFormInstanceDtoAsync(businessId, version);
    }

    /// <summary>
    /// 使用formCode和业务id创建表单实例（如果有上一版本从上一版本获取表单定义）
    /// </summary>
    /// <param name="formCode"></param>
    /// <param name="businessId"></param>
    /// <returns>表单实例id</returns>
    [PublicPermission]
    [UnitOfWork]
    [HttpPost("instance/{formCode}/{businessId}")]
    public async Task<string> CreateFormInstanceAsync(string formCode, string businessId)
    {
        return await Service.CreateFormInstanceAsync(formCode, businessId);
    }


    /// <summary>
    /// 使用formCode和业务id创建基于最新定义的表单实例
    /// </summary>
    /// <param name="formCode"></param>
    /// <param name="businessId"></param>
    /// <returns>表单实例id</returns>
    [PublicPermission]
    [UnitOfWork]
    [HttpPost("instance/{formCode}/{businessId}/newest")]
    public async Task<string> CreateNewestFormInstanceAsync(string formCode, string businessId)
    {
        return await Service.CreateNewestFormInstanceAsync(formCode, businessId);
    }

    /// <summary>
    /// 使用业务id获取当前表单的最新版本
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [PublicPermission]
    [IgnoreLogging]
    [HttpGet("instance/{businessId}/{version}/newest")]
    public async Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId, string version)
    {
        return await Service.GetNewestFormInstanceAsync(businessId, version);
    }


    /// <summary>
    /// 使用业务id获取当前表单的最新版本
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [PublicPermission]
    [IgnoreLogging]
    [HttpGet("instance/{businessId}/newest")]
    public async Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId)
    {
        return await Service.GetNewestFormInstanceAsync(businessId);
    }

    /// <summary>
    /// 通过业务id和版本号获取指定版本的表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [PublicPermission]
    [IgnoreLogging]
    [HttpGet("instance/{businessId}/{version}")]
    public async Task<FormDefinitionDto> GetNamedVersionFormInstanceAsync(string businessId, string version)
    {
        return await Service.GetNamedVersionFormInstanceAsync(businessId, version);
    }


    /// <summary>
    /// 通过业务id和版本号获取指定版本的表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [ApplicationPermission]
    [IgnoreLogging]
    [HttpGet("app/instance/{businessId}/{version}")]
    public async Task<FormDefinitionDto> AppGetNamedVersionFormInstanceAsync(string businessId, string version)
    {
        return await Service.GetNamedVersionFormInstanceAsync(businessId, version);
    }


    /// <summary>
    /// 保存表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission]
    [HttpPut("instance/{businessId}/{version}")]
    public async Task<string> SaveInstanceAsync(string businessId, string version,
        [FromBody] FormDefinitionDto formDefinitionDto)
    {
        return await Service.SaveInstanceAsync(businessId, version, formDefinitionDto);
    }


    [UnitOfWork]
    [PublicPermission]
    [HttpPut("instance/{businessId}")]
    public async Task<string> SaveInstanceAsync(string businessId,
        [FromBody] FormDefinitionDto formDefinitionDto)
    {
        return await Service.SaveInstanceAsync(businessId, formDefinitionDto);
    }


    /// <summary>
    /// 无实例保存表单实例
    /// </summary>
    /// <param name="formCode"></param>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission]
    [HttpPost("instance/{formCode}")]
    public async Task<NewFormInstanceDto> SaveInstanceWithoutVersionAsync(string formCode,
        [FromBody] FormDefinitionDto formDefinitionDto)
    {
        return await Service.SaveInstanceWithoutVersionAsync(formCode, formDefinitionDto);
    }

    [UnitOfWork]
    [PublicPermission]
    [HttpPost("instance/annotations/{businessId}/{version}")]
    public async Task<bool> SetAnnotationsAsync(string businessId, string version,
        [FromBody] Dictionary<string, AnnotationValue?>? annotations)
    {
        var formInstanceDto = await Service.GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await _formFieldInstanceService.SaveAnnotationsWithValidationAsync(formInstanceDto, annotations);
    }

    [UnitOfWork]
    [PublicPermission]
    [HttpPost("instance/annotations/{businessId}")]
    public async Task<bool> SetAnnotationsAsync(string businessId,
        [FromBody] Dictionary<string, AnnotationValue?>? annotations)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);
        return await _formFieldInstanceService.SaveAnnotationsWithValidationAsync(formInstanceDto, annotations);
    }


    [PublicPermission]
    [HttpGet("instance/formData/{businessId}")]
    public async Task<Dictionary<string, string?>> GetFormDataAsync(string businessId)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);

        var result = await _formInstanceDataService.GetByFormInstanceAsync(formInstanceDto);

        return result.ToDictionary(k => k.Code, v => v.Value);
    }

    [UnitOfWork]
    [PublicPermission]
    [HttpPost("instance/formData/{businessId}")]
    public async Task<bool> SetFormDataAsync(string businessId, [FromBody] Dictionary<string, string?> formData)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);

        //if (formInstanceDto.Status != FormInstanceStatus.Draft)
        //{
        //    throw new ValidationException("表单实例状态不允许修改/ The form instance status does not allow modification.");
        //}

        var result = await _formInstanceDataService.SetFormDataAsync(formInstanceDto, formData);

        return result;
    }

    /// <summary>
    /// 取消当前版本表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission]
    [HttpPost("instance/cancel/{businessId}/{version}")]
    public async Task<bool> CancelInstanceAsync(string businessId, string version)
    {
        return await Service.CancelInstanceAsync(businessId, version);
    }


    /// <summary>
    /// 提交当前版本表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission]
    [HttpPost("instance/submit/{businessId}/{version}")]
    public async Task<bool> SubmitInstanceAsync(string businessId, string version)
    {
        return await Service.SubmitInstanceAsync(businessId, version);
    }

    /// <summary>
    /// 提交最新版本表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission]
    [HttpPost("instance/submit/{businessId}")]
    public async Task<bool> SubmitInstanceAsync(string businessId)
    {
        return await Service.SubmitInstanceAsync(businessId);
    }

    /// <summary>
    /// 获取与上一版本的差异
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [PublicPermission]
    [IgnoreLogging]
    [HttpGet("instance/diff/{businessId}/{version}")]
    public async Task<FormDefinitionDto> GetPreviousDifferenceAsync(string businessId, string version)
    {
        return await Service.GetPreviousDifferenceAsync(businessId, version);
    }


    /// <summary>
    /// 获取与上一版本的差异
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [ApplicationPermission]
    [IgnoreLogging]
    [HttpGet("app/instance/diff/{businessId}/{version}")]
    public async Task<FormDefinitionDto> GetAppPreviousDifferenceAsync(string businessId, string version)
    {
        return await Service.GetPreviousDifferenceAsync(businessId, version);
    }

    /// <summary>
    /// 获取与上一版本的差异
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [PublicPermission]
    [IgnoreLogging]
    [HttpGet("instance/diff/{businessId}")]
    public async Task<FormDefinitionDto> GetPreviousDifferenceAsync(string businessId)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);
        return await Service.GetPreviousDifferenceAsync(businessId, formInstanceDto.Version);
    }

    /// <summary>
    /// 获取与指定版本的差异
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <param name="namedVersion"></param>
    /// <returns></returns>
    [PublicPermission]
    [IgnoreLogging]
    [HttpGet("instance/diff/{businessId}/{version}/{namedVersion}")]
    public async Task<FormDefinitionDto> GetPreviousDifferenceAsync(string businessId, string version,
        string namedVersion)
    {
        return await Service.GetNamedVersionDifferenceAsync(businessId, version, namedVersion);
    }

    /// <summary>
    /// 根据业务id获取所有版本的表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [PublicPermission]
    [HttpGet("instance/histories/{businessId}")]
    public async Task<List<FormInstanceDto>> GetHistoriesAsync(string businessId)
    {
        return await Service.GetFormInstanceVersionsAsync(businessId);
    }

    /// <summary>
    /// 获取当前表单验证结果 针对于提交前的验证
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [PublicPermission]
    [HttpGet("instance/validate/{businessId}/{version}")]
    public async Task<List<string>> GetValidateAsync(string businessId, string version)
    {
        return await Service.GetValidateAsync(businessId, version);
    }

    /// <summary>
    /// 获取当前表单验证结果 针对于提交前的验证
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [PublicPermission]
    [HttpGet("instance/validate/{businessId}")]
    public async Task<List<string>> GetValidateAsync(string businessId)
    {
        return await Service.GetValidateAsync(businessId);
    }

    /// <summary>
    /// 分页获取表单实例
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("page")]
    [PublicPermission]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> GetPageAsync(
        [FromQuery] PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await Service.GetUserPageAsync(criteria);
    }

    /// <summary>
    /// 分页获取表单实例最新状态
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("newest-page")]
    [PublicPermission]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> GetNewestPageAsync(
        [FromQuery] PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await Service.GetNewestUserPageAsync(criteria);
    }


    [HttpPost("count/{formCode}")]
    [IgnoreLogging]
    [PublicPermission]
    public async Task<int> GetCountAsync([FromRoute] string formCode, [FromBody] GetFormInstanceCountDto input)
    {
        return await Service.GetUserCountAsync(formCode, input.Status, input.DynamicQueries,
            input.FormDataDynamicQueries);
    }

    [HttpPost("newest-count/{formCode}")]
    [IgnoreLogging]
    [PublicPermission]
    public async Task<int> GetNewestCountAsync([FromRoute] string formCode, [FromBody] GetFormInstanceCountDto input)
    {
        return await Service.GetUserNewestCountAsync(formCode, input.Status, input.DynamicQueries,
            input.FormDataDynamicQueries);
    }


    /// <summary>
    /// 检查业务数据值是否存在
    /// </summary>
    /// <param name="formCode"></param>
    /// <param name="formDataCode"></param>
    /// <param name="formDataValue"></param>
    /// <returns></returns>
    [HttpGet("formData/exist/{formCode}/{formDataCode}/{formDataValue}")]
    [PublicPermission]
    public async Task<bool> ExistFormDataValueAsync(string formCode, string formDataCode, string formDataValue)
    {
        return await _formInstanceDataService.ExistFormDataValueAsync(formCode, formDataCode, formDataValue);
    }

    /// <summary>
    /// 检查业务数据值是否存在
    /// </summary>
    /// <param name="formCode"></param>
    /// <param name="businessId"></param>
    /// <param name="formDataCode"></param>
    /// <param name="formDataValue"></param>
    /// <returns></returns>
    [HttpGet("formData/exist/{formCode}/{businessId}/{formDataCode}/{formDataValue}")]
    [PublicPermission]
    public async Task<bool> ExistFormDataValueAsync(string formCode, string businessId, string formDataCode,
        string formDataValue)
    {
        return await _formInstanceDataService.ExistFormDataValueAsync(formCode, businessId, formDataCode,
            HttpUtility.UrlDecode(formDataValue));
    }
}
