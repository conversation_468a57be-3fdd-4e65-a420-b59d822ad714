using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.Interfaces;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Consts;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;

namespace XJ.Framework.DynamicForm.Interface.Controllers;

/// <summary>
/// Form 控制器
/// </summary>
// [NameController(ModuleConst.ApplicationCode, ModuleConst.ApplicationName, ModuleConst.ModuleId, ModuleConst.ModuleName)]
public class
    FormController : BaseEditableAppController<long, FormDto, FormOperationDto, IFormService, FormQueryCriteria>
{
    private JsonSerializerOptions _jsonSerializerOptions;

    public FormController(IServiceProvider serviceProvider, IOptions<JsonOptions> jsonOptions) : base(serviceProvider)
    {
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
    }

    [AllowAnonymous]
    [HttpGet("definition/{formCode}")]
    [IgnoreLogging]
    public async Task<FormDefinitionDto> GetDefinitionAsync(string formCode)
    {
        return await Service.LoadDefinitionAsync(formCode);
    }
}
