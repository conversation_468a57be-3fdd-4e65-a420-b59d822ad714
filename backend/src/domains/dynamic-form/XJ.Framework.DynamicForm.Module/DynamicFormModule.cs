using Microsoft.AspNetCore.Builder;
using XJ.Framework.DynamicForm.Interface;
using XJ.Framework.DynamicForm.Interface.Mgt;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Modular.Abstractions;

namespace XJ.Framework.DynamicForm.Module;

/// <summary>
/// DynamicForm模块
/// </summary>
public class DynamicFormModule<TEntryProgram, TAuthProvider, TAuthInfoGetter> : BaseModule<TEntryProgram, TAuthProvider,
    TAuthInfoGetter,
    DynamicFormWebApiWrapper,
    DynamicFormInterfaceWrapper>
    where TAuthProvider : class, IAuthProvider
    where TAuthInfoGetter : class, IAuthInfoGetter

{
    public override List<string> ReferencedModuleIds { get; } = [Domain.Shared.Consts.MgtModuleConst.ModuleId];
    public override string ModuleId => Domain.Shared.Consts.ModuleConst.ModuleId;
    public override string ModuleName => Domain.Shared.Consts.ModuleConst.ModuleName;
    public override string Version => "1.0.0";
    public override string ApplicationCode => Domain.Shared.Consts.ModuleConst.ApplicationCode;
    public override string RoutePrefix => "dynamic-form";


    public override void ConfigureApplication(WebApplication app)
    {
        // DynamicForm模块特定的中间件配置（如果需要）
    }
}

/// <summary>
/// DynamicForm管理模块
/// </summary>
public class DynamicFormMgtModule<TEntryProgram, TAuthProvider, TAuthInfoGetter> : BaseModule<TEntryProgram,
    TAuthProvider, TAuthInfoGetter,
    DynamicFormWebApiMgtWrapper,
    DynamicFormMgtInterfaceWrapper>
    where TAuthProvider : class, IAuthProvider
    where TAuthInfoGetter : class, IAuthInfoGetter
{
    public override List<string> ReferencedModuleIds { get; } = [Domain.Shared.Consts.ModuleConst.ModuleId];
    public override string ModuleId => Domain.Shared.Consts.MgtModuleConst.ModuleId;
    public override string ModuleName => Domain.Shared.Consts.MgtModuleConst.ModuleName;
    public override string ApplicationCode => Domain.Shared.Consts.MgtModuleConst.ApplicationCode;
    public override string Version => "1.0.0";
    public override string RoutePrefix => "dynamic-form-mgt";


    public override void ConfigureApplication(WebApplication app)
    {
        // DynamicForm管理模块特定的中间件配置（如果需要）
    }
}
