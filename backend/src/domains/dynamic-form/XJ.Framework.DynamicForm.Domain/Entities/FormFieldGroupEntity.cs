using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.DynamicForm.Domain.Entities;

/// <summary>
/// FormFieldGroup 实体
/// </summary>
[Table("form_field_groups", Schema = "d")]
[SoftDeleteIndex("IX_form_field_groups_form_code_version_code_not_deleted", nameof(FormCode), nameof(Code),
    nameof(Version), IsUnique = true)]
public class FormFieldGroupEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 表单ID
    /// </summary>
    [Column("form_code")]
    [StringLength(50)]
    public required string FormCode { get; set; }

    /// <summary>
    /// 表单版本
    /// </summary>
    [Column("version")]
    [StringLength(50)]
    public required string Version { get; set; }

    /// <summary>
    /// 分组编码
    /// </summary>
    [Column("code")]
    [StringLength(50)]
    public required string Code { get; set; } = null!;

    /// <summary>
    /// 中文标题
    /// </summary>
    [Column("title_zh")]
    [StringLength(400)]
    public string? TitleZh { get; set; }

    /// <summary>
    /// 英文标题
    /// </summary>
    [Column("title_en")]
    [StringLength(400)]
    public string? TitleEn { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [Column("sort_order")]
    public required int SortOrder { get; set; }
}
