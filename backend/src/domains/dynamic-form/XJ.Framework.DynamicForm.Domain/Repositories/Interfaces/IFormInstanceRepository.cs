using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.Domain.Repositories.Interfaces;

/// <summary>
/// FormInstance 仓储接口
/// </summary>
public interface IFormInstanceRepository : IAuditRepository<long, FormInstanceEntity>
{
    Task<PageData<long, FormInstanceEntity>> GetPageRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, DynamicFormQueryDto> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        bool isNewest);

    Task<int> GetCountRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, DynamicFormQueryDto> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        bool isNewest = true);

}
