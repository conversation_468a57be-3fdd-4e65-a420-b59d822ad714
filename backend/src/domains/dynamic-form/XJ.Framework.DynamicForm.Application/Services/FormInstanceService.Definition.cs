using XJ.Framework.DynamicForm.Application.Contract.Services;

namespace XJ.Framework.DynamicForm.Application.Services;

public partial class FormInstanceService
{
    public async Task<FormDefinitionDto> GetCurrentInstanceDefinitionAsync(FormInstanceDto formInstance,
        bool forceDefine = false)
    {
        var definition = forceDefine
            ? await _formService.LoadDefinitionAsync(formInstance.FormCode)
            : await _formService.LoadDefinitionAsync(formInstance.FormCode, formInstance.FormVersion);

        definition.Version = formInstance.Version;

        definition.Language = formInstance.Language;

        var formDataList = await _formInstanceDataService.GetByFormInstanceAsync(formInstance);

        definition.FormData = formDataList.ToDictionary(k => k.Code, v => v.Value);

        var formFieldInstanceDtos = await _formFieldInstanceService.LoadListAsync(formInstance);

        await FormDefinitionHelper.SetFieldValueAsync(definition, await GetFieldValuesAsync(formFieldInstanceDtos));

        // var warnings = await _formFieldInstanceService.GetWarningsAsync(formInstance);

        // await FormDefinitionHelper.SetWarningsAsync(definition, warnings);

        var annotations = await _formFieldInstanceService.BuildAnnotationsAsync(formFieldInstanceDtos);

        await FormDefinitionHelper.SetAnnotationsAsync(definition, annotations);

        return definition;
    }


    public async Task<FormDefinitionDto> GetNamedVersionDifferenceAsync(FormInstanceDto sourceFormInstance,
        string? version)
    {
        FormDefinitionDto? target = null;
        if (version != null)
        {
            var targetFormInstance = await GetNamedInstanceAsync(sourceFormInstance.BusinessId, version);
            if (targetFormInstance != null)
            {
                target = await GetCurrentInstanceDefinitionAsync(targetFormInstance);
            }
        }

        var source = await GetCurrentInstanceDefinitionAsync(sourceFormInstance);

        await CompareDifferenceAsync(source, target);

        return source;
    }

    public async Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId, string version)
    {
        var formInstance = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await GetCurrentInstanceDefinitionAsync(formInstance, true);
    }

    public async Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId)
    {
        var formInstance = await GetNewestFormInstanceDtoAsync(businessId);
        return await GetCurrentInstanceDefinitionAsync(formInstance, true);
    }

    public async Task<FormDefinitionDto> GetNamedVersionFormInstanceAsync(string businessId, string version)
    {
        var formInstance = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await GetCurrentInstanceDefinitionAsync(formInstance);
    }

    public async Task<FormDefinitionDto> GetPreviousDifferenceAsync(string businessId,
        string version)
    {
        var formInstance = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await GetNamedVersionDifferenceAsync(formInstance, formInstance.PreviousVersion);
    }


    public async Task<FormDefinitionDto> GetNamedVersionDifferenceAsync(string businessId, string version,
        string namedVersion)
    {
        var formInstance = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await GetNamedVersionDifferenceAsync(formInstance, namedVersion);
    }
}
