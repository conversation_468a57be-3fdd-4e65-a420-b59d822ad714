using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Linq.Expressions;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.DynamicFormFieldInstance.Application.Services;

/// <summary>
/// FormFieldInstance 服务实现
/// </summary>
public sealed class FormFieldInstanceService :
    BaseEditableAppService<long, FormFieldInstanceEntity, FormFieldInstanceDto, FormFieldInstanceOperationDto,
        IFormFieldInstanceRepository, FormFieldInstanceQueryCriteria>,
    IFormFieldInstanceService
{
    private readonly ICurrentUserContext _currentUserContext;
    private readonly JsonSerializerOptions _jsonSerializerOptions;
    private readonly IFormService _formService;

    public Expression<Func<FormFieldInstanceEntity, bool>> GetEqualToFormInstanceExpr(FormInstanceDto formInstanceDto)
    {
        return q =>
            q.BusinessId.ToLower().Equals(formInstanceDto.BusinessId.ToLower()) &&
            q.Version.ToLower().Equals(formInstanceDto.Version.ToLower()) &&
            q.FormCode.ToLower().Equals(formInstanceDto.FormCode.ToLower()) &&
            q.FormVersion.ToLower().Equals(formInstanceDto.FormVersion.ToLower());
    }

    public Expression<Func<FormFieldInstanceEntity, bool>> GetIgnoreCaseEqualToFormInstanceExpr(
        FormInstanceDto formInstanceDto)
    {
        return q =>
            q.BusinessId.Equals(formInstanceDto.BusinessId) &&
            q.Version.Equals(formInstanceDto.Version) &&
            q.FormCode.Equals(formInstanceDto.FormCode) &&
            q.FormVersion.Equals(formInstanceDto.FormVersion);
    }

    public Expression<Func<FormFieldInstanceEntity, bool>> GetEqualToFormInstanceEntityExpr(
        FormInstanceEntity formInstance)
    {
        return q =>
            q.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
            q.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
            q.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
            q.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower());
    }

    public Expression<Func<FormFieldInstanceDto, bool>> GetDtoEqualToFormInstanceExpr(
        FormInstanceDto formInstanceDto)
    {
        return q =>
            q.BusinessId.ToLower().Equals(formInstanceDto.BusinessId.ToLower()) &&
            q.Version.ToLower().Equals(formInstanceDto.Version.ToLower()) &&
            q.FormCode.ToLower().Equals(formInstanceDto.FormCode.ToLower()) &&
            q.FormVersion.ToLower().Equals(formInstanceDto.FormVersion.ToLower());
    }

    public Expression<Func<FormFieldInstanceOperationDto, bool>> GetOperationDtoEqualToFormInstanceExpr(
        FormInstanceDto formInstanceDto)
    {
        return q =>
            q.BusinessId.ToLower().Equals(formInstanceDto.BusinessId.ToLower()) &&
            q.Version.ToLower().Equals(formInstanceDto.Version.ToLower()) &&
            q.FormCode.ToLower().Equals(formInstanceDto.FormCode.ToLower()) &&
            q.FormVersion.ToLower().Equals(formInstanceDto.FormVersion.ToLower());
    }

    public FormFieldInstanceService(IFormFieldInstanceRepository repository, IMapper mapper,
        IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext, IOptions<JsonOptions> jsonOptions,
        IFormService formService) : base(repository, mapper,
        unitOfWork, keyGenerator, currentUserContext)
    {
        _currentUserContext = currentUserContext;
        this._jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
        _formService = formService;
    }

    public async Task<List<FormFieldInstanceDto>> GetListByInstanceAsync(string formVersion, string formCode,
        IEnumerable<FormInstanceDto> instances, List<string> columns)
    {
        var result = await Repository.GetListByInstanceAsync(columns, formVersion, formCode, instances
            .Select(q => (businessId: q.BusinessId, version: q.Version)).ToList());

        return await GetDtosAsync(result);
    }

    public async Task<List<FormFieldInstanceDto>> LoadListAsync(FormInstanceDto formInstanceDto)
    {
        var formFieldInstances = await Repository.GetListAsync(GetIgnoreCaseEqualToFormInstanceExpr(formInstanceDto));

        return formFieldInstances.Select(q => Mapper.Map<FormFieldInstanceDto>(q)).ToList();
    }

    public async Task<List<FormFieldInstanceDto>> LoadListAsync(
        Expression<Func<FormFieldInstanceEntity, bool>> expression)
    {
        var formFieldInstances = await Repository.GetListAsync(expression);

        return formFieldInstances.Select(q => Mapper.Map<FormFieldInstanceDto>(q)).ToList();
    }

    public async Task<bool> SaveAsync(FormInstanceDto formInstanceDto,
        List<FormFieldInstanceOperationDto> sourceFieldInstances, //Dictionary<string, string?>? warnings = null,
        Dictionary<string, AnnotationValue?>? annotationValues = null)
    {
        var originals = await Repository.GetListAsync(GetIgnoreCaseEqualToFormInstanceExpr(formInstanceDto));

        //通过Code和RowIndex属性比较sourceFieldInstances和originals的新增、修改、删除集合 注意 修改和删除需要使用Entity
        var sourceFieldInstancesMap = sourceFieldInstances.ToDictionary(q => q.Code + q.RowIndex);
        var originalsMap = originals.ToDictionary(q => q.Code + q.RowIndex);

        var newFieldInstances = sourceFieldInstancesMap
            .Where(q => !originalsMap.ContainsKey(q.Key))
            .Select(q => Mapper.Map<FormFieldInstanceEntity>(q.Value))
            .ToList();

        var modifiedFieldInstances = originalsMap
            .Where(q => sourceFieldInstancesMap.ContainsKey(q.Key))
            .Select(q => q.Value)
            .ToList();

        var deletedFieldInstances = originalsMap
            .Where(q => !sourceFieldInstancesMap.ContainsKey(q.Key))
            .Select(q => Mapper.Map<FormFieldInstanceEntity>(q.Value))
            .ToList();

        // 处理新增
        foreach (var newFieldInstance in newFieldInstances)
        {
            // string? warning = null;
            // warnings?.TryGetValue(newFieldInstance.Code, out warning);


            newFieldInstance.Key = KeyGenerator.GenerateKey();
            newFieldInstance.BusinessId = formInstanceDto.BusinessId;
            newFieldInstance.Version = formInstanceDto.Version;
            newFieldInstance.FormCode = formInstanceDto.FormCode;
            newFieldInstance.FormVersion = formInstanceDto.FormVersion;
            // newFieldInstance.Warning = warning;

            var originalAnnotationValue =
                (annotationValues?.ContainsKey(newFieldInstance.Code) ??
                 false) && annotationValues?[newFieldInstance.Code] != null
                    ? annotationValues[newFieldInstance.Code]
                    : null;

            if (newFieldInstance.RowIndex is not null)
            {
                if (originalAnnotationValue?.ObjectArray != null)
                {
                    newFieldInstance.Annotations =
                        originalAnnotationValue.ObjectArray.Count - 1 >= newFieldInstance.RowIndex.Value
                            ? JsonSerializer.Serialize(
                                originalAnnotationValue.ObjectArray![newFieldInstance.RowIndex.Value])
                            : null;
                }
                else
                {
                    newFieldInstance.Annotations = JsonSerializer.Serialize(originalAnnotationValue);
                }
            }

            else
            {
                newFieldInstance.Annotations = JsonSerializer.Serialize(originalAnnotationValue);
            }
        }

        if (newFieldInstances.Any())
            await Repository.InsertAsync(newFieldInstances);

        // 处理修改
        foreach (var modifiedFieldInstance in modifiedFieldInstances)
        {
            // string? warning = null;
            // warnings?.TryGetValue(modifiedFieldInstance.Code, out warning);


            var operation = sourceFieldInstancesMap[modifiedFieldInstance.Code + modifiedFieldInstance.RowIndex];

            Mapper.Map(operation, modifiedFieldInstance);

            modifiedFieldInstance.BusinessId = formInstanceDto.BusinessId;
            modifiedFieldInstance.Version = formInstanceDto.Version;
            modifiedFieldInstance.FormCode = formInstanceDto.FormCode;
            modifiedFieldInstance.FormVersion = formInstanceDto.FormVersion;
            // modifiedFieldInstance.Warning = warning;

            var originalAnnotationValue =
                modifiedFieldInstance.Annotations != null
                    ? JsonSerializer.Deserialize<AnnotationValue>(modifiedFieldInstance.Annotations)
                    : null;

            var combined = FormDefinitionHelper.CombineAnnotation(originalAnnotationValue,
                annotationValues?.ContainsKey(modifiedFieldInstance.Code) ?? false
                    ? annotationValues[modifiedFieldInstance.Code]
                    : null);

            if (combined?.ObjectArray != null)
            {
                if (modifiedFieldInstance.RowIndex is not null)
                {
                    modifiedFieldInstance.Annotations =
                        combined.ObjectArray != null &&
                        combined.ObjectArray.Count - 1 >= modifiedFieldInstance.RowIndex.Value
                            ? JsonSerializer.Serialize(
                                combined.ObjectArray![modifiedFieldInstance.RowIndex.Value])
                            : null;
                }
                else
                {
                    modifiedFieldInstance.Annotations = JsonSerializer.Serialize(combined);
                }
            }
            else if (combined?.KeyValue != null)
            {
                modifiedFieldInstance.Annotations = JsonSerializer.Serialize(combined);
            }
        }

        if (modifiedFieldInstances.Any())
            await Repository.UpdateAsync(modifiedFieldInstances);

        // 处理删除
        if (deletedFieldInstances.Any())
            await Repository.DeleteAsync(deletedFieldInstances);
        return true;
    }

    public async Task<bool> SaveAnnotationsWithValidationAsync(FormInstanceDto formInstance,
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        if (formInstance.IsObsoleted)
        {
            throw new ValidationException("已过期版本，无法进行该操作/Expired versions cannot perform this action");
        }

        if (formInstance.ApplyUserId != _currentUserContext.GetCurrentUserId())
        {
            throw new ValidationException("只有发起人可以进行该操作/Only the initiator can perform this operation");
        }

        return await SaveAnnotationsAsync(formInstance, annotationValues);
    }

    public async Task<bool> SaveAnnotationsAsync(FormInstanceDto formInstance,
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        // if (formInstance.Status != FormInstanceStatus.Draft && formInstance.Status != FormInstanceStatus.Rejected)
        // {
        //     throw new ValidationException("只有草稿和已驳回的版本可以进行该操作");
        // }

        var formFieldInstances = (await Repository.GetListAsync(GetEqualToFormInstanceExpr(formInstance))).ToList();

        formFieldInstances.ForEach(field => { field.Annotations = null; });

        annotationValues?.ForEach(kv =>
        {
            var code = kv.Key;
            var fields = formFieldInstances.Where(q => q.Code.ToLower().Equals(code.ToLower())).ToList();

            if (fields.Any())
            {
                var first = fields.First();
                if (!first.ParentCode.IsNullOrEmpty())
                {
                    fields.OrderBy(q => q.RowIndex).ForEach((idx, row) =>
                    {
                        if (kv.Value?.ObjectArray?.Count - 1 >= idx)
                        {
                            var value = kv.Value?.ObjectArray?[idx];
                            if (value != null)
                            {
                                var jsonValue = JsonSerializer.Serialize(new AnnotationValue() { KeyValue = value });
                                row.Annotations = jsonValue;
                            }
                        }
                    });
                }
                else if (first.ParentCode.IsNullOrEmpty() &&
                         formFieldInstances.Any(f =>
                             !f.ParentCode.IsNullOrEmpty() &&
                             f.ParentCode!.ToLower().Equals(first.Code.ToLower())))
                {
                    first.Annotations = JsonSerializer.Serialize(kv.Value);
                }
                else
                {
                    first.Annotations = JsonSerializer.Serialize(kv.Value);
                }
            }
        });

        return await Repository.UpdateAsync(formFieldInstances);
    }

    // public async Task<bool> SaveWarningsAsync(FormInstanceDto formInstance, Dictionary<string, string>? warnings)
    // {
    //     var formFieldInstances = (await Repository.GetListAsync(GetEqualToFormInstanceExpr(formInstance))).ToList();
    //     formFieldInstances.ForEach(field =>
    //     {
    //         if (warnings?.ContainsKey(field.Code) ?? false)
    //         {
    //             field.Warning = warnings[field.Code];
    //         }
    //         else
    //         {
    //             field.Warning = null;
    //         }
    //     });
    //     return await Repository.UpdateAsync(formFieldInstances);
    // }

    public async Task<Dictionary<string, AnnotationValue?>> GetAnnotationsAsync(FormInstanceDto formInstance)
    {
        var formFieldInstance = await LoadListAsync(formInstance);

        return await BuildAnnotationsAsync(formFieldInstance);
    }

    public async Task<Dictionary<string, AnnotationValue?>> BuildAnnotationsAsync(
        List<FormFieldInstanceDto> formFieldInstance)
    {
        var annotations = new Dictionary<string, AnnotationValue?>();

        formFieldInstance.GroupBy(q => q.Code).ForEach(kv =>
        {
            var rows = kv.OrderBy(q => q.RowIndex).ToList();
            var code = kv.Key;

            var first = rows.First();
            // 如果parentCode不为空 或者有parentCode为当前code的。说明是子表单中的字段或者子表单本身
            if (!first.ParentCode.IsNullOrEmpty())
            {
                var values = new List<Dictionary<string, string?>?>();
                rows.ForEach(row =>
                {
                    var value = row.Annotations != null
                        ? JsonSerializer.Deserialize<Dictionary<string, string?>>(row.Annotations!)
                        : null;

                    values.Add(value);
                });
                annotations.Add(code, new AnnotationValue() { ObjectArray = values });
            }
            else if (first.ParentCode.IsNullOrEmpty() &&
                     formFieldInstance.Any(f =>
                         !f.ParentCode.IsNullOrEmpty() &&
                         f.ParentCode!.ToLower().Equals(first.Code.ToLower())))
            {
                annotations.Add(code,
                    first.Annotations != null
                        ? JsonSerializer.Deserialize<AnnotationValue>(first.Annotations!)
                        : null);
            }
            else
            {
                annotations.Add(code,
                    first.Annotations != null
                        ? JsonSerializer.Deserialize<AnnotationValue>(first.Annotations!)
                        : null);
            }
        });

        return await Task.FromResult(annotations);
    }
}
