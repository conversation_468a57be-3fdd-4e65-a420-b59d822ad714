using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.Extensions;

namespace XJ.Framework.DynamicForm.Application.Services;

public partial class FormInstanceService
{
    private const string EnglishPattern =
        @"^[\u0020-\u007e\u00a0-\u00ff\u2000-\u206f\u2100-\u214f\u03bc\u2013\u2019≥≤\u0391-\u03a9\u03b1-\u03c9\u0100-\u017f\u0180-\u024f\u0400-\u04ff]*$";

    private const string EnglishPatternWithNewline =
        @"^[\u0020-\u007e\u00a0-\u00ff\u2000-\u206f\u2100-\u214f\u03bc\u2013\u2019≥≤\u0391-\u03a9\u03b1-\u03c9\u0100-\u017f\u0180-\u024f\u0400-\u04ff\r\n]*$";

    private static List<FieldDto> GetAllFields(FormDefinitionDto formDefinition)
    {
        var result = new List<FieldDto>();
        foreach (var group in formDefinition.Groups)
        {
            foreach (var field in group.Fields)
            {
                result.Add(field);
                if (field.Fields.Count > 0)
                {
                    result.AddRange(GetAllFields(field));
                }
            }
        }

        return result;
    }

    private static List<FieldDto> GetAllFields(FieldDto parent)
    {
        var result = new List<FieldDto>();
        foreach (var field in parent.Fields)
        {
            result.Add(field);
            if (field.Fields.Count > 0)
            {
                result.AddRange(GetAllFields(field));
            }
        }

        return result;
    }

    private static object? NormalizeByType(object? val, string? type)
    {
        if (type == null) return val;

        switch (type)
        {
            case "int":
                if (val == null) return null;
                if (val is int) return val;
                if (val is string s && string.IsNullOrWhiteSpace(s)) return null;
                if (int.TryParse(val.ToString(), out var intVal)) return intVal;
                return null;

            case "float":
                if (val == null) return null;
                if (val is float || val is double) return Convert.ToDouble(val);
                if (val is string s2 && string.IsNullOrWhiteSpace(s2)) return null;
                if (double.TryParse(val.ToString(), out var doubleVal)) return doubleVal;
                return null;

            case "boolean":
                if (val == null) return null;
                if (val is bool b) return b;
                if (val is string s3 && string.IsNullOrWhiteSpace(s3)) return null;
                if (bool.TryParse(val.ToString(), out var boolVal)) return boolVal;
                // 兼容 0/1/yes/no/true/false
                var str = val.ToString()?.ToLowerInvariant();
                if (str == "1" || str == "yes" || str == "true") return true;
                if (str == "0" || str == "no" || str == "false") return false;
                return null;

            case "string":
                return val?.ToString();

            case "date":
                if (val == null) return null;
                if (val is DateTime dt) return dt;
                if (val is string s4 && string.IsNullOrWhiteSpace(s4)) return null;
                if (DateTime.TryParse(val.ToString(), out var dt2)) return dt2;
                return null;

            default:
                return val;
        }
    }

    private static object? EvalValidationExpr(object? expr, Dictionary<string, object?> context,
        Dictionary<string, Func<object?[], Dictionary<string, object?>, object?>> funcs)
    {
        if (expr is bool || expr is int || expr is double || expr is string)
        {
            return expr;
        }

        var dict = JsonElementToDictionary(expr);
        if (dict == null)
        {
            return null;
        }

        if (dict.TryGetValue("property", out var value4))
        {
            var props = JsonElementToObjectList<object>(value4);
            object? val = context;
            if (props != null)
            {
                foreach (var p in props)
                {
                    var d = JsonElementToDictionary(val);
                    if (d?.ContainsKey(p.ToString()!) ?? false)
                    {
                        val = d[p.ToString()!];
                    }
                    else if (context.ContainsKey(p.ToString()!))
                    {
                        val = context[p.ToString()!];
                    }
                    else
                    {
                        return null;
                    }
                }
            }

            if (dict.TryGetValue("type", out var typeObj))
            {
                val = NormalizeByType(val, typeObj?.ToString());
            }

            return val;
        }

        if (dict.TryGetValue("const", out var val1))
        {
            if (dict.TryGetValue("type", out var typeObj))
            {
                val1 = NormalizeByType(val1, typeObj?.ToString());
            }

            return val1;
        }

        if (dict.TryGetValue("func", out var value5))
        {
            var funcName = value5?.ToString();
            object?[] args = [];
            if (dict.TryGetValue("args", out var argsObj) && argsObj is JsonElement argsJson &&
                argsJson.ValueKind == JsonValueKind.Array)
            {
                args = argsJson.Deserialize<List<object>>()?.Select(a => EvalValidationExpr(a, context, funcs))
                    .Cast<object?>().ToArray() ?? [];
            }

            return funcs[funcName!](args, context);
        }

        if (dict.TryGetValue("if", out var value6))
        {
            var cond = Convert.ToBoolean(EvalValidationExpr(value6, context, funcs));
            if (cond)
            {
                return EvalValidationExpr(dict["then"], context, funcs);
            }
            else
            {
                return EvalValidationExpr(dict["else"], context, funcs);
            }
        }

        if (dict.TryGetValue("and", out var value))
        {
            var arr = JsonElementToObjectList<object>(value);
            if (arr == null)
            {
                return false;
            }

            return arr.All(sub => Convert.ToBoolean(EvalValidationExpr(sub, context, funcs)));
        }

        if (dict.TryGetValue("or", out var value1))
        {
            var arr = JsonElementToObjectList<object>(value1);
            if (arr == null)
            {
                return false;
            }

            return arr.Any(sub => Convert.ToBoolean(EvalValidationExpr(sub, context, funcs)));
        }

        if (dict.TryGetValue("not", out var value2))
        {
            return !Convert.ToBoolean(EvalValidationExpr(value2, context, funcs));
        }

        foreach (var op in new[] { "==", "!=", ">", ">=", "<", "<=" })
        {
            if (dict.TryGetValue(op, out var value3))
            {
                var arr = JsonElementToObjectList<object>(value3);
                if (arr == null || arr.Count != 2)
                {
                    return false;
                }

                var l = EvalValidationExpr(arr[0], context, funcs);
                var r = EvalValidationExpr(arr[1], context, funcs);
                switch (op)
                {
                    case "==": return Equals(l, r);
                    case "!=": return !Equals(l, r);
                    case ">": return Comparer<object>.Default.Compare(l, r) > 0;
                    case ">=": return Comparer<object>.Default.Compare(l, r) >= 0;
                    case "<": return Comparer<object>.Default.Compare(l, r) < 0;
                    case "<=": return Comparer<object>.Default.Compare(l, r) <= 0;
                }
            }
        }

        return false;
    }

    private readonly static Dictionary<string, Func<object?[], Dictionary<string, object?>, object?>> DslFuncs = new()
    {
        { "now", (args, ctx) => DateTime.Now },
        { "stringLength", (args, ctx) => (args[0]?.ToString() ?? string.Empty).Length }
        // 其它函数可按需补充
    };

    private static List<T>? JsonElementToObjectList<T>(object? obj)
    {
        if (obj is List<T> list)
        {
            return list;
        }

        if (obj is System.Text.Json.JsonElement je && je.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<T>>(je.GetRawText());
        }

        return null;
    }

    private static Dictionary<string, object?>? JsonElementToDictionary(object? obj)
    {
        if (obj is Dictionary<string, object?> dict)
            return dict;
        if (obj is System.Text.Json.JsonElement je && je.ValueKind == System.Text.Json.JsonValueKind.Object)
            return System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object?>>(je.GetRawText());
        return null;
    }

    // 字段类型与校验方法映射
    private readonly static Dictionary<string, Func<object?, FieldDto, string, string>> FieldValidators = new()
    {
        { "input", ValidateInputField },
        { "number", ValidateNumberField },
        { "date", ValidateDateField },
        { "date_range", ValidateDateRangeField },
        { "checkbox", ValidateCheckboxField },
        { "radio", ValidateRadioField },
        { "select", ValidateSelectField },
        { "textarea", ValidateTextareaField },
        { "text_multilang", ValidateTextMultilangField },
        { "textarea_multilang", ValidateTextareaMultilangField },
        { "file", ValidateFileField },
        { "unit_select", ValidateUnitSelectField },
        { "int_range", ValidateIntRangeField },
        { "subForm", ValidateSubFormField },
        { "multiSubForm", ValidateMultiSubFormField },
    };

    // 各控件类型的校验方法（与前端logic.ts一致，部分简化）
    private static string ValidateInputField(object? value, FieldDto field, string language)
    {
        var val = value?.ToString() ?? "";
        if (field.Required && string.IsNullOrEmpty(val))
            return language == "both" || language == "zh" ? "该字段为必填项" : "This field is required";
        if (field.Extends != null && field.Extends.TryGetValue("minLength", out var minLenObj) &&
            int.TryParse(minLenObj?.ToString(), out var minLen) && val.Length < minLen)
            return language == "both" || language == "zh" ? $"最少{minLen}个字符" : $"At least {minLen} characters required";
        if (field.Extends != null && field.Extends.TryGetValue("maxLength", out var maxLenObj) &&
            int.TryParse(maxLenObj?.ToString(), out var maxLen) && val.Length > maxLen)
            return language == "both" || language == "zh" ? $"最多{maxLen}个字符" : $"At most {maxLen} characters allowed";
        if (field.Extends != null && field.Extends.TryGetValue("pattern", out var patternObj) && val != "")
        {
            var pattern = patternObj.ToString()!;
            if (!System.Text.RegularExpressions.Regex.IsMatch(val, pattern))
            {
                field.Extends.TryGetValue("patternMsg", out var patternMsgObj);

                var patternMsgDict = JsonElementToDictionary(patternMsgObj);

                object? msg;
                return patternMsgDict?.TryGetValue(language == "both" ? "zh" : language, out msg) ?? false
                    ? msg?.ToString() ?? "格式错误"
                    : "格式错误";
            }
        }

        return "";
    }

    private static string ValidateNumberField(object? value, FieldDto field, string language)
    {
        if (field.Required && (value == null || value.ToString() == ""))
            return language == "both" || language == "zh" ? "该字段为必填项" : "This field is required";
        if (double.TryParse(value?.ToString(), out var num))
        {
            if (field.Extends != null && field.Extends.TryGetValue("min", out var minObj) &&
                double.TryParse(minObj?.ToString(), out var min) && num < min)
                return language == "both" || language == "zh"
                    ? $"不能小于最小值{min}"
                    : $"Cannot be less than minimum value {min}";
            if (field.Extends != null && field.Extends.TryGetValue("max", out var maxObj) &&
                double.TryParse(maxObj?.ToString(), out var max) && num > max)
                return language == "both" || language == "zh"
                    ? $"不能大于最大值{max}"
                    : $"Cannot be greater than maximum value {max}";
        }

        return "";
    }

    private static string ValidateDateField(object? value, FieldDto field, string language)
    {
        var val = value?.ToString();
        if (val != null && val != "" && !DateTime.TryParse(val, out _))
            return language == "both" || language == "zh" ? "请输入合法日期" : "Please enter a valid date";
        if (field.Required && (val == null || val == ""))
            return language == "both" || language == "zh" ? "必填" : "Required";
        if (field.Extends != null && field.Extends.TryGetValue("min", out var minObj) &&
            DateTime.TryParse(val, out var dt) && DateTime.TryParse(minObj?.ToString(), out var min) && dt < min)
            return language == "both" || language == "zh"
                ? $"不能早于{min:yyyy-MM-dd}"
                : $"Cannot be earlier than {min:yyyy-MM-dd}";
        if (field.Extends != null && field.Extends.TryGetValue("max", out var maxObj) &&
            DateTime.TryParse(val, out var dt2) && DateTime.TryParse(maxObj?.ToString(), out var max) && dt2 > max)
            return language == "both" || language == "zh"
                ? $"不能晚于{max:yyyy-MM-dd}"
                : $"Cannot be later than {max:yyyy-MM-dd}";
        return "";
    }

    private static string ValidateDateRangeField(object? value, FieldDto field, string language)
    {
        if (value is Dictionary<string, object?> dict)
        {
            var start = dict.TryGetValue("start", out var s) ? s?.ToString() : null;
            var end = dict.TryGetValue("end", out var e) ? e?.ToString() : null;
            if ((start != null && !DateTime.TryParse(start, out _)) || (end != null && !DateTime.TryParse(end, out _)))
                return language == "both" || language == "zh" ? "请输入合法日期" : "Please enter a valid date";
            if (start != null && end != null && DateTime.TryParse(start, out var dt1) &&
                DateTime.TryParse(end, out var dt2) && dt1 >= dt2)
                return language == "both" || language == "zh"
                    ? "开始时间必须早于结束时间"
                    : "Start time must be earlier than end time";
            if (field.Required && (string.IsNullOrEmpty(start) || string.IsNullOrEmpty(end)))
                return language == "both" || language == "zh" ? "区间必填" : "Range is required";
            if (field.Extends != null && field.Extends.TryGetValue("min", out var minObj) &&
                DateTime.TryParse(start, out var dt3) && DateTime.TryParse(minObj?.ToString(), out var min) &&
                dt3 < min)
                return language == "both" || language == "zh"
                    ? $"不能早于{min:yyyy-MM-dd}"
                    : $"Cannot be earlier than {min:yyyy-MM-dd}";
            if (field.Extends != null && field.Extends.TryGetValue("max", out var maxObj) &&
                DateTime.TryParse(end, out var dt4) && DateTime.TryParse(maxObj?.ToString(), out var max) && dt4 > max)
                return language == "both" || language == "zh"
                    ? $"不能晚于{max:yyyy-MM-dd}"
                    : $"Cannot be later than {max:yyyy-MM-dd}";
        }

        return "";
    }

    private static string ValidateCheckboxField(object? value, FieldDto field, string language)
    {
        if (value is IEnumerable<object?> arr)
        {
            var arrList = arr.ToList();
            if (arrList.Any() && field.Options != null &&
                !arrList.All(v => field.Options.Any(opt => opt.Value == v?.ToString())))
                return language == "both" || language == "zh" ? "存在无效选项" : "There are invalid options.";
            if (field.Required && arrList.Count == 0)
                return language == "both" || language == "zh" ? "必选" : "Required";
            if (field.Extends != null && field.Extends.TryGetValue("min", out var minObj) &&
                int.TryParse(minObj?.ToString(), out var min) && arrList.Count < min)
                return language == "both" || language == "zh" ? $"最少选择{min}项" : $"At least select {min} items";
            if (field.Extends != null && field.Extends.TryGetValue("max", out var maxObj) &&
                int.TryParse(maxObj?.ToString(), out var max) && arrList.Count > max)
                return language == "both" || language == "zh" ? $"最多选择{max}项" : $"At most select {max} items";
        }

        return "";
    }

    private static string ValidateRadioField(object? value, FieldDto field, string language)
    {
        if (value != null && value.ToString() != "" &&
            (field.Options == null || !field.Options.Any(opt => opt.Value == value.ToString())))
            return language == "both" || language == "zh" ? "无效选项" : "Invalid option";
        if (field.Required && (value == null || value.ToString() == ""))
            return language == "both" || language == "zh" ? "必选" : "Required";
        return "";
    }

    private static string ValidateSelectField(object? value, FieldDto field, string language)
    {
        if (value != null && value.ToString() != "" &&
            (field.Options == null || !field.Options.Any(opt => opt.Value == value.ToString())))
            return language == "both" || language == "zh" ? "无效选项" : "Invalid option";
        if (field.Required && (value == null || value.ToString() == ""))
            return language == "both" || language == "zh" ? "必选" : "Required";
        return "";
    }

    private static string ValidateTextareaField(object? value, FieldDto field, string language)
    {
        var val = value?.ToString() ?? "";
        if (field.Required && string.IsNullOrEmpty(val))
            return language == "both" || language == "zh" ? "该字段为必填项" : "This field is required";
        if (field.Extends != null && field.Extends.TryGetValue("minLength", out var minLenObj) &&
            int.TryParse(minLenObj?.ToString(), out var minLen) && val.Length < minLen)
            return language == "both" || language == "zh" ? $"最少{minLen}个字符" : $"At least {minLen} characters required";
        if (field.Extends != null && field.Extends.TryGetValue("maxLength", out var maxLenObj) &&
            int.TryParse(maxLenObj?.ToString(), out var maxLen) && val.Length > maxLen)
            return language == "both" || language == "zh" ? $"最多{maxLen}个字符" : $"At most {maxLen} characters allowed";
        if (field.Extends != null && field.Extends.TryGetValue("pattern", out var patternObj) && val != "" &&
            patternObj is string pattern && !System.Text.RegularExpressions.Regex.IsMatch(val, pattern))
        {
            if (field.Extends.TryGetValue("patternMsg", out var patternMsgObj) &&
                patternMsgObj is Dictionary<string, object?> patternMsgDict)
            {
                return patternMsgDict.TryGetValue(language == "both" ? "zh" : language, out var msg)
                    ? msg?.ToString() ?? "格式错误"
                    : "格式错误";
            }

            return "格式错误";
        }

        return "";
    }

    private static string ValidateTextMultilangField(object? value, FieldDto field, string language)
    {
        value ??= new Dictionary<string, object?>();

        var dict = (value as Dictionary<string, object?>)!;

        dict.TryGetValue("zh", out var zhValue);
        dict.TryGetValue("en", out var enValue);

        // 这里只做简单必填和长度校验，复杂可参考前端
        var errors = new List<string>();
        if (field.Required)
        {
            if ((language == "zh" || language == "both") &&
                (zhValue == null || zhValue.ToString() == ""))
                errors.Add(language == "both" || language == "zh" ? "中文必填" : "Chinese is required");
            
            if ((language == "en" || language == "both") &&
                (enValue == null || enValue.ToString() == ""))
                errors.Add(language == "both" || language == "zh" ? "英文必填" : "English is required");
        }

        if (enValue is string enString && !string.IsNullOrEmpty(enString))
        {
            // 移除换行符，保证正则校验一致
            var enStringFlat = enString.Replace("\r", "").Replace("\n", "");
            if (!System.Text.RegularExpressions.Regex.IsMatch(enStringFlat, EnglishPattern))
            {
                errors.Add(language == "both" || language == "zh"
                    ? "英文内容包含不支持的字符"
                    : "English content contains unsupported characters.");
            }
        }
        
        // 如果是双语输入 则需要验证 英文和中文只要有一个输入了 另外一个也必须输入
        if (language == "both")
        {
            if ((zhValue != null && zhValue.ToString() != "") && (enValue == null || enValue.ToString() == ""))
                errors.Add(language == "both" || language == "zh" ? "请填写英文" : "Please fill in English");
            if ((enValue != null && enValue.ToString() != "") && (zhValue == null || zhValue.ToString() == ""))
                errors.Add(language == "both" || language == "zh" ? "请填写中文" : "Please fill in Chinese");
        }

        // ... 其它长度、格式校验可补充
        return string.Join("；", errors);
    }

    private static string ValidateTextareaMultilangField(object? value, FieldDto field, string language)
    {
        // 这里只做简单必填和长度校验，复杂可参考前端
        if (value is Dictionary<string, object?> dict)
        {
            dict.TryGetValue("zh", out var zhValue);
            dict.TryGetValue("en", out var enValue);

            var errors = new List<string>();


            if (field.Required)
            {
                if ((language == "zh" || language == "both") &&
                    (zhValue == null || zhValue.ToString() == ""))
                    errors.Add(language == "both" || language == "zh" ? "中文必填" : "Chinese is required");

                if ((language == "en" || language == "both") &&
                    (enValue == null || enValue.ToString() == ""))
                    errors.Add(language == "both" || language == "zh" ? "英文必填" : "English is required");
            }

            if (enValue is string enString &&
                !string.IsNullOrEmpty(enString))
            {
                // 移除换行符，保证正则校验一致
                var enStringFlat = enString.Replace("\r", "").Replace("\n", "");
                if (!System.Text.RegularExpressions.Regex.IsMatch(enStringFlat, EnglishPatternWithNewline))
                {
                    errors.Add(language == "both" || language == "zh"
                        ? "英文内容包含不支持的字符"
                        : "English content contains unsupported characters.");
                }
            }

            // 如果是双语输入 则需要验证 英文和中文只要有一个输入了 另外一个也必须输入
            if (language == "both")
            {
                if ((zhValue != null && zhValue.ToString() != "") && (enValue == null || enValue.ToString() == ""))
                    errors.Add(language == "both" || language == "zh" ? "请填写英文" : "Please fill in English");
                if ((enValue != null && enValue.ToString() != "") && (zhValue == null || zhValue.ToString() == ""))
                    errors.Add(language == "both" || language == "zh" ? "请填写中文" : "Please fill in Chinese");
            }

            // ... 其它长度、格式校验可补充
            return string.Join("；", errors);
        }

        return "";
    }

    private static string ValidateFileField(object? value, FieldDto field, string language)
    {
        if (field.Required && (value == null || (value is Dictionary<string, object?> dict && dict.Count == 0)))
            return language == "both" || language == "zh" ? "请上传文件" : "Please upload a file";
        // ... 其它文件大小、类型校验可补充
        return "";
    }

    private static string ValidateUnitSelectField(object? value, FieldDto field, string language)
    {
        if (value is Dictionary<string, object?> dict)
        {
            if (dict.TryGetValue("unit", out var unit) && unit != null && unit.ToString() != "" &&
                (field.Options == null || !field.Options.Any(opt => opt.Value == unit.ToString())))
                return language == "both" || language == "zh" ? "无效选项" : "Invalid option";
            if (field.Required && (!dict.TryGetValue("value", out var v) || v == null || v.ToString() == ""))
                return language == "both" || language == "zh" ? "必填" : "Required";
            if (field.Required && (!dict.TryGetValue("unit", out var u) || u == null || u.ToString() == ""))
                return language == "both" || language == "zh" ? "必选" : "Required";
        }

        return "";
    }

    private static string ValidateIntRangeField(object? value, FieldDto field, string language)
    {
        if (value is Dictionary<string, object?> dict)
        {
            var min = dict.TryGetValue("min", out var minObj) ? minObj?.ToString() : null;
            var max = dict.TryGetValue("max", out var maxObj) ? maxObj?.ToString() : null;
            if ((min != null && min != "" && !int.TryParse(min, out _)) ||
                (max != null && max != "" && !int.TryParse(max, out _)))
                return language == "both" || language == "zh" ? "请输入有效数字" : "Please enter valid numbers";
            if (min != null && max != null && int.TryParse(min, out var minVal) &&
                int.TryParse(max, out var maxVal) &&
                minVal >= maxVal)
                return language == "both" || language == "zh"
                    ? "最小值必须小于最大值"
                    : "Minimum value must be less than maximum value";
            if (field.Required && (string.IsNullOrEmpty(min) || string.IsNullOrEmpty(max)))
                return language == "both" || language == "zh" ? "区间必填" : "Range is required";
            if (field.Extends != null && field.Extends.TryGetValue("min", out var minLimitObj) &&
                int.TryParse(min, out var minVal2) && int.TryParse(minLimitObj?.ToString(), out var minLimit) &&
                minVal2 < minLimit)
                return language == "both" || language == "zh"
                    ? $"最小值不能小于{minLimit}"
                    : $"Minimum value cannot be less than {minLimit}";
            if (field.Extends != null && field.Extends.TryGetValue("max", out var maxLimitObj) &&
                int.TryParse(max, out var maxVal2) && int.TryParse(maxLimitObj?.ToString(), out var maxLimit) &&
                maxVal2 > maxLimit)
                return language == "both" || language == "zh"
                    ? $"最大值不能大于{maxLimit}"
                    : $"Maximum value cannot be greater than {maxLimit}";
        }

        return "";
    }

    private static string ValidateSubFormField(object? value, FieldDto field, string language)
    {
        if (value is not Dictionary<string, object?> dict)
            return language == "both" || language == "zh" ? "子表单数据格式错误" : "Subform data format error";
        var errors = new List<string>();
        foreach (var subField in field.Fields)
        {
            var subValue = dict.TryGetValue(subField.Code, out var v) ? v : null;

            var subFieldValue = FieldDtoExtensions.RefactMultiTypeValue(subValue);

            if (FieldValidators.TryGetValue(subField.Type, out var validator))
            {
                var err = validator(GetFieldValue(subFieldValue), subField, language);
                if (!string.IsNullOrEmpty(err))
                    errors.Add($"{subField.LabelZh ?? subField.Code}: {err}");
            }
        }

        if (field.Required && dict.Count == 0)
            errors.Add(language == "both" || language == "zh" ? "请填写子表单" : "Please fill in the form");
        if (field.Extends != null && field.Extends.TryGetValue("minFields", out var minFieldsObj) &&
            int.TryParse(minFieldsObj?.ToString(), out var minFields) && dict.Count < minFields)
            errors.Add(language == "both" || language == "zh"
                ? $"最少填写{minFields}个字段"
                : $"At least fill in {minFields} fields");
        if (field.Extends != null && field.Extends.TryGetValue("maxFields", out var maxFieldsObj) &&
            int.TryParse(maxFieldsObj?.ToString(), out var maxFields) && dict.Count > maxFields)
            errors.Add(language == "both" || language == "zh"
                ? $"最多填写{maxFields}个字段"
                : $"At most fill in {maxFields} fields");
        return string.Join("；", errors);
    }

    public static object? GetFieldValue(MultiTypeValue? multiTypeValue)
    {
        object? value = null;
        if (multiTypeValue?.StringValue != null)
            value = multiTypeValue.StringValue;
        else if (multiTypeValue?.IntValue != null)
            value = multiTypeValue.IntValue;
        else if (multiTypeValue?.StringArray != null)
            value = multiTypeValue.StringArray;
        else if (multiTypeValue?.KeyValue != null)
            value = multiTypeValue.KeyValue;
        else if (multiTypeValue?.ObjectArray != null)
            value = multiTypeValue.ObjectArray;

        return value;
    }

    private static string ValidateMultiSubFormField(object? value, FieldDto field, string language)
    {
        if (value is not IEnumerable<object?> arr)
            return language == "both" || language == "zh" ? "子表单数据格式错误" : "Subform data format error";
        var arrList = arr.ToList();
        var errors = new List<string>();
        for (int i = 0; i < arrList.Count; i++)
        {
            if (arrList[i] is Dictionary<string, object?> rowDict)
            {
                foreach (var subField in field.Fields)
                {
                    var subValue = rowDict.TryGetValue(subField.Code, out var v) ? v : null;

                    var subFieldValue = FieldDtoExtensions.RefactMultiTypeValue(subValue);

                    if (FieldValidators.TryGetValue(subField.Type, out var validator))
                    {
                        var err = validator(GetFieldValue(subFieldValue), subField, language);
                        if (!string.IsNullOrEmpty(err))
                            errors.Add(
                                $"#{i + 1}-{((language == "both" || language == "zh") ? subField.LabelZh : subField.LabelEn) ?? subField.Code}: {err}");
                    }
                }
            }
            else
            {
                errors.Add(
                    $"#{i + 1}: {(language == "both" || language == "zh" ? "子表单数据格式错误" : "Subform data format error")}");
            }
        }

        if (field.Required && arrList.Count == 0)
            errors.Add(language == "both" || language == "zh" ? "请填写子表单" : "Please fill in the subform");
        if (field.Extends != null && field.Extends.TryGetValue("minRows", out var minRowsObj) &&
            int.TryParse(minRowsObj?.ToString(), out var minRows) && arrList.Count < minRows)
            errors.Add(language == "both" || language == "zh"
                ? $"最少{minRows}行"
                : $"At least {minRows} rows required");
        if (field.Extends != null && field.Extends.TryGetValue("maxRows", out var maxRowsObj) &&
            int.TryParse(maxRowsObj?.ToString(), out var maxRows) && arrList.Count > maxRows)
            errors.Add(language == "both" || language == "zh"
                ? $"最多{maxRows}行"
                : $"At most {maxRows} rows allowed");
        return string.Join("；", errors);
    }

    private async Task<List<string>> GetValidateAsync(FormInstanceDto formInstance)
    {
        var formDefinitionDto = await this.GetNewestFormInstanceAsync(formInstance.BusinessId);

        var language = formDefinitionDto.Language?.ToLowerInvariant() ?? "both";

        // 1. 获取所有字段
        var allFields = GetAllFields(formDefinitionDto);
        // 2. 初始化所有字段可见性为 true
        var fieldVisible = new Dictionary<string, bool>();
        foreach (var field in allFields)
        {
            fieldVisible[field.Code] = true;
        }

        // 3. 处理 behavior
        if (formDefinitionDto.JsonConfig != null &&
            formDefinitionDto.JsonConfig.TryGetValue("behavior", out var behaviorObj))
        {
            var behaviorList = JsonElementToObjectList<Dictionary<string, object?>>(behaviorObj);
            if (behaviorList != null)
            {
                foreach (var behavior in behaviorList)
                {
                    var triggerCode = behavior.GetValueOrDefault("code")?.ToString();
                    var showFields =
                        JsonElementToObjectList<Dictionary<string, object?>>(
                            behavior.GetValueOrDefault("showFields"));
                    var hideFields =
                        JsonElementToObjectList<Dictionary<string, object?>>(
                            behavior.GetValueOrDefault("hideFields"));
                    var triggerValue = allFields.FirstOrDefault(q =>
                            q.Code.Equals(triggerCode, StringComparison.InvariantCultureIgnoreCase))?.Value
                        ?.StringValue;
                    // showFields
                    if (showFields != null)
                    {
                        foreach (var show in showFields)
                        {
                            var condition = JsonElementToDictionary(show.GetValueOrDefault("condition"));
                            var fields = JsonElementToObjectList<object>(show.GetValueOrDefault("fields"));
                            var matched = false;
                            if (condition != null)
                            {
                                if (condition.TryGetValue("equals", out var eq))
                                {
                                    matched = triggerValue?.ToString() == eq?.ToString();
                                }
                                else if (condition.TryGetValue("notEquals", out var neq))
                                {
                                    matched = triggerValue?.ToString() != neq?.ToString();
                                }
                            }

                            if (matched && fields != null)
                            {
                                foreach (var code in fields)
                                {
                                    fieldVisible[code.ToString()!] = true;
                                }
                            }
                        }
                    }

                    // hideFields
                    if (hideFields != null)
                    {
                        foreach (var hide in hideFields)
                        {
                            var condition = JsonElementToDictionary(hide.GetValueOrDefault("condition"));
                            var fields = JsonElementToObjectList<object>(hide.GetValueOrDefault("fields"));
                            var matched = false;
                            if (condition != null)
                            {
                                if (condition.TryGetValue("equals", out var eq))
                                {
                                    matched = triggerValue?.ToString() == eq?.ToString();
                                }
                                else if (condition.TryGetValue("notEquals", out var neq))
                                {
                                    matched = triggerValue?.ToString() != neq?.ToString();
                                }
                            }

                            if (matched && fields != null)
                            {
                                foreach (var code in fields)
                                {
                                    fieldVisible[code.ToString()!] = false;
                                }
                            }
                        }
                    }
                }
            }
        }

        // 4. 处理 validation
        var errors = new List<string>();
        if (formDefinitionDto.JsonConfig != null &&
            formDefinitionDto.JsonConfig.TryGetValue("validation", out var validationObj))
        {
            var validationList = JsonElementToObjectList<Dictionary<string, object?>>(validationObj);
            if (validationList != null)
            {
                foreach (var rule in validationList)
                {
                    var expr = rule.GetValueOrDefault("expression");
                    var triggers = JsonElementToObjectList<object>(rule.GetValueOrDefault("triggers"));
                    var message = JsonElementToDictionary(rule.GetValueOrDefault("message"));
                    // 只校验可见字段
                    if (triggers != null &&
                        triggers.Any(code => fieldVisible.GetValueOrDefault(code.ToString()!, true)))
                    {
                        // 构建 context
                        var context = new Dictionary<string, object?>();
                        foreach (var field in allFields)
                        {
                            if (field.Value?.StringValue != null)
                            {
                                context[field.Code] = field.Value.StringValue;
                            }
                            else if (field.Value?.IntValue != null)
                            {
                                context[field.Code] = field.Value.IntValue;
                            }
                            else if (field.Value?.StringArray != null)
                            {
                                context[field.Code] = field.Value.StringArray;
                            }
                            else if (field.Value?.KeyValue != null)
                            {
                                context[field.Code] = field.Value.KeyValue;
                            }
                            else if (field.Value?.ObjectArray != null)
                            {
                                context[field.Code] = field.Value.ObjectArray;
                            }
                        }

                        var ok = Convert.ToBoolean(EvalValidationExpr(expr, context, DslFuncs));
                        if (!ok)
                        {
                            var msg = message?.GetValueOrDefault("en")?.ToString();
                            if (language == "both" || language == "zh")
                            {
                                msg = message?.GetValueOrDefault("zh")?.ToString();
                            }

                            errors.Add(msg ?? message?.ToString() ?? "校验未通过");
                        }
                    }
                }
            }
        }

        // 字段级固定校验
        foreach (var field in formDefinitionDto.Groups.SelectMany(g => g.Fields))
        {
            var isFieldVisible = fieldVisible.GetValueOrDefault(field.Code.ToString()!, true);

            if (isFieldVisible && FieldValidators.TryGetValue(field.Type, out var validator))
            {
                var value = GetFieldValue(field.Value);
                var errorMsg = validator(value, field, language ?? "both");
                if (!string.IsNullOrEmpty(errorMsg))
                {
                    errors.Add(
                        $"{(language == "both" || language == "zh" ? field.LabelZh : field.LabelEn)}: {errorMsg}");
                }
            }
        }

        return await Task.FromResult(errors);
    }
}
