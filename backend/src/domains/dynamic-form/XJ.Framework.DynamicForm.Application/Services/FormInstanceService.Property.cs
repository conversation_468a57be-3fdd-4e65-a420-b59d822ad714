using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.DynamicForm.Application.Services;

public partial class FormInstanceService
{
    /// <summary>
    /// 使用最新表单定义和数据值返回字段实例
    /// </summary>
    /// <param name="formCode"></param>
    /// <param name="dataValue"></param>
    /// <returns></returns>
    private async Task<List<FormFieldInstanceOperationDto>> GetFieldInstancesAsync(string formCode,
        Dictionary<string, MultiTypeValue?>? dataValue)
    {
        var formInstance = await _formService.GetNewestFormDtoAsync(formCode);
        return await GetFieldInstancesAsync(formInstance.Code, formInstance.Version, dataValue);
    }


    /// <summary>
    /// 使用指定表单版本的定义和数据值返回字段定义
    /// </summary>
    /// <param name="formCode"></param>
    /// <param name="version"></param>
    /// <param name="dataValue"></param>
    /// <returns></returns>
    private async Task<List<FormFieldInstanceOperationDto>> GetFieldInstancesAsync(string formCode, string version,
        Dictionary<string, MultiTypeValue?>? dataValue)
    {
        var formDefinition = await _formService.LoadDefinitionAsync(formCode, version);
        if (formDefinition == null)
            throw new ValidationException($"表单定义不存在：{formCode}/The form definition does not exist：{formCode}");


        await FormDefinitionHelper.SetFieldValueAsync(formDefinition!, dataValue);


        return await GetFieldInstancesAsync(formDefinition!);
    }

    /// <summary>
    /// 使用表单定义转换为FormFieldInstanceDto集合数据格式
    /// </summary>
    /// <param name="definitionDto"></param>
    /// <returns></returns>
    private async Task<List<FormFieldInstanceOperationDto>> GetFieldInstancesAsync(FormDefinitionDto definitionDto)
    {
        var fieldInstanceOperationDtos = new List<FormFieldInstanceOperationDto>();

        await definitionDto.Groups.ForEachAsync(async groupDto =>
        {
            await groupDto.Fields.ForEachAsync(async field =>
            {
                var fieldInstanceOperationDto = new FormFieldInstanceOperationDto()
                {
                    Code = field.Code,
                    Type = field.Type,
                    FormCode = definitionDto.Code,
                    JsonValue = SerializeMultiTypeValue(field.Value),
                    ParentCode = null,
                    RowIndex = null
                };
                fieldInstanceOperationDtos.Add(fieldInstanceOperationDto);

                await field.Fields.ForEachAsync(async childField =>
                {
                    if (field.Value?.KeyValue != null)
                    {
                        var childFieldInstanceOperationDto = new FormFieldInstanceOperationDto()
                        {
                            Code = childField.Code,
                            Type = childField.Type,
                            FormCode = definitionDto.Code,
                            JsonValue = SerializeMultiTypeValue(childField.Value),
                            ParentCode = field.Code,
                            RowIndex = null
                        };
                        fieldInstanceOperationDtos.Add(childFieldInstanceOperationDto);
                    }
                    else if (field.Value?.ObjectArray != null)
                    {
                        field.Value?.ObjectArray?.ForEach((idx, row) =>
                        {
                            row.TryGetValue(childField.Code, out var rowFieldValue);
                            var childFieldInstanceOperationDto = new FormFieldInstanceOperationDto()
                            {
                                Code = childField.Code,
                                Type = childField.Type,
                                FormCode = definitionDto.Code,
                                JsonValue = SerializeMultiTypeValue(rowFieldValue),
                                ParentCode = field.Code,
                                RowIndex = idx
                            };
                            fieldInstanceOperationDtos.Add(childFieldInstanceOperationDto);
                        });
                    }

                    await Task.CompletedTask;
                });
            });
        });

        return fieldInstanceOperationDtos;
    }


    private async Task<Dictionary<string, MultiTypeValue?>?> GetFieldValuesAsync(
        List<FormFieldInstanceDto> fieldInstanceDtos)
    {
        var fieldValues = new Dictionary<string, MultiTypeValue?>();

        var groupByFields = fieldInstanceDtos.GroupBy(q => q.Code).ToList();

        groupByFields.Where(q => q.FirstOrDefault()!.ParentCode.IsNullOrEmpty())
            .ForEach(field =>
            {
                var first = field.FirstOrDefault()!;

                var jsonValue = DeserializeMultiTypeValue(first.JsonValue);

                // var children = groupByFields.Where(child =>
                //     child.FirstOrDefault()!.ParentCode != null &&
                //     child.FirstOrDefault()!.ParentCode!.ToLower() == field.Key.ToLower());


                fieldValues.Add(first.Code, jsonValue);

                //子表单已经带上了下面的值 不需要遍历下级


                // if (!children.Any())
                // {
                // }
                // else if (jsonValue?.ObjectArray != null)
                // {
                //     //多行
                // }
                // else if (jsonValue?.KeyValue != null)
                // {
                //     //单行
                // }
            });


        return await Task.FromResult(fieldValues);
    }

    private async Task<Dictionary<string, MultiTypeValue?>?> GetFieldValuesAsync(FormInstanceDto formInstance)
    {
        var fieldInstanceDtos = await _formFieldInstanceService.LoadListAsync(formInstance);
        return await GetFieldValuesAsync(fieldInstanceDtos);
    }

    private string SerializeMultiTypeValue(object? value)
    {
        return JsonSerializer.Serialize(value, _jsonSerializerOptions);
    }

    private MultiTypeValue? DeserializeMultiTypeValue(string? jsonString)
    {
        if (jsonString.IsNullOrEmpty())
            return null;
        return JsonSerializer.Deserialize<MultiTypeValue>(jsonString!, _jsonSerializerOptions);
    }


    private async Task CompareDifferenceAsync(FormDefinitionDto source, FormDefinitionDto? target)
    {
        source.PreviousVersion = target?.Version;
        if (target != null)
        {
            await source.Groups.ForEachAsync(async group =>
            {
                await group.Fields.ForEachAsync(async field =>
                {
                    var targetField = target.Groups.SelectMany(q => q.Fields).FirstOrDefault(q => q.Code == field.Code);
                    if (targetField != null)
                    {
                        //这里不需要向下遍历，子表单的Value上已经带了下级的值
                        field.PreviousValue = targetField.Value;
                    }

                    await Task.CompletedTask;
                });
            });
        }
    }
}
