using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Text.Json;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.DynamicForm.Application.Services;

/// <summary>
/// Form 服务实现
/// </summary>
public sealed class FormService :
    BaseEditableAppService<long, FormEntity, FormDto, FormOperationDto, IFormRepository, FormQueryCriteria>,
    IFormService
{
    private readonly IFormFieldGroupRepository _formFieldGroupRepository;
    private readonly IFormFieldRepository _formFieldRepository;
    private readonly IFormRepository _formRepository;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    public FormService(IFormRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext,
        IFormFieldGroupRepository formFieldGroupRepository, IFormRepository formRepository,
        IFormFieldRepository formFieldRepository, IOptions<JsonOptions> jsonOptions) : base(repository, mapper,
        unitOfWork,
        keyGenerator, currentUserContext)
    {
        _formFieldGroupRepository = formFieldGroupRepository;
        _formRepository = formRepository;
        _formFieldRepository = formFieldRepository;
        this._jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
    }

    public async new Task<bool> UpdateAsync(long id, FormOperationDto entity)
    {
        var finder = await Repository.GetAsync(id);
        finder.NullCheck();

        entity.Version = finder!.Version;
        entity.NewestVersion = finder!.NewestVersion;
        return await base.UpdateAsync(id, entity);
    }

    public async Task<bool> SaveDefinitionAsync(string formCode, FormDefinitionDto formDefinitionDto)
    {
        var form = await GetNewestFormDtoAsync(formCode);
        form.NullCheck();

        var formOperationDto = Mapper.Map<FormOperationDto>(form);


        // form!.Version = DateTimeOffset.UtcNow.ToString("yyyyMMddHHmmssfff");
        formOperationDto!.NewestVersion = false;
        await base.UpdateAsync(form.Key, formOperationDto);

        var newestForm = Mapper.Map<FormOperationDto>(formOperationDto);
        newestForm.Version = DateTimeOffset.UtcNow.ToString("yyyyMMddHHmmssfff");
        newestForm.NewestVersion = true;
        newestForm.JsonConfig = formDefinitionDto.JsonConfig != null
            ? JsonSerializer.Serialize(formDefinitionDto.JsonConfig, _jsonSerializerOptions)
            : null;

        await this.CreateAsync(newestForm);


        var fieldOperationDtos = new List<FormFieldOperationDto>();
        var fieldGroupOperationDtos = new List<FormFieldGroupOperationDto>();

        await FlatToOperationDtosAsync(newestForm.Code, newestForm.Version, formDefinitionDto, fieldOperationDtos,
            fieldGroupOperationDtos);

        await SaveFormFieldGroupsAsync(newestForm.Code, newestForm.Version, fieldGroupOperationDtos);

        await SaveFormFieldsAsync(newestForm.Code, newestForm.Version, fieldOperationDtos);

        return true;
    }


    public async Task<FormDefinitionDto> LoadDefinitionAsync(string formCode)
    {
        var form = await GetNewestFormDtoAsync(formCode);
        form.NullCheck();

        return await BuildDefinitionDtoTreeAsync(form!.Code, form!.Version);
    }


    public async Task<FormDefinitionDto> LoadDefinitionAsync(string formCode, string formVersion)
    {
        var form = await GetNamedVersionFormDtoAsync(formCode, formVersion);
        form.NullCheck();

        return await BuildDefinitionDtoTreeAsync(form!.Code, form!.Version);
    }

    public async Task<FormDto> GetNewestFormDtoAsync(string formCode)
    {
        var form = await _formRepository.GetAsync(q =>
            q.Code.ToLower().Equals(formCode.ToLower()) && q.NewestVersion);

        return Mapper.Map<FormDto>(form);
    }

    public async Task<FormDto> GetNamedVersionFormDtoAsync(string formCode, string formVersion)
    {
        var form = await _formRepository.GetAsync(q =>
            q.Code.ToLower().Equals(formCode.ToLower()) && q.Version.ToLower().Equals(formVersion.ToLower()));

        return Mapper.Map<FormDto>(form);
    }

    public async Task<List<FormFieldDto>> GetFormFieldDtosAsync(string formCode, string formVersion)
    {
        var formFields = (await _formFieldRepository.LoadAsync(q =>
                q.FormCode.ToLower().Equals(formCode.ToLower()) && q.Version.ToLower().Equals(formVersion.ToLower())))
            .ToList();

        var formFieldDtos = Mapper.Map<List<FormFieldDto>>(formFields);
        return formFieldDtos;
    }


    public async Task<List<FormFieldDto>> GetNewestFormFieldDtosAsync(string formCode)
    {
        var formDto = await GetNewestFormDtoAsync(formCode);
        var formFields = (await _formFieldRepository.LoadAsync(q =>
                q.FormCode.ToLower().Equals(formCode.ToLower()) &&
                q.Version.ToLower().Equals(formDto.Version.ToLower())))
            .ToList();

        var formFieldDtos = Mapper.Map<List<FormFieldDto>>(formFields);
        return formFieldDtos;
    }

    public async Task<List<FormFieldGroupDto>> GetFormFieldGroupDtosAsync(string formCode, string formVersion)
    {
        var fieldGroups =
            (await _formFieldGroupRepository.LoadAsync(q =>
                q.FormCode.ToLower().Equals(formCode.ToLower()) && q.Version.ToLower().Equals(formVersion.ToLower())))
            .OrderBy(q => q.SortOrder).ToList();

        return Mapper.Map<List<FormFieldGroupDto>>(fieldGroups);
    }

    public async Task<string> GetFormInstanceDefaultValueStringAsync(string formCode, string formVersion)
    {
        return JsonSerializer.Serialize(await GetFormInstanceDefaultValueAsync(formCode, formVersion),
            _jsonSerializerOptions);
    }

    public async Task<Dictionary<string, MultiTypeValue?>> GetFormInstanceDefaultValueAsync(string formCode,
        string formVersion)
    {
        var dataValue = new Dictionary<string, MultiTypeValue?>();
        var formFields = await GetFormFieldDtosAsync(formCode, formVersion);

        await formFields.Where(q => q.ParentCode.IsNullOrEmpty()).ForEachAsync(async formField =>
        {
            dataValue.Add(formField.Code, formField.DefaultValue != null
                ? JsonSerializer.Deserialize<MultiTypeValue>(formField.DefaultValue, _jsonSerializerOptions)
                : null);

            await Task.CompletedTask;
        });
        return dataValue;
    }

    private async Task<FormDefinitionDto> BuildDefinitionDtoTreeAsync(string formCode, string formVersion)
    {
        var formDto = await GetNamedVersionFormDtoAsync(formCode, formVersion);


        var formDefinitionDto = new FormDefinitionDto
        {
            Code = formCode,
            JsonConfig = formDto.JsonConfig != null
                ? JsonSerializer.Deserialize<Dictionary<string, object>>(formDto.JsonConfig, _jsonSerializerOptions)
                : null
        };

        var fieldGroups = await GetFormFieldGroupDtosAsync(formCode, formVersion);

        var fields = await GetFormFieldDtosAsync(formCode, formVersion);

        var formFieldGroupDtos = Mapper.Map<List<FormFieldGroupDto>>(fieldGroups);
        var formFieldDtos = Mapper.Map<List<FormFieldDto>>(fields);

        await formFieldGroupDtos.ForEachAsync(async fieldGroupDtos =>
        {
            var groupDto = new GroupDto()
            {
                Id = Guid.NewGuid().ToString(),
                Code = fieldGroupDtos.Code,
                LabelZh = fieldGroupDtos.TitleZh ?? string.Empty,
                LabelEn = fieldGroupDtos.TitleEn ?? string.Empty
            };
            var groupFormFieldDtos =
                formFieldDtos.Where(q => q.GroupCode.ToLower().Equals(groupDto.Code.ToLower()))
                    .OrderBy(q => q.SortOrder)
                    .ToList();

            await BuildGroupFieldDtoTreeAsync(groupDto, groupFormFieldDtos);
            formDefinitionDto.Groups.Add(groupDto);
        });
        return formDefinitionDto;
    }

    private async Task BuildGroupFieldDtoTreeAsync(GroupDto groupDto, List<FormFieldDto> groupFormFieldDtos)
    {
        var rootFromFieldDto = groupFormFieldDtos.Where(q => q.ParentCode == null).ToList();
        if (rootFromFieldDto.Any())
        {
            await rootFromFieldDto.ForEachAsync(async fieldDto =>
            {
                var field = await BuildGroupFieldDtoFieldTreeAsync(fieldDto, groupFormFieldDtos.ToList());
                groupDto.Fields.Add(field);
            });
        }
    }

    private async Task<FieldDto> BuildGroupFieldDtoFieldTreeAsync(FormFieldDto parentFormFieldDto,
        List<FormFieldDto> groupFormFieldDtos)
    {
        var fieldDto = new FieldDto()
        {
            Id = Guid.NewGuid().ToString(),
            Code = parentFormFieldDto.Code,
            LabelZh = parentFormFieldDto.LabelZh ?? string.Empty,
            LabelEn = parentFormFieldDto.LabelEn ?? string.Empty,
            Type = parentFormFieldDto.Type,
            Required = parentFormFieldDto.Required,
            NewLine = parentFormFieldDto.NewLine,
            Unit = parentFormFieldDto.Colspan,
            RejectReasons = parentFormFieldDto.RejectReason != null
                ? JsonSerializer.Deserialize<List<string>>(parentFormFieldDto.RejectReason!, _jsonSerializerOptions)!
                : new List<string>(),
            Description =
                parentFormFieldDto.Description != null
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(parentFormFieldDto.Description,
                        _jsonSerializerOptions)
                    : null,
            Options = parentFormFieldDto.Option != null
                ? JsonSerializer.Deserialize<List<OptionDto>>(parentFormFieldDto.Option, _jsonSerializerOptions)
                : null,
            Value = parentFormFieldDto.DefaultValue != null
                ? JsonSerializer.Deserialize<MultiTypeValue>(parentFormFieldDto.DefaultValue, _jsonSerializerOptions)
                : null,
            Extends = parentFormFieldDto.ExtraConfig != null
                ? JsonSerializer.Deserialize<ExtendDto>(parentFormFieldDto.ExtraConfig, _jsonSerializerOptions)
                : null,
        };

        var children = groupFormFieldDtos.Where(q =>
                q.ParentCode != null && q.ParentCode.ToLower().Equals(parentFormFieldDto.Code.ToLower()))
            .OrderBy(q => q.SortOrder);

        await children.ForEachAsync(async child =>
        {
            fieldDto.Fields.Add(await BuildGroupFieldDtoFieldTreeAsync(child, groupFormFieldDtos));
        });

        return fieldDto;
    }

    private async Task<bool> SaveFormFieldsAsync(string formCode, string formVersion,
        List<FormFieldOperationDto> operationDtos)
    {
        var addEntities = operationDtos.Select(operationDto =>
        {
            var entity = Mapper.Map<FormFieldEntity>(operationDto);
            entity.Key = KeyGenerator.GenerateKey();
            entity.FormCode = formCode;
            entity.Version = formVersion;
            return entity;
        }).ToList();
        await _formFieldRepository.InsertAsync(addEntities);

        return true;
    }

    private async Task<bool> SaveFormFieldGroupsAsync(string formCode, string formVersion,
        List<FormFieldGroupOperationDto> operationDtos)
    {
        var addEntities = operationDtos.Select(operationDto =>
        {
            var entity = Mapper.Map<FormFieldGroupEntity>(operationDto);
            entity.Key = KeyGenerator.GenerateKey();
            entity.FormCode = formCode;
            entity.Version = formVersion;
            return entity;
        }).ToList();
        await _formFieldGroupRepository.InsertAsync(addEntities);
        return true;
    }

    private async Task FlatToOperationDtosAsync(string formCode, string formVersion,
        FormDefinitionDto formDefinitionDto,
        List<FormFieldOperationDto> fields, List<FormFieldGroupOperationDto> fieldGroups)
    {
        await formDefinitionDto.Groups.ForEachAsync(async groupDto =>
        {
            var fieldGroup = new FormFieldGroupOperationDto()
            {
                FormCode = formCode,
                Version = formVersion,
                Code = groupDto.Code,
                TitleZh = groupDto.LabelZh,
                TitleEn = groupDto.LabelEn,
                SortOrder = fieldGroups.Count
            };

            fieldGroups.Add(fieldGroup);

            await groupDto.Fields.ForEachAsync(async fieldDto =>
            {
                await RecursiveFieldsAsync(formCode, formVersion, groupDto, null, fieldDto, fields);
            });
        });
    }

    private async Task RecursiveFieldsAsync(string formCode, string formVersion, GroupDto groupDto,
        FieldDto? parentFieldDto, FieldDto fieldDto,
        List<FormFieldOperationDto> fields)
    {
        var field = new FormFieldOperationDto()
        {
            FormCode = formCode,
            Version = formVersion,
            ParentCode = parentFieldDto?.Code,
            GroupCode = groupDto.Code,
            Code = fieldDto.Code,
            LabelZh = fieldDto.LabelZh,
            LabelEn = fieldDto.LabelEn,
            Type = fieldDto.Type,
            Required = fieldDto.Required,
            NewLine = fieldDto.NewLine,
            Description = fieldDto.Description?.ToJson(),
            Option = fieldDto.Options?.ToJson(),
            DefaultValue = JsonSerializer.Serialize(fieldDto.Value, _jsonSerializerOptions),
            Colspan = fieldDto.Unit,
            SortOrder = fields.Count,
            RejectReason = fieldDto.RejectReasons?.ToJson(),
            ExtraConfig = fieldDto.Extends?.ToJson(),
        };


        fields.Add(field);


        if (fieldDto.Fields.Count != 0)
        {
            await fieldDto.Fields.ForEachAsync(async child =>
            {
                await RecursiveFieldsAsync(formCode, formVersion, groupDto, fieldDto, child, fields);
            });
        }

        await Task.CompletedTask;
    }
}
