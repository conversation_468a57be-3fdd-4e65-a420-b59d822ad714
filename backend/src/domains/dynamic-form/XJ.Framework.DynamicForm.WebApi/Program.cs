using XJ.Framework.DynamicForm.Domain.Shared.Consts;
using XJ.Framework.DynamicForm.Module;
using XJ.Framework.DynamicForm.Interface;
using XJ.Framework.Library.Interface.Services;

var builder = WebApplication.CreateBuilder(args);

await builder
    .Init<Program, WebApiAuthProvider, WebApiAuthInfoGetter, DynamicFormWebApiWrapper, DynamicFormInterfaceWrapper>(
        ModuleConst.ApplicationCode, "").RunAsync();

