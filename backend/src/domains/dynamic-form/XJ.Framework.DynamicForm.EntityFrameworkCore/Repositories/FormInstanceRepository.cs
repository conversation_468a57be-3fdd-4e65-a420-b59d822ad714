using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Repositories;

/// <summary>
/// FormInstance 仓储实现
/// </summary>
public class FormInstanceRepository : BaseSoftDeleteRepository<DynamicFormDbContext, long, FormInstanceEntity>,
    IFormInstanceRepository
{
    public FormInstanceRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 构建最新版本查询的完整SQL和参数
    /// </summary>
    /// <param name="whereItems">WHERE条件项</param>
    /// <param name="dynamicQueries">动态查询条件</param>
    /// <param name="formDataDynamicQueries">表单数据动态查询条件</param>
    /// <param name="orderByItems">排序条件项</param>
    /// <param name="dynamicOrderBys">动态排序条件</param>
    /// <param name="formDataDynamicOrderBys">表单数据动态排序条件</param>
    /// <param name="isNewest">是否取最新数据</param>
    /// <returns>完整SQL和参数字典</returns>
    private (string completeSql, string countSql, Dictionary<string, object> parameters)
        BuildQuerySql(
            List<WhereItem> whereItems,
            Dictionary<string, DynamicFormQueryDto> dynamicQueries,
            List<FormDataDynamicQuery> formDataDynamicQueries,
            List<OrderByItem> orderByItems,
            Dictionary<string, SortDirection> dynamicOrderBys,
            Dictionary<string, SortDirection> formDataDynamicOrderBys,
            bool isNewest = true
        )
    {
        // 添加最新版本的基础条件
        whereItems.Add(WhereItemFactory.Equal("obsoleted", false));
        whereItems.Add(WhereItemFactory.Equal("is_deleted", false));
        if (isNewest)
        {
            whereItems.Add(WhereItemFactory.NotExists(@"
            SELECT 1 FROM d.form_instances f_inner
            WHERE f_inner.id != form_instances.id
                AND f_inner.obsoleted = 0 AND f_inner.is_deleted = 0
                AND f_inner.business_id = form_instances.business_id
                AND f_inner.version_time > form_instances.version_time"));
        }

        // 添加动态查询条件
        AddDynamicWhereItems(whereItems, dynamicQueries);
        AddFormDataDynamicWhereItems(whereItems, formDataDynamicQueries);

        // 添加动态排序条件
        AddDynamicOrderByItems(orderByItems, dynamicOrderBys);
        AddFormDataDynamicOrderByItems(orderByItems, formDataDynamicOrderBys);

        if (orderByItems.Count == 0)
        {
            orderByItems.Add(OrderByItemFactory.Ascending("id"));
        }

        // 构建SQL
        var parameters = new Dictionary<string, object>();
        var whereClause = WhereItemBuilder.BuildWhereClause(whereItems, parameters);
        var orderByClause = OrderByItemBuilder.BuildOrderByClause(orderByItems);
        var completeSql = $"SELECT * FROM d.form_instances {whereClause} {orderByClause}";
        var countSql = $"SELECT COUNT(1) AS Value FROM d.form_instances {whereClause}";

        return (completeSql, countSql, parameters);
    }

    /// <summary>
    /// 获取最新版本记录的总数
    /// </summary>
    /// <param name="whereItems">WHERE条件项</param>
    /// <param name="dynamicQueries">动态查询条件</param>
    /// <param name="formDataDynamicQueries">表单数据动态查询条件</param>
    /// <param name="isNewest">是否取最新数据</param>
    /// <returns>记录总数</returns>
    public async Task<int> GetCountRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, DynamicFormQueryDto> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        bool isNewest = true)
    {
        // 对于计数查询，不需要排序条件
        var emptyOrderByItems = new List<OrderByItem>();
        var emptyDynamicOrderBys = new Dictionary<string, SortDirection>();
        var emptyFormDataDynamicOrderBys = new Dictionary<string, SortDirection>();

        // 构建完整查询SQL
        var (_, countSql, parameters) = BuildQuerySql(
            whereItems, dynamicQueries, formDataDynamicQueries,
            emptyOrderByItems, emptyDynamicOrderBys, emptyFormDataDynamicOrderBys, isNewest);


        // 执行count查询
        var totalCount = await this.DbContext.Database
            .SqlQueryRaw<int>(countSql, parameters.Values.ToArray())
            .FirstOrDefaultAsync();

        return totalCount;
    }


    public async Task<PageData<long, FormInstanceEntity>> GetPageRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, DynamicFormQueryDto> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        bool isNewest = true
    )
    {
        // 构建完整查询SQL
        var (completeSql, countSql, parameters) = BuildQuerySql(
            whereItems, dynamicQueries, formDataDynamicQueries,
            orderByItems, dynamicOrderBys, formDataDynamicOrderBys, isNewest);

        // 构建分页和计数SQL
        var pageSql = $"{completeSql}  OFFSET {rowIndex} ROWS FETCH NEXT {pageSize} ROWS ONLY";

        // 执行查询
        var data = this.DbSet.FromSqlRaw(pageSql, parameters.Values.ToArray());

        // 执行count查询获取总数
        var totalCount = await this.DbContext.Database
            .SqlQueryRaw<int>(countSql, parameters.Values.ToArray())
            .FirstOrDefaultAsync();

        var pageData = new PageData<long, FormInstanceEntity>
        {
            Totals = totalCount,
            Rows = await data.ToListAsync()
        };

        return pageData;
    }

    private void AddFormDataDynamicOrderByItems(List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> formDataDynamicOrderBys)
    {
        formDataDynamicOrderBys.ForEach(kv =>
        {
            orderByItems.Add(new OrderByItem()
            {
                DataField = $@"
                    (select value
                        from d.form_instance_datas
                        where business_id = form_instances.business_id
                          and version = form_instances.version
                          and form_code = form_instances.form_code
                          and form_version = form_instances.form_version
                          and is_deleted = 0
                          and code = N'{kv.Key}')",
                SortDirection = kv.Value == SortDirection.Ascending
                    ? OrderByDirection.Ascending
                    : OrderByDirection.Descending
            });
        });
    }

    private void AddDynamicOrderByItems(List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys)
    {
        dynamicOrderBys.ForEach(kv =>
        {
            orderByItems.Add(new OrderByItem()
            {
                DataField = $@"
                    (select json_value
                        from d.form_field_instances
                        where business_id = form_instances.business_id
                          and version = form_instances.version
                          and form_code = form_instances.form_code
                          and form_version = form_instances.form_version
                          and is_deleted = 0
                          and code = N'{kv.Key}')",
                SortDirection = kv.Value == SortDirection.Ascending
                    ? OrderByDirection.Ascending
                    : OrderByDirection.Descending
            });
        });
    }

    private void AddFormDataDynamicWhereItems(
        List<WhereItem> whereItems, List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        formDataDynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var op = kv.Operator;
            var value = kv.Value;

            // 直接在GetFormDataDynamicQuerySql中处理whereItems的添加
            GetFormDataDynamicQuerySql(op, value, key, whereItems);
        });
    }

    /// <summary>
    /// 获取表单数据动态查询SQL条件并添加到whereItems中
    /// </summary>
    /// <param name="op">查询操作符</param>
    /// <param name="value">查询值</param>
    /// <param name="key">字段键</param>
    /// <param name="whereItems">WHERE条件项列表</param>
    private void GetFormDataDynamicQuerySql(FormDataQueryOperator op, string? value, string key,
        List<WhereItem> whereItems)
    {
        // 对于需要值的操作符，如果值为null或空，添加false条件
        if (string.IsNullOrEmpty(value) && (op == FormDataQueryOperator.Equal || op == FormDataQueryOperator.NotEqual ||
                                            op == FormDataQueryOperator.In))
        {
            whereItems.Add(WhereItemFactory.RawSql("1=0"));
            return;
        }

        switch (op)
        {
            case FormDataQueryOperator.Equal:
            {
                var sql = @"
                        EXISTS (
                            SELECT 1 FROM d.form_instance_datas fid
                            WHERE fid.business_id = form_instances.business_id
                                AND fid.version = form_instances.version
                                AND fid.form_code = form_instances.form_code
                                AND fid.form_version = form_instances.form_version
                                AND fid.code = {0}
                                AND fid.is_deleted = 0
                                AND fid.value = {1})";
                whereItems.Add(WhereItemFactory.RawSql(sql, key, value!));
                break;
            }

            case FormDataQueryOperator.NotEqual:
            {
                var sql = @"
                            EXISTS (
                                SELECT 1 FROM d.form_instance_datas fid
                                WHERE fid.business_id = form_instances.business_id
                                    AND fid.version = form_instances.version
                                    AND fid.form_code = form_instances.form_code
                                    AND fid.form_version = form_instances.form_version
                                    AND fid.code = {0}
                                    AND fid.is_deleted = 0
                                    AND fid.value != {1}
                            )
                            ";     
                whereItems.Add(WhereItemFactory.RawSql(sql, key, value!));
                break;
            }
            case FormDataQueryOperator.NotEqualOrNotExist:
            {
                var sql = @"
                        (
                            EXISTS (
                                SELECT 1 FROM d.form_instance_datas fid
                                WHERE fid.business_id = form_instances.business_id
                                    AND fid.version = form_instances.version
                                    AND fid.form_code = form_instances.form_code
                                    AND fid.form_version = form_instances.form_version
                                    AND fid.code = {0}
                                    AND fid.is_deleted = 0
                                    AND fid.value != {1}
                            )
                           OR
                           NOT EXISTS (
                                SELECT 1 FROM d.form_instance_datas fid
                                WHERE fid.business_id = form_instances.business_id
                                    AND fid.version = form_instances.version
                                    AND fid.form_code = form_instances.form_code
                                    AND fid.form_version = form_instances.form_version
                                    AND fid.code = {0}
                                    AND fid.value = {1}
                                    AND fid.is_deleted = 0
                            )
                        )";
                whereItems.Add(WhereItemFactory.RawSql(sql, key, value!));
                break;
            }

            case FormDataQueryOperator.Empty:
            {
                // 包含两种情况用OR连接：
                // 1. 存在记录但值为空
                // 2. 记录不存在
                var sql = @"
                        (
                            EXISTS (
                                SELECT 1 FROM d.form_instance_datas fid
                                WHERE fid.business_id = form_instances.business_id
                                    AND fid.version = form_instances.version
                                    AND fid.form_code = form_instances.form_code
                                    AND fid.form_version = form_instances.form_version
                                    AND fid.code = {0}
                                    AND fid.is_deleted = 0
                                    AND (fid.value IS NULL OR fid.value = '')
                            )
                            OR
                            NOT EXISTS (
                                SELECT 1 FROM d.form_instance_datas fid
                                WHERE fid.business_id = form_instances.business_id
                                    AND fid.version = form_instances.version
                                    AND fid.form_code = form_instances.form_code
                                    AND fid.form_version = form_instances.form_version
                                    AND fid.code = {0}
                                    AND fid.is_deleted = 0
                            )
                        )";
                whereItems.Add(WhereItemFactory.RawSql(sql, key, key));
                break;
            }

            case FormDataQueryOperator.NotEmpty:
            {
                var sql = @"
                        EXISTS (
                            SELECT 1 FROM d.form_instance_datas fid
                            WHERE fid.business_id = form_instances.business_id
                                AND fid.version = form_instances.version
                                AND fid.form_code = form_instances.form_code
                                AND fid.form_version = form_instances.form_version
                                AND fid.code = {0}
                                AND fid.is_deleted = 0
                                AND (fid.value IS NOT NULL AND fid.value != ''))";

                var allParams = new List<object> { key };
                whereItems.Add(WhereItemFactory.RawSql(sql, allParams.ToArray()));
                break;
            }

            case FormDataQueryOperator.In:
            {
                // 处理IN操作符，需要解析逗号分隔的值
                var values = value!.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(v => v.Trim().Trim('\'', '"')) // 移除可能的引号
                    .Where(v => !string.IsNullOrEmpty(v))
                    .ToArray();

                if (!values.Any())
                {
                    whereItems.Add(WhereItemFactory.RawSql("1=0")); // 空值返回false条件
                    break;
                }

                // 构建IN条件的占位符
                var placeholders = values.Select((_, index) => $"{{{index + 1}}}").ToArray();
                var inCondition = $"fid.value IN ({string.Join(", ", placeholders)})";

                var sql = @"
                        EXISTS (
                            SELECT 1 FROM d.form_instance_datas fid
                            WHERE fid.business_id = form_instances.business_id
                                AND fid.version = form_instances.version
                                AND fid.form_code = form_instances.form_code
                                AND fid.form_version = form_instances.form_version
                                AND fid.code = {0}
                                AND fid.is_deleted = 0
                                AND " + inCondition + ")";

                var allParams = new List<object> { key };
                allParams.AddRange(values.Cast<object>());
                whereItems.Add(WhereItemFactory.RawSql(sql, allParams.ToArray()));
                break;
            }

            default:
                whereItems.Add(WhereItemFactory.RawSql("1=0")); // 未知操作符返回false条件
                break;
        }
    }

    private void AddDynamicWhereItems(List<WhereItem> whereItems,
        Dictionary<string, DynamicFormQueryDto> dynamicQueries)
    {
        dynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var value = kv.Value;

            var filterValues = value.Values.Where(v => !string.IsNullOrEmpty(v)).ToList();

            if (!filterValues.Any())
                return;

            var parameters = new List<object>();
            var idx = 0;
            var sql = string.Join(value.Or ? " OR " : " AND ", value.Values.Select(item => $@"
                EXISTS (
                    SELECT 1 FROM d.form_field_instances ffi
                    WHERE ffi.business_id = form_instances.business_id
                        AND ffi.version = form_instances.version
                        AND ffi.form_code = form_instances.form_code
                        AND ffi.form_version = form_instances.form_version
                        AND ffi.code = {{{idx++}}}
                        AND ffi.json_value IS NOT NULL
                        AND ffi.json_value LIKE {{{idx++}}}
                        AND ffi.is_deleted = 0
                )"));

            value.Values.ForEach(item =>
            {
                parameters.Add(key);
                parameters.Add($"%{item}%");
            });

            whereItems.Add(WhereItemFactory.RawSql($"({sql})", parameters.ToArray()));
        });
    }
}
