using System.Linq.Expressions;
using XJ.Framework.DynamicForm.Domain.Entities;

namespace XJ.Framework.DynamicForm.Application.Contract.Interfaces;

/// <summary>
/// FormInstance 服务接口
/// </summary>
public interface IFormFieldInstanceService :
    IAppService<long, FormFieldInstanceDto, FormFieldInstanceQueryCriteria>,
    IEditableAppService<long, FormFieldInstanceOperationDto>
{
    Expression<Func<FormFieldInstanceEntity, bool>> GetEqualToFormInstanceEntityExpr(FormInstanceEntity formInstance);
    Expression<Func<FormFieldInstanceEntity, bool>> GetEqualToFormInstanceExpr(FormInstanceDto formInstanceDto);
    Expression<Func<FormFieldInstanceDto, bool>> GetDtoEqualToFormInstanceExpr(FormInstanceDto formInstanceDto);

    Expression<Func<FormFieldInstanceOperationDto, bool>> GetOperationDtoEqualToFormInstanceExpr(
        FormInstanceDto formInstanceDto);

    Task<List<FormFieldInstanceDto>> GetListByInstanceAsync(string formVersion, string formCode,
        IEnumerable<FormInstanceDto> instances, List<string> columns);

    Task<List<FormFieldInstanceDto>> LoadListAsync(FormInstanceDto formInstanceDto);


    Task<bool> SaveAsync(FormInstanceDto formInstanceDto,
        List<FormFieldInstanceOperationDto> sourceFieldInstances, //Dictionary<string, string?>? warnings = null,
        Dictionary<string, AnnotationValue?>? annotationValues = null);

    Task<bool> SaveAnnotationsWithValidationAsync(FormInstanceDto formInstance,
        Dictionary<string, AnnotationValue?>? annotationValues);

    Task<bool> SaveAnnotationsAsync(FormInstanceDto formInstance,
        Dictionary<string, AnnotationValue?>? annotationValues);

    // Task<bool> SaveWarningsAsync(FormInstanceDto formInstance, Dictionary<string, string>? warnings);

    // Task<Dictionary<string, string?>?> GetWarningsAsync(FormInstanceDto formInstance);
    Task<Dictionary<string, AnnotationValue?>> GetAnnotationsAsync(FormInstanceDto formInstance);

    Task<Dictionary<string, AnnotationValue?>> BuildAnnotationsAsync(List<FormFieldInstanceDto> formFieldInstance);
}
