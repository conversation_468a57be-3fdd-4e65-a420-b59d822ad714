namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

/// <summary>
/// FormInstanceData 操作 DTO
/// </summary>
public class FormInstanceDataOperationDto : BaseOperationDto
{
    /// <summary>
    /// 表单编码
    /// </summary>
    public string FormCode { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    public string FormVersion { get; set; } = null!;


    /// <summary>
    /// 当前实例版本
    /// </summary>
    public string Version { get; set; } = null!;

    /// <summary>
    /// 当前实例业务id
    /// </summary>
    public string BusinessId { get; set; } = null!;

    /// <summary>
    /// 数据编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 数据值
    /// </summary>
    public string? Value { get; set; }
}
