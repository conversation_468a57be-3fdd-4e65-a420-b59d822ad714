namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

public class FormCompareDto : List<FormCompareFieldDto>
{
}

public class FormCompareFieldDto
{
    public string FieldCode { get; set; } = null!;
    public string FieldName { get; set; } = null!;
    public bool Nested { get; set; }
    public List<FormCompareItemDto> Items { get; set; } = new List<FormCompareItemDto>();
}

public class FormCompareItemDto
{
    public int? RowIndex { get; set; }
    public string? UserInput { get; set; }
    public string? Match { get; set; }
    public double Score { get; set; }
    public string? Summary { get; set; }

    public int ReferenceCount { get; set; }
    public List<FormCompareReferenceDto> References { get; set; } = new List<FormCompareReferenceDto>();
}

public class FormCompareReferenceDto
{
    public string FileCode { get; set; } = null!;
    public string ReferenceContent { get; set; } = null!;
    public List<FormCompareReferenceAreaDto> Areas { get; set; } = new List<FormCompareReferenceAreaDto>();
}

public class FormCompareReferenceAreaDto
{
    public int PageNumber { get; set; }
    public double X0 { get; set; }
    public double X1 { get; set; }
    public double Y0 { get; set; }
    public double Y1 { get; set; }
}