using XJ.Framework.DynamicForm.Application.Contract.Extensions;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.DynamicForm.Application.Contract.Services;

public static class FormDefinitionHelper
{
    public async static Task<Dictionary<string, MultiTypeValue?>?> GetFieldValuesAsync(
        FormDefinitionDto formDefinitionDto)
    {
        var fieldValues = new Dictionary<string, MultiTypeValue?>();

        formDefinitionDto.Groups.ForEach(group =>
        {
            group.Fields.ForEach(field =>
            {
                fieldValues.Add(field.Code, field.Value);
            });
        });

        return await Task.FromResult(fieldValues);
    }

    public async static Task<Dictionary<string, MultiTypeValue?>?> GetFieldPreviousValuesAsync(
        FormDefinitionDto formDefinitionDto)
    {
        var fieldValues = new Dictionary<string, MultiTypeValue?>();

        formDefinitionDto.Groups.ForEach(group =>
        {
            group.Fields.ForEach(field =>
            {
                fieldValues.Add(field.Code, field.PreviousValue);
            });
        });

        return await Task.FromResult(fieldValues);
    }

    public static AnnotationValue? CombineAnnotation(AnnotationValue? source, AnnotationValue? target)
    {
        var result = source;
        if (target != null)
        {
            if (target.KeyValue != null)
            {
                result = new AnnotationValue() { KeyValue = new Dictionary<string, string?>() };
                target.KeyValue.ForEach(targetKv =>
                {
                    if (result!.KeyValue!.ContainsKey(targetKv.Key))
                    {
                        result.KeyValue[targetKv.Key] = targetKv.Value;
                    }
                    else
                    {
                        result.KeyValue.Add(targetKv.Key, targetKv.Value);
                    }
                });
            }
            else
            {
                result ??= new AnnotationValue() { ObjectArray = new List<Dictionary<string, string?>?>() };
                target.ObjectArray?.ForEach((idx, targetRow) =>
                {
                    if (result?.ObjectArray?.Count - 1 < idx)
                    {
                        result?.ObjectArray?.Add(new Dictionary<string, string?>());
                    }

                    var row = result?.ObjectArray![idx];
                    targetRow?.ForEach(targetRowKv =>
                    {
                        if (row!.ContainsKey(targetRowKv.Key))
                        {
                            row[targetRowKv.Key] = targetRowKv.Value;
                        }
                        else
                        {
                            row.Add(targetRowKv.Key, targetRowKv.Value);
                        }
                    });
                });
            }
        }

        return result;
    }

    public async static Task<Dictionary<string, AnnotationValue?>?> CombineAnnotationsAsync(
        Dictionary<string, AnnotationValue?>? source, Dictionary<string, AnnotationValue?>? target)
    {
        if (source == null && target == null)
        {
            return null;
        }

        // 合并source和target的字典
        var combined = new Dictionary<string, AnnotationValue?>();
        if (source != null)
        {
            foreach (var kvp in source)
            {
                combined[kvp.Key] = kvp.Value;
            }
        }

        if (target != null)
        {
            foreach (var kvp in target)
            {
                if (combined.ContainsKey(kvp.Key))
                {
                    combined[kvp.Key] = kvp.Value;
                }
                else
                {
                    combined.Add(kvp.Key, kvp.Value);
                }
            }
        }

        combined.ForEach(kv =>
        {
            var targetAnnotation = target?[kv.Key];

            combined[kv.Key] = CombineAnnotation(kv.Value, targetAnnotation);
        });
        return await Task.FromResult(combined);
    }


    public async static Task<Dictionary<string, AnnotationValue?>?> GetAnnotationsAsync(
        FormDefinitionDto formDefinitionDto)
    {
        var annotations = new Dictionary<string, AnnotationValue?>();

        formDefinitionDto.Groups.ForEach(group =>
        {
            group.Fields.ForEach(field =>
            {
                annotations.Add(field.Code, field.Annotations);

                field.Fields.ForEach(child =>
                {
                    annotations.Add(child.Code, child.Annotations);
                });
            });
        });

        return await Task.FromResult(annotations);
    }

    public async static Task SetAnnotationsAsync(FormDefinitionDto definition,
        Dictionary<string, AnnotationValue?> annotations)
    {
        definition.Groups.ForEach(group =>
        {
            group.Fields.ForEach(field =>
            {
                if (annotations?.TryGetValue(field.Code, out var annotation) ?? false)
                {
                    field.Annotations = annotation;
                }

                field.Fields.ForEach(child =>
                {
                    if (annotations?.TryGetValue(child.Code, out var childAnnotation) ?? false)
                    {
                        child.Annotations = childAnnotation;
                    }
                });
            });
        });
        await Task.CompletedTask;
    }


    // public async static Task SetWarningsAsync(FormDefinitionDto definition, Dictionary<string, string?>? warnings)
    // {
    //     definition.Groups.ForEach(group =>
    //     {
    //         group.Fields.ForEach(field =>
    //         {
    //             if (warnings?.TryGetValue(field.Code, out var warning) ?? false)
    //             {
    //                 field.Warning = warning;
    //             }
    //         });
    //     });
    //     await Task.CompletedTask;
    // }

    /// <summary>
    /// 表单定义填充值
    /// </summary>
    /// <param name="definition"></param>
    /// <param name="dataJson"></param>
    public async static Task SetFieldValueAsync(FormDefinitionDto definition,
        Dictionary<string, MultiTypeValue?>? dataJson)
    {
        if (dataJson != null)
        {
            await definition.Groups.ForEachAsync(async group =>
            {
                await group.Fields.ForEachAsync(async field =>
                {
                    dataJson.TryGetValue(field.Code, out var fieldDataValue);

                    var fieldValue = field.Value;

                    if (field.Type == "multiSubForm" && field.Fields.Any())
                    {
                        var value = new MultiTypeValue() { ObjectArray = [] };

                        // 取最多的行
                        // var maxRowCount = Math.Max(fieldValue?.ObjectArray?.Count ?? 0,
                        //     fieldDataValue?.ObjectArray?.Count ?? 0);

                        var maxRowCount = fieldDataValue?.ObjectArray?.Count ?? 0;

                        // 多行子表单 按数组处理
                        for (var idx = 0; idx < maxRowCount; idx++)
                        {
                            var row = new Dictionary<string, object?>();

                            var rowValue = fieldValue?.ObjectArray?.Count - 1 > idx
                                ? fieldValue!.ObjectArray![idx]
                                : null;

                            var rowDataValue = fieldDataValue?.ObjectArray?.Count - 1 >= idx
                                ? fieldDataValue!.ObjectArray![idx]
                                : null;

                            field.Fields.ForEach(childField =>
                            {
                                object? rowChildValue = null;
                                rowValue?.TryGetValue(childField.Code, out rowChildValue);

                                object? rowChildDataValue = null;
                                rowDataValue?.TryGetValue(childField.Code, out rowChildDataValue);

                                childField.Value =
                                    FieldDtoExtensions.RefactMultiTypeValue(rowChildDataValue ?? rowChildValue);

                                row.Add(childField.Code, childField.Value);
                            });
                            value.ObjectArray.Add(row);
                        }

                        field.Value = value;
                    }
                    else if (field.Type == "subForm" && field.Fields.Any())
                    {
                        var value = new MultiTypeValue() { KeyValue = new Dictionary<string, object?>() };
                        // 单行子表单 按字典处理
                        field.Fields.ForEach(childField =>
                        {
                            object? childFieldValue = null;
                            object? childDataFieldValue = null;

                            fieldValue?.KeyValue?.TryGetValue(childField.Code, out childFieldValue);

                            fieldDataValue?.KeyValue?.TryGetValue(childField.Code, out childDataFieldValue);

                            childField.Value =
                                FieldDtoExtensions.RefactMultiTypeValue(childDataFieldValue ?? childFieldValue);

                            value.KeyValue.Add(childField.Code, childField.Value);
                        });
                        field.Value = value;
                    }
                    else
                    {
                        // 普通字段
                        if (fieldDataValue != null)
                        {
                            field.Value = fieldDataValue;
                        }
                    }

                    await Task.CompletedTask;
                });
            });
        }
    }

    public async static Task RemoveNamedAnnotationsAsync(Dictionary<string, AnnotationValue?>? annotationValues,
        params string[] annotationKeys)
    {
        annotationValues?.ForEach(kv =>
        {
            if (kv.Value != null)
            {
                if (kv.Value.KeyValue != null)
                {
                    annotationKeys.ForEach(annotationKey =>
                    {
                        kv.Value.KeyValue.Remove(annotationKey);
                    });
                }
                else if (kv.Value.ObjectArray != null)
                {
                    kv.Value.ObjectArray.ForEach((idx, row) =>
                    {
                        annotationKeys.ForEach(annotationKey =>
                        {
                            row?.Remove(annotationKey);
                        });
                    });
                }
            }
        });
        await Task.CompletedTask;
    }
}
