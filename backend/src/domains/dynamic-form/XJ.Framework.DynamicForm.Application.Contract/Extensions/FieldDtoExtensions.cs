using System.Text.Json;

namespace XJ.Framework.DynamicForm.Application.Contract.Extensions;

public static class FieldDtoExtensions
{
    public static void ConvertToSubformAnnotation(this FieldDto field,
        List<Tuple<int, string?, Dictionary<string, string>>> texts, string annotationKey)
    {
        if (texts.Count == 0)
        {
            return;
        }

        field.Annotations ??= new AnnotationValue() { ObjectArray = new List<Dictionary<string, string?>?>() };

        foreach (var text in texts.OrderBy(q => q.Item1))
        {
            var rowIndex = text.Item1;

            if (field.Annotations?.ObjectArray?.Count - 1 < rowIndex)
            {
                field.Annotations?.ObjectArray?.Insert(rowIndex, new Dictionary<string, string?>());
            }

            if (field.Annotations!.ObjectArray![rowIndex]!.ContainsKey(annotationKey))
            {
                field.Annotations!.ObjectArray![rowIndex]![annotationKey] = text.Item2;
            }
            else
            {
                field.Annotations!.ObjectArray?[rowIndex]!.Add(annotationKey, text.Item2);
            }


            foreach (var child in field.Fields)
            {
                text.Item3.TryGetValue(child.Code, out var annotation);
                child.Annotations ??= new AnnotationValue() { ObjectArray = new List<Dictionary<string, string?>?>() };

                if (child.Annotations?.ObjectArray?.Count - 1 < rowIndex)
                {
                    child.Annotations?.ObjectArray?.Insert(rowIndex, new Dictionary<string, string?>());
                }
                else
                {
                    child.Annotations!.ObjectArray![rowIndex] ??= new Dictionary<string, string?>();
                }

                if (child.Annotations!.ObjectArray![rowIndex]!.ContainsKey(annotationKey))
                {
                    child.Annotations!.ObjectArray![rowIndex]![annotationKey] = annotation;
                }
                else
                {
                    child.Annotations!.ObjectArray![rowIndex]!.Add(annotationKey, annotation);
                }
            }
        }
    }

    public static void ConvertToAnnotation(this FieldDto field, string? text, string annotationKey)
    {
        if (text == null)
            return;

        field.Annotations ??= new AnnotationValue() { KeyValue = new Dictionary<string, string?>() };

        if (field.Annotations.KeyValue!.ContainsKey(annotationKey))
        {
            field.Annotations.KeyValue[annotationKey] = text;
        }
        else
        {
            field.Annotations.KeyValue.Add(annotationKey, text);
        }
    }

    public static MultiTypeValue? RefactMultiTypeValue(object? input)
    {
        return JsonSerializer.Deserialize<MultiTypeValue>(JsonSerializer.Serialize(input));
    }

    private static string FormatFileSize(this FieldDto field, long fileSize)
    {
        // Convert file size to human-readable format
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        while (fileSize >= 1024 && order < sizes.Length - 1)
        {
            order++;
            fileSize /= 1024;
        }

        return $"{fileSize:0.##} {sizes[order]}";
    }

    public static List<string> GetSubformDisplayValue(this FieldDto field)
    {
        switch (field.Type)
        {
            case "subForm":
            {
                var children = field.Fields;
                var childDisplayValues = children.Select(child =>
                {
                    var code = child.Code;
                    var name = child.LabelZh;
                    var value = child.GetDisplayValue();
                    return $"{name}:{value}";
                });
                return [string.Join("\t", childDisplayValues)];
            }
            case "multiSubForm":
            {
                var lines = new List<string>();
                field.Value?.ObjectArray?.ForEach(row =>
                {
                    var childDisplayValues = field.Fields.Select(child =>
                    {
                        var code = child.Code;
                        var name = child.LabelZh;
                        var value = child.GetDisplayValue(RefactMultiTypeValue(row[code]));
                        return $"{name}:{value}";
                    });
                    lines.Add(string.Join("\t", childDisplayValues));
                });
                return lines;
            }
        }

        return new List<string>();
    }

    private static string? TryGetValue(this Dictionary<string, object?>? dictionary, string key)
    {
        return dictionary?.TryGetValue(key, out var value) ?? false ? value?.ToString() : null;
    }

    public static string? GetDisplayValue(this FieldDto field, MultiTypeValue? multiTypeValue)
    {
        switch (field.Type)
        {
            case "int_range":
            {
                var min = multiTypeValue?.KeyValue.TryGetValue("min");
                var max = multiTypeValue?.KeyValue.TryGetValue("max");
                if (min != null && max != null)
                {
                    return $"{min}~{max}";
                }

                return null;
            }
            case "textarea":
            case "input":
            case "date":
                return multiTypeValue?.StringValue;
            case "checkbox":
            case "radio":
            case "select":
            {
                var stringValue = multiTypeValue?.StringValue;
                string? optionStringValue = null;
                if (stringValue != null)
                {
                    optionStringValue =
                        field.Options?.FirstOrDefault(o => o.Value == multiTypeValue?.StringValue)?.LabelZh;
                }

                return optionStringValue ?? stringValue;
            }
            case "unit_select":
            {
                var stringValue = multiTypeValue?.KeyValue.TryGetValue("unit");
                string? optionStringValue = null;
                if (stringValue != null)
                {
                    optionStringValue =
                        field.Options?.FirstOrDefault(o => o.Value == multiTypeValue?.StringValue)?.LabelZh;
                }

                var unit = optionStringValue ?? stringValue;
                var value = multiTypeValue?.KeyValue.TryGetValue("value");

                if (unit != null && value != null)
                {
                    return $"{value} {unit}";
                }

                return null;
            }
            case "file":
            {
                var fileName = multiTypeValue?.KeyValue.TryGetValue("fileName");
                var fileSize = multiTypeValue?.KeyValue.TryGetValue("fileSize");
                if (fileName != null && fileSize != null)
                {
                    return $"{fileName} ({field.FormatFileSize(Convert.ToInt64(fileSize))})";
                }

                return null;
            }
            case "text_multilang":
            case "textarea_multilang":
            {
                return multiTypeValue?.KeyValue.TryGetValue("zh");
            }
            case "date_range":
            {
                var start = multiTypeValue?.KeyValue.TryGetValue("start");
                var end = multiTypeValue?.KeyValue.TryGetValue("end");
                if (start != null && end != null)
                {
                    return $"{start}~{end}";
                }

                return null;
            }
            default:
                throw new NotImplementedException($"Field type {field.Type} is not implemented.");
        }
    }

    public static string? GetDisplayValue(this FieldDto field)
    {
        return field.GetDisplayValue(field.Value);
    }

    public static void ConvertToSubformValue(this FieldDto field, List<Tuple<int, Dictionary<string, string>>>? texts,
        string language)
    {
        if (texts == null || !texts.Any())
        {
            return;
        }

        switch (field.Type)
        {
            case "subForm":
            {
                field.Value ??= new MultiTypeValue() { KeyValue = new Dictionary<string, object?>() };
                var first = texts.FirstOrDefault();
                if (first != null)
                {
                    var subformValue = new Dictionary<string, object?>();
                    foreach (var child in field.Fields)
                    {
                        first.Item2.TryGetValue(child.Code, out var value);

                        // if (value != null)
                        // {
                        //     child.ConvertToValue(value, language);
                        // }


                        var childValue = GetValue(child, value, language);

                        subformValue.Add(child.Code, childValue);
                    }

                    field.Value.KeyValue = subformValue;
                }
            }
                break;
            case "multiSubForm":
            {
                field.Value ??= new MultiTypeValue() { ObjectArray = new List<Dictionary<string, object?>>() };
                var subformValue = field.Value;
                foreach (var text in texts.OrderBy(q => q.Item1))
                {
                    var rowIndex = text.Item1;
                    var rowValue = text.Item2;
                    if (rowValue.Count == 0)
                    {
                        return;
                    }

                    if (subformValue?.ObjectArray?.Count - 1 < rowIndex)
                    {
                        subformValue?.ObjectArray?.Insert(rowIndex, new Dictionary<string, object?>());
                    }


                    foreach (var child in field.Fields)
                    {
                        text.Item2.TryGetValue(child.Code, out var value);
                        // if (value != null)
                        // {
                        //     child.ConvertToValue(value, language);
                        // }

                        var childValue = GetValue(child, value, language);

                        if (subformValue!.ObjectArray![rowIndex].ContainsKey(child.Code))
                        {
                            subformValue!.ObjectArray![rowIndex][child.Code] = childValue;
                        }
                        else
                        {
                            subformValue!.ObjectArray![rowIndex].Add(child.Code, childValue);
                        }
                    }
                }

                field.Value = subformValue;
            }
                break;
        }
    }

    public static void ConvertToValue(this FieldDto field, string? text, string language)
    {
        var value = GetValue(field, text, language);

        if (value != null)
        {
            if (field.Type == "text_multilang" || field.Type == "textarea_multilang")
            {
                field.Value!.KeyValue![language] = value.KeyValue![language];
            }
            else
            {
                field.Value = value;
            }
        }
    }

    public static MultiTypeValue? GetValue(FieldDto field, string? text, string language)
    {
        if (text == null)
            return null;

        switch (field.Type)
        {
            case "int_range":
            {
                var splits = text.Split('~', StringSplitOptions.RemoveEmptyEntries);

                if (splits.Length == 2)
                {
                    var min = splits[0];
                    var max = splits[1];
                    return new MultiTypeValue()
                    {
                        KeyValue = new Dictionary<string, object?>()
                        {
                            { "min", min },
                            { "max", max }
                        }
                    };
                }
            }
                break;
            case "textarea":
            {
                return new MultiTypeValue() { StringValue = text };
            }
            case "input":
            {
                return new MultiTypeValue() { StringValue = text };
            }
            case "checkbox":
            case "radio":
            case "select":
            {
                var option =
                    field.Options!.FirstOrDefault(q => (language == "zh" ? q.LabelZh : q.LabelEn).Equals(text));
                if (option != null)
                {
                    return new MultiTypeValue() { StringValue = option.Value };
                }
            }
                break;
            case "date":
            {
                return new MultiTypeValue() { StringValue = text };
            }
            case "unit_select":
            {
                var splits = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (splits.Length == 2)
                {
                    var value = splits[0];
                    var unit = splits[1];
                    var option =
                        field.Options!.FirstOrDefault(q => (language == "zh" ? q.LabelZh : q.LabelEn).Equals(unit));
                    if (option != null)
                    {
                        return new MultiTypeValue()
                        {
                            KeyValue = new Dictionary<string, object?>()
                            {
                                { "value", value },
                                { "unit", option.Value }
                            }
                        };
                    }
                }
            }
                break;
            case "file":
            {
            }
                break;
            case "text_multilang":
            case "textarea_multilang":
            {
                var en = "";
                var zh = "";

                if (language == "zh")
                {
                    zh = text;
                }
                else
                {
                    en = text;
                }

                return new MultiTypeValue()
                {
                    KeyValue = new Dictionary<string, object?>()
                    {
                        { "en", en },
                        { "zh", zh }
                    }
                };
            }
            case "date_range":
            {
                var splits = text.Split('~', StringSplitOptions.RemoveEmptyEntries);
                if (splits.Length == 2)
                {
                    var start = splits[0];
                    var end = splits[1];
                    return new MultiTypeValue()
                    {
                        KeyValue = new Dictionary<string, object?>()
                        {
                            {
                                "start", start
                            },
                            {
                                "end", end
                            }
                        }
                    };
                }
            }
                break;
            default:
                throw new NotImplementedException($"Field type {field.Type} is not implemented.");
        }

        return null;
    }
}
