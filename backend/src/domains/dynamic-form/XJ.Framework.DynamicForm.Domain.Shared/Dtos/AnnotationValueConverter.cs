using System.Text.Json;
using System.Text.Json.Serialization;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;

namespace XJ.Framework.DynamicForm.Application.JsonConverters;

public class AnnotationValueConverter : JsonConverter<AnnotationValue>
{
    public override AnnotationValue? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var result = new AnnotationValue();

        if (reader.TokenType == JsonTokenType.StartArray)
        {
            var list = JsonSerializer.Deserialize<List<Dictionary<string, string?>?>>(ref reader, options);
            result.ObjectArray = list;
        }
        else if (reader.TokenType == JsonTokenType.StartObject)
        {
            var dict = JsonSerializer.Deserialize<Dictionary<string, string?>>(ref reader, options);
            result.KeyValue = dict;
        }
        else if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }
        else
        {
            throw new JsonException("Unknown type for MultiTypeValue");
        }

        return result;
    }

    public override void Write(Utf8JsonWriter writer, AnnotationValue value, JsonSerializerOptions options)
    {
        if (value.KeyValue != null)
        {
            JsonSerializer.Serialize(writer, value.KeyValue, options);
        }
        else if (value.ObjectArray != null)
        {
            JsonSerializer.Serialize(writer, value.ObjectArray, options);
        }
        else
        {
            writer.WriteNullValue();
        }
    }
}
