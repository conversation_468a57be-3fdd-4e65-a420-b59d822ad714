namespace XJ.Framework.DynamicForm.Domain.Shared.Dtos;

/// <summary>
/// FormFieldInstance DTO
/// </summary>
public class FormFieldInstanceDto : BaseDto<long>
{
    /// <summary>
    /// 表单编码
    /// </summary>
    public string FormCode { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    public string FormVersion { get; set; } = null!;


    /// <summary>
    /// 当前实例版本
    /// </summary>
    public string Version { get; set; } = null!;

    /// <summary>
    /// 当前实例业务id
    /// </summary>
    public string BusinessId { get; set; } = null!;

    /// <summary>
    /// 字段编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 字段实例值
    /// </summary>
    public string? JsonValue { get; set; }

    /// <summary>
    /// 批注
    /// </summary>
    public string? Annotations { get; set; }

    /// <summary>
    /// 字段实例值显示
    /// </summary>
    public string? Display { get; set; }

    // /// <summary>
    // /// 字段驳回时的批注信息
    // /// </summary>
    // public string? Warning { get; set; }

    /// <summary>
    /// 上级字段编码
    /// </summary>
    public string? ParentCode { get; set; }

    /// <summary>
    /// 字段类型
    /// </summary>
    public string Type { get; set; } = null!;

    /// <summary>
    /// 值所在行 该值仅在多行子表单时有效
    /// </summary>
    public int? RowIndex { get; set; }
}
