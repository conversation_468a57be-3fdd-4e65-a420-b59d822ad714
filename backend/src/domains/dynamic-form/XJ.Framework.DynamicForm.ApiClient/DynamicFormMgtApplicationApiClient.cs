using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.DynamicForm.Domain.Shared.Consts;
using XJ.Framework.Library.Domain.Shared.Attributes;

namespace XJ.Framework.Files.ApiClient;

[NameModule(MgtModuleConst.ModuleId)]
public class DynamicFormMgtApplicationApiClient : BaseApplicationApiClient
{
    private readonly DynamicFormApiClientHelper _helper;

    public DynamicFormMgtApplicationApiClient(IServiceProvider serviceProvider, HttpClient httpClient
    ) : base(serviceProvider, httpClient)
    {
        _helper = serviceProvider.GetRequiredService<DynamicFormApiClientHelper>();
    }


    public async Task<bool> SetFormDataAsync(string businessId, string version, Dictionary<string, string?> formData)
    {
        var url = $"{BaseUrl}/FormInstance/app/instance/formData/{businessId}/{version}";
        await InternalPutAsync<bool>(url, formData);
        return true;
    }
}
