namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 角色 实体
/// </summary>
[Table("roles", Schema = "r")]
[SoftDeleteIndex("IX_Roles_Code", nameof(Code), IsUnique = true)]
public class RoleEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 角色编码
    /// </summary>
    [Column("code")]
    [StringLength(100)]
    public required string Code { get; set; } = null!;

    /// <summary>
    /// 角色名称
    /// </summary>
    [Column("name")]
    [StringLength(200)]
    public required string Name { get; set; } = null!;

    /// <summary>
    /// 角色类型
    /// </summary>
    /// <remarks>
    /// General: 通用角色
    /// Position: 岗位角色
    /// System: 系统角色
    /// </remarks>
    [Column("type")]
    public required RoleType Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    /// <remarks>
    /// Enabled: 启用
    /// Disabled: 禁用
    /// </remarks>
    [Column("status")]
    public required CommonStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [Column("description")]
    [StringLength(1000)]
    public string? Description { get; set; }
}