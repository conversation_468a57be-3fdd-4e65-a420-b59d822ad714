using Microsoft.Extensions.Options;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;

namespace XJ.Framework.Rbac.EntityFrameworkCore;

public class RbacDbContext : BaseDbContext
{
    public RbacDbContext(DbContextOptions<RbacDbContext> options, IServiceProvider serviceProvider) :
        base(options, serviceProvider)
    {
    }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
    }
}
