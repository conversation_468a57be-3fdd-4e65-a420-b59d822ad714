using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.Rbac.EntityFrameworkCore;

public static class InitialDataSeeder
{
    public static void Seed(this ModelBuilder modelBuilder)
    {
        // 添加用户种子数据
        SeedUsers(modelBuilder);

        // 添加组织机构种子数据
        SeedOrganizations(modelBuilder);

        // 添加角色种子数据
        SeedRoles(modelBuilder);

        // 添加权限种子数据
        SeedPermissions(modelBuilder);

        SeedPositions(modelBuilder);
    }

    private static void SeedPositions(ModelBuilder modelBuilder)
    {
        var now = DateTimeOffset.UtcNow;
        modelBuilder.Entity<PositionEntity>().HasData(new
            {
                Key = 1L,
                RoleId = 2L,
                Code = "CHECKER_LEVEL_1",
                Name = "总审核员",
                Status = CommonStatus.Enabled,
                Description = "",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = 2L,
                RoleId = 3L,
                Code = "CHECKER_LEVEL_2",
                Name = "高级审核员",
                Status = CommonStatus.Enabled,
                Description = "",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = 3L,
                RoleId = 4L,
                Code = "CHECKER_LEVEL_3",
                Name = "中级审核员",
                Status = CommonStatus.Enabled,
                Description = "",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = 4L,
                RoleId = 5L,
                Code = "CHECKER_LEVEL_4",
                Name = "初级审核员",
                Status = CommonStatus.Enabled,
                Description = "",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }
        );
    }

    private static void SeedPermissions(ModelBuilder modelBuilder)
    {
        var now = DateTimeOffset.UtcNow;


        // 菜单和权限种子数据
        var permissions = new List<PermissionEntity>
        {
            new()
            {
                Key = 111,
                AppId = null,
                AppCode = "messaging-mgt",
                ParentId = 11,
                Code = "MessageProvider",
                Name = "服务商管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "服务商管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 112,
                AppId = null,
                AppCode = "messaging-mgt",
                ParentId = 11,
                Code = "MessageAccount",
                Name = "账户管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "账户管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            new()
            {
                Key = 113,
                AppId = null,
                AppCode = "messaging-mgt",
                ParentId = 11,
                Code = "MessageTemplate",
                Name = "模板管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "模板管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            new()
            {
                Key = 114,
                AppId = null,
                AppCode = "messaging-mgt",
                ParentId = 11,
                Code = "MessageSendRecords",
                Name = "发送记录",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "发送记录页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 11,
                AppId = null,
                AppCode = "messaging-mgt",
                ParentId = null,
                Code = "Messaging",
                Name = "消息管理",
                Type = PermissionType.Directory,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "消息管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },


            // 系统管理
            new()
            {
                Key = 2,
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = null,
                Code = "system",
                Name = "系统管理",
                Type = PermissionType.Directory,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "系统管理模块",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 用户管理
            new()
            {
                Key = 10,
                AppId = null,
                AppCode = "logging-mgt",
                ParentId = 2,
                Code = "system:logging",
                Name = "日志管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "日志管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 3,
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 2,
                Code = "system:user",
                Name = "用户管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "用户管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 角色管理
            new()
            {
                Key = 4,
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 2,
                Code = "system:role",
                Name = "角色管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "角色管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 组织管理
            new()
            {
                Key = 5,
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 2,
                Code = "system:organization",
                Name = "组织管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "组织管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },


            // 岗位管理
            new()
            {
                Key = 7,
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 2,
                Code = "system:position",
                Name = "岗位管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "岗位管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 8,
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 2,
                Code = "system:permission",
                Name = "权限管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "权限管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 存储管理模块（files-mgt）
            new()
            {
                Key = 10001,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = null,
                Code = "files",
                Name = "存储管理",
                Type = PermissionType.Directory,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "存储管理模块",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10002,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10001,
                Code = "files:storage",
                Name = "存储管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "存储管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10003,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10001,
                Code = "files:file-type",
                Name = "文件类型管理",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "文件类型管理页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 用户管理按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 3,
                Code = "system:user:add",
                Name = "添加用户",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "添加用户按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 3,
                Code = "system:user:edit",
                Name = "编辑用户",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑用户按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 3,
                Code = "system:user:reset-password",
                Name = "重置用户密码",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "重置用户密码按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 3,
                Code = "system:user:disable",
                Name = "禁用用户",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "禁用用户按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 3,
                Code = "system:user:enable",
                Name = "启用用户",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "启用用户按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 3,
                Code = "system:user:role",
                Name = "绑定角色",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "绑定角色按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 3,
                Code = "system:user:org",
                Name = "绑定组织架构",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "绑定组织架构按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 角色管理按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 4,
                Code = "system:role:add",
                Name = "添加角色",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "添加角色按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 4,
                Code = "system:role:edit",
                Name = "编辑角色",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑角色按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 4,
                Code = "system:role:delete",
                Name = "删除角色",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "删除角色按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 4,
                Code = "system:role:permission",
                Name = "分配角色权限",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "分配角色权限按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 组织管理按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 5,
                Code = "system:organization:add",
                Name = "添加组织",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "添加组织按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 5,
                Code = "system:organization:edit",
                Name = "编辑组织",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑组织按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 5,
                Code = "system:organization:delete",
                Name = "删除组织",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "删除组织按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },


            // 岗位管理按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 7,
                Code = "system:position:add",
                Name = "添加岗位",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "添加岗位按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 7,
                Code = "system:position:edit",
                Name = "编辑岗位",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑岗位按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 7,
                Code = "system:position:delete",
                Name = "删除岗位",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "删除岗位按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 权限管理


            // 权限管理按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 8,
                Code = "system:permission:add",
                Name = "添加权限",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "添加权限按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 8,
                Code = "system:permission:edit",
                Name = "编辑权限",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑权限按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "rbac-mgt",
                ParentId = 8,
                Code = "system:permission:delete",
                Name = "删除权限",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "删除权限按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 9,
                AppId = null,
                AppCode = null,
                ParentId = null,
                Code = "api-permissions-root",
                Name = "Api权限",
                Type = PermissionType.Directory,
                Path = null,
                Method = null,
                SortOrder = 999,
                Status = CommonStatus.Enabled,
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
            },

            // 存储管理按钮
            new()
            {
                Key = 10011,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10002, // 存储管理菜单
                Code = "files:storage:add",
                Name = "新增存储",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "新增存储按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10012,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10002,
                Code = "files:storage:edit",
                Name = "编辑存储",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑存储按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10013,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10002,
                Code = "files:storage:delete",
                Name = "删除存储",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "删除存储按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 文件类型管理按钮
            new()
            {
                Key = 10021,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10003, // 文件类型管理菜单
                Code = "files:file-type:add",
                Name = "新增文件类型",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "新增文件类型按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10022,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10003,
                Code = "files:file-type:edit",
                Name = "编辑文件类型",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑文件类型按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10023,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10003,
                Code = "files:file-type:delete",
                Name = "删除文件类型",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "删除文件类型按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 类型存储关系按钮
            new()
            {
                Key = 10031,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10003, // 类型存储关系菜单
                Code = "files:file-type:storage:add",
                Name = "新增类型存储关系",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "新增类型存储关系按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10032,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10003,
                Code = "files:file-type:storage",
                Name = "类型存储关系配置",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "类型存储关系配置按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 10033,
                AppId = null,
                AppCode = "files-mgt",
                ParentId = 10003,
                Code = "files:file-type:storage:delete",
                Name = "删除类型存储关系",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "删除类型存储关系按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            // // API权限
            // new PermissionEntity
            // {
            //     Key = IdGenerator.NextId(),
            //     AppId = null,
            //     AppCode = "rbac-mgt",
            //     ParentId = 9,
            //     Code = "api-permissions",
            //     Name = "加载终结点",
            //     Type = PermissionType.Api,
            //     Path = "permission/api-permissions",
            //     Method = HttpMethod.GET,
            //     SortOrder = 0,
            //     Status = CommonStatus.Enabled,
            //     CreatedBy = "system",
            //     CreatedTime = DateTimeOffset.UtcNow,
            //     LastModifiedBy = "system",
            //     LastModifiedTime = DateTimeOffset.UtcNow,
            // }

            // 表单管理目录
            new()
            {
                Key = 20001,
                AppId = null,
                AppCode = "dynamic-form-mgt",
                ParentId = null,
                Code = "form-management",
                Name = "表单管理",
                Type = PermissionType.Directory,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "表单管理模块",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            // 表单定义菜单
            new()
            {
                Key = 20002,
                AppId = null,
                AppCode = "dynamic-form-mgt",
                ParentId = 20001,
                Code = "form-management:form-list",
                Name = "表单定义",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "表单定义页面",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            // 新增表单按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "dynamic-form-mgt",
                ParentId = 20002,
                Code = "form:defined:add",
                Name = "新增表单",
                Type = PermissionType.Button,
                SortOrder = 1,
                Status = CommonStatus.Enabled,
                Description = "新增表单按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            // 编辑表单按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "dynamic-form-mgt",
                ParentId = 20002,
                Code = "form:defined:edit",
                Name = "编辑表单",
                Type = PermissionType.Button,
                SortOrder = 2,
                Status = CommonStatus.Enabled,
                Description = "编辑表单按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            // 表单设计按钮（view）
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "dynamic-form-mgt",
                ParentId = 20002,
                Code = "form:defined:design",
                Name = "表单设计",
                Type = PermissionType.Button,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "表单设计按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = 20003,
                AppId = null,
                AppCode = "dynamic-form-mgt",
                ParentId = 20001,
                Code = "form-management:design",
                Name = "表单设计",
                Type = PermissionType.Menu,
                SortOrder = 3,
                Status = CommonStatus.Enabled,
                Description = "表单设计",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            // 删除表单按钮
            new()
            {
                Key = IdGenerator.NextId(),
                AppId = null,
                AppCode = "dynamic-form-mgt",
                ParentId = 20002,
                Code = "form:defined:delete",
                Name = "删除表单",
                Type = PermissionType.Button,
                SortOrder = 4,
                Status = CommonStatus.Enabled,
                Description = "删除表单按钮",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
        };

        modelBuilder.Entity<PermissionEntity>().HasData(permissions);

        // 为系统管理员角色赋予所有权限
        var rolePermissions = permissions.Select(p => new RolePermissionEntity
        {
            Key = IdGenerator.NextId(),
            RoleId = 1, // 系统管理员角色ID
            PermissionId = p.Key, // 权限ID
            CreatedBy = "system",
            CreatedTime = now,
            LastModifiedBy = "system",
            LastModifiedTime = now,
            Deleted = false
        });

        modelBuilder.Entity<RolePermissionEntity>().HasData(rolePermissions);


        var projectPermissions = new List<PermissionEntity>
        {
            new()
            {
                Key = -30035L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemAllList",
                Name = "管理员-全部项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "管理员-全部项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30036L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemEdit",
                Name = "管理员-项目修改",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "管理员-项目修改",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30001L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingJudgeList",
                Name = "一审-待判断项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-待判断项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30002L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemApplyEditList",
                Name = "一审-再修改申请列表",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-再修改申请列表",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30003L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemReturnEditList",
                Name = "一审-再修改退回列表",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-再修改退回列表",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30004L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemNonTraditionalList",
                Name = "一审-非传统医学项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-非传统医学项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30005L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingSendNumberList",
                Name = "一审-待发号项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-待发号项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30006L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingReviewList",
                Name = "一审-待审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-待审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30007L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectJudge",
                Name = "一审-判断项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-判断项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30008L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectReviewEdit",
                Name = "一审-再修改项目审核",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-再修改项目审核",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30009L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemApprovedList",
                Name = "一审-已通过项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-已通过项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30010L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectSendNumber",
                Name = "一审-审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

// 二审相关权限
            new()
            {
                Key = -30011L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingAssignList",
                Name = "二审-待分配项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-待分配项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30012L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingReviewList2",
                Name = "二审-待复审项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-待复审项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30013L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemReviewReturnedList2",
                Name = "二审-已退回项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-已退回项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30014L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemApprovedList2",
                Name = "二审-已通过项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-已通过项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30015L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemReAssignList",
                Name = "二审-重新分配项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-重新分配项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30016L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectReview2",
                Name = "二审-审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30017L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectAssign",
                Name = "二审-分配项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-分配项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

// 三审相关权限
            new()
            {
                Key = -30018L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingAssignReviewList",
                Name = "三审-待分审项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-待分审项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30019L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingReviewList3",
                Name = "三审-待审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-待审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30020L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectAssignReview",
                Name = "三审-分审项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-分审项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30021L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectReview3",
                Name = "三审-审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30022L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemReviewReturnedList3",
                Name = "三审-已退回项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-已退回项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30023L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemApprovedList3",
                Name = "三审-已通过项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-已通过项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30037L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemReAssignList3",
                Name = "三审-重新分配项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-重新分配项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
// 四审相关权限
            new()
            {
                Key = -30024L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingReviewList4",
                Name = "四审-待审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "四审-待审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30025L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemProjectReview4",
                Name = "四审-审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "四审-审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30026L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemReviewReturnedList4",
                Name = "四审-已退回项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "四审-已退回项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30027L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemApprovedList4",
                Name = "四审-已通过项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "四审-已通过项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30028L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingApprovedList4",
                Name = "四审-等待上级审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "四审-等待上级审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            new()
            {
                Key = -30029L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingApprovedList3",
                Name = "三审-等待上级审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "三审-等待上级审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30030L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemPendingApprovedList2",
                Name = "二审-等待上级审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "二审-等待上级审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            new()
            {
                Key = -30031L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectSystemAllSubmittedList",
                Name = "一审-审核状态查询",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-审核状态查询",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30032L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectUserEditReviewProjectView",
                Name = "一审-再修改审核",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-再修改审核",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -30034L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -1L,
                Code = "projectRecall",
                Name = "一审-项目召回",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "一审-项目召回",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            new()
            {
                Key = -1L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = null,
                Code = "projectApproval",
                Name = "项目审核",
                Type = PermissionType.Directory,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "项目审核",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },

            //研究员

            new()
            {
                Key = -2L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = null,
                Code = "project",
                Name = "项目中心",
                Type = PermissionType.Directory,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "项目中心",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -20001L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -2L,
                Code = "projectUserAllList",
                Name = "我的项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "我的项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -20002L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -2L,
                Code = "projectUserPendingSubmit",
                Name = "待提交项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "待提交项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -20003L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -2L,
                Code = "projectUserPendingApproval",
                Name = "待审核项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "待审核项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -20004L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = -2L,
                Code = "projectUserApprovedList",
                Name = "已通过项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "已通过项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new()
            {
                Key = -20005L,
                AppId = null,
                AppCode = "itmctr-mgt",
                ParentId = null,
                Code = "projectUserAdd",
                Name = "注册新项目",
                Type = PermissionType.Menu,
                Path = null,
                Component = null,
                Icon = null,
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "注册新项目",
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
        };


        modelBuilder.Entity<PermissionEntity>().HasData(projectPermissions);


        modelBuilder.Entity<RolePermissionEntity>().HasData(
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -1L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -1L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -1L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 5L, // 系统管理员角色ID
                PermissionId = -1L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30001L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30002L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30003L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30004L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30005L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30006L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30007L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30008L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30009L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30010L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30011L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30012L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30013L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30014L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30015L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30016L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30017L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -30018L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -30019L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -30020L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -30021L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -30022L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -30023L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 5L, // 系统管理员角色ID
                PermissionId = -30024L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 5L, // 系统管理员角色ID
                PermissionId = -30025L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 5L, // 系统管理员角色ID
                PermissionId = -30026L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 5L, // 系统管理员角色ID
                PermissionId = -30027L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 5L, // 系统管理员角色ID
                PermissionId = -30028L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 4L, // 系统管理员角色ID
                PermissionId = -30029L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 3L, // 系统管理员角色ID
                PermissionId = -30030L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 2L, // 系统管理员角色ID
                PermissionId = -30030L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            },
            new
            {
                Key = IdGenerator.NextId(),
                RoleId = 6L, // 系统管理员角色ID
                PermissionId = -2L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 6L, // 系统管理员角色ID
                PermissionId = -20001L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }, new
            {
                Key = IdGenerator.NextId(),
                RoleId = 6L, // 系统管理员角色ID
                PermissionId = -20002L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }
            , new
            {
                Key = IdGenerator.NextId(),
                RoleId = 6L, // 系统管理员角色ID
                PermissionId = -20003L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }
            , new
            {
                Key = IdGenerator.NextId(),
                RoleId = 6L, // 系统管理员角色ID
                PermissionId = -20004L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }
            , new
            {
                Key = IdGenerator.NextId(),
                RoleId = 6L, // 系统管理员角色ID
                PermissionId = -20005L, // 权限ID
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            }
        );
    }

    private static void SeedRoles(ModelBuilder modelBuilder)
    {
        // 添加角色种子数据
        modelBuilder.Entity<RoleEntity>().HasData(
            new RoleEntity
            {
                Key = 1,
                Code = "ADMIN",
                Name = "系统管理员",
                Type = RoleType.System,
                Status = CommonStatus.Enabled,
                Description = "系统管理员角色",
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
                Deleted = false
            },
            new RoleEntity
            {
                Key = 2,
                Code = "CHECKER_LEVEL_1",
                Name = "总审核员",
                Type = RoleType.Position,
                Status = CommonStatus.Enabled,
                Description = "总审核员",
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
                Deleted = false
            },
            new RoleEntity
            {
                Key = 3,
                Code = "CHECKER_LEVEL_2",
                Name = "高级审核员",
                Type = RoleType.Position,
                Status = CommonStatus.Enabled,
                Description = "高级审核员",
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
                Deleted = false
            },
            new RoleEntity
            {
                Key = 4,
                Code = "CHECKER_LEVEL_3",
                Name = "中级审核员",
                Type = RoleType.Position,
                Status = CommonStatus.Enabled,
                Description = "中级审核员",
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
                Deleted = false
            },
            new RoleEntity
            {
                Key = 5,
                Code = "CHECKER_LEVEL_4",
                Name = "初级审核员",
                Type = RoleType.Position,
                Status = CommonStatus.Enabled,
                Description = "初级审核员",
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
                Deleted = false
            },
            new RoleEntity
            {
                Key = 6,
                Code = "RESEARCHER",
                Name = "研究员",
                Type = RoleType.General,
                Status = CommonStatus.Enabled,
                Description = "研究员",
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
                Deleted = false
            }
        );


        // 添加用户与角色的关联
        modelBuilder.Entity<UserRoleEntity>().HasData(
            new UserRoleEntity
            {
                Key = 1,
                UserId = 1, // 管理员用户ID
                RoleId = 1, // 系统管理员角色ID
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
            },
            new UserRoleEntity
            {
                Key = IdGenerator.NextId(),
                UserId = 1, // 管理员用户ID
                RoleId = 2, // 系统管理员角色ID
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
            },
            new UserRoleEntity
            {
                Key = IdGenerator.NextId(),
                UserId = 1, // 管理员用户ID
                RoleId = 3, // 系统管理员角色ID
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
            },
            new UserRoleEntity
            {
                Key = IdGenerator.NextId(),
                UserId = 1, // 管理员用户ID
                RoleId = 4, // 系统管理员角色ID
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
            },
            new UserRoleEntity
            {
                Key = IdGenerator.NextId(),
                UserId = 1, // 管理员用户ID
                RoleId = 5, // 系统管理员角色ID
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
            }
        );
    }

    /// <summary>
    /// 添加用户种子数据
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void SeedUsers(ModelBuilder modelBuilder)
    {
        var salt = PasswordHelper.GenerateSalt();
        var password = PasswordHelper.HashPassword("123456", salt);
        modelBuilder.Entity<UserEntity>().HasData(
            new UserEntity
            {
                Key = 1,
                CreatedBy = "system",
                CreatedTime = DateTimeOffset.UtcNow,
                LastModifiedBy = "system",
                LastModifiedTime = DateTimeOffset.UtcNow,
                Username = "xjsupport",
                PasswordHash = password,
                PasswordSalt = salt,
                RealName = "系统管理员",
                EmailConfirmed = true,
                MobileConfirmed = true,
                UserType = UserType.System,
                Status = UserStatus.Enabled,
                PasswordErrorCount = 0,
            });
    }

    /// <summary>
    /// 添加组织机构种子数据
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void SeedOrganizations(ModelBuilder modelBuilder)
    {
        // 根据页面数据添加组织机构
        var now = DateTimeOffset.UtcNow;

        // 添加中国科学院（根组织）
        modelBuilder.Entity<OrganizationEntity>().HasData(
            new OrganizationEntity
            {
                Key = 1,
                ParentId = null,
                Code = "cacms",
                Name = "中国中医科学院",
                NamePath = "/中国中医科学院",
                Level = 1,
                Path = "/1/",
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "中国中医科学院",
                OrgType = OrganizationType.Internal,
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            });

        // 添加中国中医药研究中心（子组织）
        modelBuilder.Entity<OrganizationEntity>().HasData(
            new OrganizationEntity
            {
                Key = 2,
                ParentId = 1, // 父组织为中国中医科学院
                Code = "ccebtcm",
                Name = "中国中医药循证医学中心",
                NamePath = "/中国中医科学院/中国中医药循证医学中心",
                Level = 2,
                Path = "/1/2/",
                SortOrder = 0,
                Status = CommonStatus.Enabled,
                Description = "中国中医药循证医学中心",
                OrgType = OrganizationType.Internal,
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            });

        // 将管理员用户关联到中国中医科学院组织
        modelBuilder.Entity<UserOrganizationEntity>().HasData(
            new UserOrganizationEntity
            {
                Key = 1,
                UserId = 1, // 管理员用户ID
                OrganizationId = 1, // 中国科学院组织ID
                IsPrimary = true, // 设为主组织
                CreatedBy = "system",
                CreatedTime = now,
                LastModifiedBy = "system",
                LastModifiedTime = now,
                Deleted = false
            });
    }
}
