using Microsoft.Extensions.Logging;

namespace XJ.Framework.Rbac.EntityFrameworkCore;

public class RbacInfrastructureWrapper : InfrastructureWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services.AddDbContext<RbacDbContext>(
            optionsAction: (serviceProvider, contextOptions) =>
            {
                var env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
                if (env.IsDevelopment())
                {
                    contextOptions.EnableSensitiveDataLogging();
                }

                contextOptions.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            },
            contextLifetime: ServiceLifetime.Scoped
        );

        services.AddScoped<Library.Domain.UOW.IUnitOfWork>(sp => 
        {
            var dbContext = sp.GetRequiredService<RbacDbContext>();
            var logger = sp.GetRequiredService<ILogger<UnitOfWork<RbacDbContext>>>();
            return new UnitOfWork<RbacDbContext>(dbContext, logger);
        });
    }
} 
