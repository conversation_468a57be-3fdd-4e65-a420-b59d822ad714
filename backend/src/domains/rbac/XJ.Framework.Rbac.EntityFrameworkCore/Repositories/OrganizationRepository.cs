using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>
/// Organization 仓储实现
/// </summary>
public class OrganizationRepository : BaseAuditRepository<RbacDbContext, long, OrganizationEntity>,
    IOrganizationRepository
{
    public OrganizationRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<List<OrganizationEntity>> GetOrganizationChildrenAsync(string organizationCode, bool includeSelf)
    {
        var organization = await GetAsync(q => q.Code.ToLower().Equals(organizationCode.ToLower()));
        if (organization == null)
        {
            return new List<OrganizationEntity>();
        }

        Expression<Func<OrganizationEntity, bool>> expr = q =>
            q.Path.StartsWith(organization.Path) && q.Status == CommonStatus.Enabled;

        if (!includeSelf)
        {
            expr = expr.And(q => q.Key != organization.Key);
        }

        var organizations = await GetListAsync(expr);

        return organizations.ToList();
    }

    public async Task<Dictionary<string, List<OrganizationEntity>>> GetOrganizationChildrenAsync(
        List<string> organizationCodes,
        bool includeSelf)
    {
        var organizations =
            (await GetListAsync(q => organizationCodes.Select(o => o.ToLower()).Contains(q.Code.ToLower()))).ToList();
        if (!organizations.Any())
        {
            return new Dictionary<string, List<OrganizationEntity>>();
        }

        var organizationPaths = organizations
            .Select(o => o.Path)
            .Distinct()
            .ToList();

        Expression<Func<OrganizationEntity, bool>> expr = q =>
            q.Status == CommonStatus.Enabled
            && organizationPaths
                .Any(o => q.Path.StartsWith(o));

        var result = (await GetListAsync(expr)).ToList();

        return organizations.ToDictionary(k => k.Code.ToLower(), v =>
        {
            return result.Where(r => r.Path.StartsWith(v.Path) && (includeSelf || r.Key != v.Key))
                .ToList();
        });
    }
}
