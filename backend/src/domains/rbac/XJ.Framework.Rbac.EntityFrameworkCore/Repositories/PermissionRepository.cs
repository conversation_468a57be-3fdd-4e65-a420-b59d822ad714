using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>
/// Permission 仓储实现
/// </summary>
public class PermissionRepository : BaseSoftDeleteRepository<RbacDbContext, long, PermissionEntity>,
    IPermissionRepository
{
    private readonly IRoleRepository _roleRepository;

    public PermissionRepository(IServiceProvider serviceProvider, IRoleRepository roleRepository) : base(
        serviceProvider)
    {
        _roleRepository = roleRepository;
    }

    public async Task<List<PermissionEntity>> GetUserPermissionsAsync(long userId, string? appCode = null,
        params PermissionType[] permissionTypes)
    {
        var permissionCodes = await GetUserPermissionCodesAsync(userId, appCode, permissionTypes);
        var permissions = await GetByCodesAsync(permissionCodes);
        return permissions;
    }

    public async Task<List<PermissionEntity>> GetPermissionsByRootIdAsync(long rootId, string? appCode = null)
    {
        var query = await GetQueryableAsync();
        if (!appCode.IsNullOrEmpty())
        {
            query = query.Where(p => p.AppCode == appCode);
        }

        // 先查出所有权限，避免多次数据库查询
        var allPermissions = await query.ToListAsync();

        // 用递归查找所有子权限
        List<PermissionEntity> result = new();

        void FindChildren(long parentId)
        {
            var children = allPermissions.Where(p => p.ParentId == parentId).ToList();
            foreach (var child in children)
            {
                result.Add(child);
                FindChildren(child.Key);
            }
        }

        FindChildren(rootId);
        return result;
    }

    /// <summary>
    /// 获取用户在指定应用下的所有权限代码列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="appCode">应用code，如果为null则获取所有权限</param>
    /// <param name="permissionTypes">权限类型</param>
    /// <returns>权限代码列表</returns>
    public async Task<List<string>> GetUserPermissionCodesAsync(long userId, string? appCode = null,
        params PermissionType[] permissionTypes)
    {
        // 1. 获取用户的角色ID列表
        var roleIds = await (from ur in DbContext.Set<UserRoleEntity>()
            where ur.UserId == userId && !ur.Deleted
            select ur.RoleId).ToListAsync();

        //追加岗位角色
        var positionRoleIds = await _roleRepository.GetUserPositionRoleIdsAsync(userId);
        roleIds = roleIds.Union(positionRoleIds).ToList();

        // 2. 获取角色关联的权限代码
        var query = await GetQueryableAsync();
        var permissionQuery = from rp in DbContext.Set<RolePermissionEntity>()
            join p in query on rp.PermissionId equals p.Key
            where roleIds.Contains(rp.RoleId) &&
                  ((permissionTypes.Any() && permissionTypes.Contains(p.Type)) || !permissionTypes.Any()) && p.Status ==
                  CommonStatus.Enabled
                  && !rp.Deleted && !p.Deleted
            select p;

        // 3. 如果指定了应用ID，则只获取该应用的权限
        if (!appCode.IsNullOrEmpty())
        {
            permissionQuery = permissionQuery.Where(p => p.AppCode == appCode!);
        }

        return await permissionQuery.Select(p => p.Code)
            .Distinct()
            .ToListAsync();
    }

    /// <summary>
    /// 获取角色在指定应用下的所有权限代码列表
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="appCode">应用code，如果为null则获取所有权限</param>
    /// <returns>权限代码列表</returns>
    public async Task<List<string>> GetRolePermissionCodesAsync(long roleId, string? appCode = null)
    {
        var query = await GetQueryableAsync();
        var permissionQuery = from rp in DbContext.Set<RolePermissionEntity>()
            join p in query on rp.PermissionId equals p.Key
            where rp.RoleId == roleId && !p.Deleted && !rp.Deleted
            select p;


        if (!appCode.IsNullOrEmpty())
        {
            permissionQuery = permissionQuery.Where(p => p.AppCode == appCode!);
        }

        return await permissionQuery.Select(p => p.Code)
            .ToListAsync();
    }

    /// <summary>
    /// 获取多个角色在指定应用下的所有权限代码列表
    /// </summary>
    /// <param name="roleIds">角色ID列表</param>
    /// <param name="appCode">应用code，如果为null则获取所有权限</param>
    /// <returns>权限代码列表</returns>
    public async Task<List<string>> GetRolesPermissionCodesAsync(IEnumerable<long> roleIds, string? appCode = null)
    {
        var query = await GetQueryableAsync();
        var permissionQuery = from rp in DbContext.Set<RolePermissionEntity>()
            join p in query on rp.PermissionId equals p.Key
            where roleIds.Contains(rp.RoleId) && !p.Deleted && !rp.Deleted
            select p;

        if (!appCode.IsNullOrEmpty())
        {
            permissionQuery = permissionQuery.Where(p => p.AppCode == appCode);
        }

        return await permissionQuery.Select(p => p.Code)
            .Distinct()
            .ToListAsync();
    }

    /// <summary>
    /// 检查权限代码是否存在
    /// </summary>
    /// <param name="code">权限代码</param>
    /// <param name="appCode">应用code，如果为null则检查所有应用</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(string code, string? appCode = null)
    {
        var query = await GetQueryableAsync();
        if (!appCode.IsNullOrEmpty())
        {
            return await query.AnyAsync(p => p.Code == code && p.AppCode == appCode);
        }

        return await query.AnyAsync(p => p.Code == code);
    }

    /// <summary>
    /// 根据权限代码获取权限实体
    /// </summary>
    /// <param name="code">权限代码</param>
    /// <param name="appCode">应用code，如果为null则在所有应用中查找</param>
    /// <returns>权限实体</returns>
    public async Task<PermissionEntity?> GetByCodeAsync(string code, string? appCode = null)
    {
        var query = await GetQueryableAsync();
        if (!appCode.IsNullOrEmpty())
        {
            return await query.FirstOrDefaultAsync(p => p.Code == code && p.AppCode == appCode);
        }

        return await query.FirstOrDefaultAsync(p => p.Code == code);
    }

    /// <summary>
    /// 批量创建权限
    /// </summary>
    /// <param name="permissions">权限列表</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateManyAsync(List<PermissionEntity> permissions)
    {
        foreach (var permission in permissions)
        {
            permission.Key = IdGenerator.NextId();
        }

        return await InsertAsync(permissions);
    }

    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <param name="appCode">应用code，如果为null则在所有应用中检查</param>
    /// <returns>是否拥有权限</returns>
    public async Task<bool> HasPermissionAsync(long userId, string permissionCode, string? appCode = null)
    {
        // 1. 获取用户的角色ID列表
        var roleIds = await (from ur in DbContext.Set<UserRoleEntity>()
            where ur.UserId == userId && !ur.Deleted
            select ur.RoleId).ToListAsync();

        //追加岗位角色
        var positionRoleIds = await _roleRepository.GetUserPositionRoleIdsAsync(userId);
        roleIds = roleIds.Union(positionRoleIds).ToList();

        // 2. 检查角色是否拥有指定权限
        var query = await GetQueryableAsync();
        var permissionQuery = from rp in DbContext.Set<RolePermissionEntity>()
            join p in query on rp.PermissionId equals p.Key
            where roleIds.Contains(rp.RoleId)
                  && p.Code == permissionCode && !p.Deleted && !rp.Deleted
            select p;

        if (!appCode.IsNullOrEmpty())
        {
            permissionQuery = permissionQuery.Where(p => p.AppCode == appCode);
        }

        return await permissionQuery.AnyAsync();
    }

    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionId">权限Id</param>
    /// <param name="appCode">应用code，如果为null则在所有应用中检查</param>
    /// <returns>是否拥有权限</returns>
    public async Task<bool> HasPermissionAsync(long userId, long permissionId, string? appCode = null)
    {
        // 1. 获取用户的角色ID列表
        var roleIds = await (from ur in DbContext.Set<UserRoleEntity>()
            where ur.UserId == userId && !ur.Deleted
            select ur.RoleId).ToListAsync();

        //追加岗位角色
        var positionRoleIds = await _roleRepository.GetUserPositionRoleIdsAsync(userId);
        roleIds = roleIds.Union(positionRoleIds).ToList();


        // 2. 检查角色是否拥有指定权限
        var query = await GetQueryableAsync();
        var permissionQuery = from rp in DbContext.Set<RolePermissionEntity>()
            join p in query on rp.PermissionId equals p.Key
            where roleIds.Contains(rp.RoleId)
                  && p.Key == permissionId && !p.Deleted && !rp.Deleted
            select p;

        if (!appCode.IsNullOrEmpty())
        {
            permissionQuery = permissionQuery.Where(p => p.AppCode == appCode);
        }

        return await permissionQuery.AnyAsync();
    }

    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionIds">权限Id集合</param>
    /// <param name="appCode">应用code，如果为null则在所有应用中检查</param>
    /// <returns>有权限的Id集合</returns>
    public async Task<List<long>> HasPermissionAsync(long userId, List<long> permissionIds, string? appCode = null)
    {
        // 1. 获取用户的角色ID列表
        var roleIds = await (from ur in DbContext.Set<UserRoleEntity>()
            where ur.UserId == userId && !ur.Deleted
            select ur.RoleId).ToListAsync();

        //追加岗位角色
        var positionRoleIds = await _roleRepository.GetUserPositionRoleIdsAsync(userId);
        roleIds = roleIds.Union(positionRoleIds).ToList();


        // 2. 检查角色是否拥有指定权限
        var query = await GetQueryableAsync();
        var permissionQuery = from rp in DbContext.Set<RolePermissionEntity>()
            join p in query on rp.PermissionId equals p.Key
            where roleIds.Contains(rp.RoleId) && !p.Deleted && !rp.Deleted
                  && permissionIds.Any(pi => pi == p.Key)
            select p;

        if (!appCode.IsNullOrEmpty())
        {
            permissionQuery = permissionQuery.Where(p => p.AppCode == appCode);
        }

        return permissionQuery.Select(p => p.Key).ToList();
    }

    /// <summary>
    /// 获取指定应用的所有权限列表
    /// </summary>
    /// <param name="appCode">应用code</param>
    /// <returns>权限列表</returns>
    public async Task<List<PermissionEntity>> GetAppPermissionsAsync(string appCode)
    {
        var query = await GetQueryableAsync();
        return await query.Where(p => p.AppCode == appCode)
            .ToListAsync();
    }


    /// <summary>
    /// 根据权限代码列表获取权限实体列表
    /// </summary>
    /// <param name="codes">权限代码列表</param>
    /// <returns>权限实体列表</returns>
    public async Task<List<PermissionEntity>> GetByCodesAsync(IEnumerable<string> codes)
    {
        var query = await GetQueryableAsync();
        return await query.Where(p => codes.Contains(p.Code))
            .OrderBy(p => p.SortOrder)
            .ToListAsync();
    }

    /// <summary>
    /// 检查权限代码是否存在（排除指定ID）
    /// </summary>
    /// <param name="code">权限代码</param>
    /// <param name="excludeId">需要排除的权限ID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsCodeAsync(string code, long? excludeId = null)
    {
        var query = await GetQueryableAsync();
        if (excludeId.HasValue)
        {
            return await query.AnyAsync(p => p.Code == code && p.Key != excludeId.Value);
        }

        return await query.AnyAsync(p => p.Code == code);
    }
}
