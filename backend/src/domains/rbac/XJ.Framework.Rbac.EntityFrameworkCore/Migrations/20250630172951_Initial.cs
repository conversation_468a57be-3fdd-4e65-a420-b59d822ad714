using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace XJ.Framework.Rbac.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "r");

            migrationBuilder.CreateTable(
                name: "data_permission_cache",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<long>(type: "bigint", nullable: false, comment: "用户ID"),
                    role_id = table.Column<long>(type: "bigint", nullable: false, comment: "角色ID"),
                    resource_type = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "资源类型"),
                    permission_scope = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "权限范围"),
                    cache_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "缓存时间"),
                    expire_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "过期时间"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_data_permission_cache", x => x.id);
                },
                comment: "数据权限缓存实体");

            migrationBuilder.CreateTable(
                name: "data_permission_rules",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    role_id = table.Column<long>(type: "bigint", nullable: false, comment: "角色ID"),
                    entity_type_name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "实体类型名称"),
                    rule_type = table.Column<int>(type: "int", nullable: false, comment: "规则类型"),
                    rule_value = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true, comment: "规则值"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态"),
                    remark = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "备注说明"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_data_permission_rules", x => x.id);
                },
                comment: "数据权限规则实体");

            migrationBuilder.CreateTable(
                name: "organizations",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    parent_id = table.Column<long>(type: "bigint", nullable: true, comment: "父级组织ID"),
                    code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "组织编码"),
                    name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "组织名称"),
                    name_path = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false, comment: "名称路径"),
                    level = table.Column<int>(type: "int", nullable: false, comment: "组织层级"),
                    path = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false, comment: "组织路径（格式：/1/2/3/）"),
                    sort_order = table.Column<int>(type: "int", nullable: false, comment: "同级排序"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态（1-启用，0-禁用）"),
                    description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "描述"),
                    org_type = table.Column<int>(type: "int", nullable: false, comment: "组织类型（1-内部组织，2-外部组织）"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_organizations", x => x.id);
                },
                comment: "组织 实体");

            migrationBuilder.CreateTable(
                name: "permission_delegations",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    delegator_id = table.Column<long>(type: "bigint", nullable: false, comment: "委托人ID"),
                    delegatee_id = table.Column<long>(type: "bigint", nullable: false, comment: "被委托人ID"),
                    role_id = table.Column<long>(type: "bigint", nullable: false, comment: "委托角色ID"),
                    organization_id = table.Column<long>(type: "bigint", nullable: true, comment: "组织ID"),
                    position_id = table.Column<long>(type: "bigint", nullable: true, comment: "岗位ID"),
                    start_time = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "委托开始时间"),
                    end_time = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "委托结束时间"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态"),
                    remark = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "备注说明"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_permission_delegations", x => x.id);
                },
                comment: "权限委托实体");

            migrationBuilder.CreateTable(
                name: "permissions",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    app_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "应用ID"),
                    app_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "应用编码"),
                    parent_id = table.Column<long>(type: "bigint", nullable: true, comment: "父级权限ID"),
                    code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "权限编码"),
                    name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "权限名称"),
                    type = table.Column<int>(type: "int", nullable: false, comment: "权限类型"),
                    path = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "路由路径"),
                    component = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "前端组件"),
                    redirect = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "重定向地址"),
                    icon = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "图标"),
                    method = table.Column<int>(type: "int", maxLength: 40, nullable: true, comment: "HTTP方法"),
                    sort_order = table.Column<int>(type: "int", nullable: false, comment: "排序号"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态"),
                    description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "权限描述"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_permissions", x => x.id);
                },
                comment: "功能权限实体");

            migrationBuilder.CreateTable(
                name: "positions",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    role_id = table.Column<long>(type: "bigint", nullable: false, comment: "角色id"),
                    code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "岗位编码"),
                    name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "岗位名称"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态"),
                    description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "描述"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_positions", x => x.id);
                },
                comment: "岗位 实体");

            migrationBuilder.CreateTable(
                name: "role_permissions",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    role_id = table.Column<long>(type: "bigint", nullable: false, comment: "??ID"),
                    permission_id = table.Column<long>(type: "bigint", nullable: false, comment: "??ID"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_role_permissions", x => x.id);
                },
                comment: "角色权限 实体");

            migrationBuilder.CreateTable(
                name: "roles",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "角色编码"),
                    name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "角色名称"),
                    type = table.Column<int>(type: "int", nullable: false, comment: "角色类型"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态"),
                    description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "描述"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_roles", x => x.id);
                },
                comment: "角色 实体");

            migrationBuilder.CreateTable(
                name: "tokens",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<long>(type: "bigint", nullable: false, comment: "用户ID"),
                    token = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false, comment: "令牌值"),
                    token_type = table.Column<int>(type: "int", nullable: false, comment: "令牌类型"),
                    expire_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "过期时间"),
                    create_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "创建时间"),
                    last_use_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "最后使用时间"),
                    device_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "设备标识"),
                    ip_address = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "IP地址"),
                    user_agent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "用户代理")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tokens", x => x.id);
                },
                comment: "令牌实体");

            migrationBuilder.CreateTable(
                name: "user_ext",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<long>(type: "bigint", nullable: false, comment: "用户ID"),
                    gender = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true, comment: "性别"),
                    country = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "国家"),
                    unit = table.Column<string>(type: "nvarchar(200)", maxLength: 100, nullable: true, comment: "注册单位名称"),
                    contact_address = table.Column<string>(type: "nvarchar(400)", maxLength: 200, nullable: true, comment: "联系地址"),
                    telephone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "固定电话"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_ext", x => x.id);
                },
                comment: "用户扩展信息");

            migrationBuilder.CreateTable(
                name: "user_organizations",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<long>(type: "bigint", nullable: false, comment: "用户ID"),
                    organization_id = table.Column<long>(type: "bigint", nullable: false, comment: "组织ID"),
                    is_primary = table.Column<bool>(type: "bit", nullable: false, comment: "是否主组织"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_organizations", x => x.id);
                },
                comment: "用户组织 实体");

            migrationBuilder.CreateTable(
                name: "user_password_histories",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<long>(type: "bigint", nullable: false, comment: "用户ID"),
                    password_hash = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "密码哈希"),
                    password_salt = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "密码盐"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_password_histories", x => x.id);
                },
                comment: "用户密码历史 实体");

            migrationBuilder.CreateTable(
                name: "user_positions",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<long>(type: "bigint", nullable: false, comment: "用户ID"),
                    position_id = table.Column<long>(type: "bigint", nullable: false, comment: "岗位ID"),
                    organization_id = table.Column<long>(type: "bigint", nullable: false, comment: "组织ID"),
                    is_primary = table.Column<bool>(type: "bit", nullable: false, comment: "是否主岗位"),
                    start_time = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "任职开始时间"),
                    end_time = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "任职结束时间"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_positions", x => x.id);
                },
                comment: "用户岗位 实体");

            migrationBuilder.CreateTable(
                name: "user_roles",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<long>(type: "bigint", nullable: false, comment: "用户ID"),
                    role_id = table.Column<long>(type: "bigint", nullable: false, comment: "角色ID"),
                    organization_id = table.Column<long>(type: "bigint", nullable: true, comment: "组织ID（岗位角色必填）"),
                    position_id = table.Column<long>(type: "bigint", nullable: true, comment: "岗位ID（岗位角色必填）"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_roles", x => x.id);
                },
                comment: "用户角色 实体");

            migrationBuilder.CreateTable(
                name: "users",
                schema: "r",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    username = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "用户名"),
                    password_hash = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "密码哈希"),
                    password_salt = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "密码盐"),
                    real_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "真实姓名"),
                    email = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "邮箱"),
                    email_confirmed = table.Column<bool>(type: "bit", nullable: false, comment: "邮箱是否已验证"),
                    email_confirmed_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "邮箱验证时间"),
                    mobile = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true, comment: "手机号"),
                    mobile_confirmed = table.Column<bool>(type: "bit", nullable: false, comment: "手机号是否已验证"),
                    mobile_confirmed_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "手机号验证时间"),
                    avatar = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "头像URL"),
                    user_type = table.Column<int>(type: "int", nullable: false, comment: "用户类型"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "用户状态"),
                    last_login_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "最后登录时间"),
                    last_login_ip = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "最后登录IP"),
                    password_error_count = table.Column<int>(type: "int", nullable: false, comment: "密码错误次数"),
                    password_update_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "密码更新时间"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_users", x => x.id);
                },
                comment: "用户实体");

            migrationBuilder.InsertData(
                schema: "r",
                table: "organizations",
                columns: new[] { "id", "code", "created_by", "created_time", "is_deleted", "description", "last_modified_by", "last_modified_time", "level", "name", "name_path", "org_type", "parent_id", "path", "sort_order", "status" },
                values: new object[,]
                {
                    { 1L, "cacms", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)), false, "中国中医科学院", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)), 1, "中国中医科学院", "/中国中医科学院", 1, null, "/1/", 0, 1 },
                    { 2L, "ccebtcm", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)), false, "中国中医药循证医学中心", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)), 2, "中国中医药循证医学中心", "/中国中医科学院/中国中医药循证医学中心", 1, 1L, "/1/2/", 0, 1 }
                });

            migrationBuilder.InsertData(
                schema: "r",
                table: "permissions",
                columns: new[] { "id", "app_code", "app_id", "code", "component", "created_by", "created_time", "is_deleted", "description", "icon", "last_modified_by", "last_modified_time", "method", "name", "parent_id", "path", "redirect", "sort_order", "status", "type" },
                values: new object[,]
                {
                    { -30036L, "itmctr-mgt", null, "projectSystemEdit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "管理员-项目修改", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "管理员-项目修改", -1L, null, null, 0, 1, 2 },
                    { -30035L, "itmctr-mgt", null, "projectSystemAllList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "管理员-全部项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "管理员-全部项目", -1L, null, null, 0, 1, 2 },
                    { -30034L, "itmctr-mgt", null, "projectRecall", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-项目召回", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-项目召回", -1L, null, null, 0, 1, 2 },
                    { -30032L, "itmctr-mgt", null, "projectUserEditReviewProjectView", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-再修改审核", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-再修改审核", -1L, null, null, 0, 1, 2 },
                    { -30031L, "itmctr-mgt", null, "projectSystemAllSubmittedList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-审核状态查询", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-审核状态查询", -1L, null, null, 0, 1, 2 },
                    { -30030L, "itmctr-mgt", null, "projectSystemPendingApprovedList2", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-等待上级审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-等待上级审核项目", -1L, null, null, 0, 1, 2 },
                    { -30029L, "itmctr-mgt", null, "projectSystemPendingApprovedList3", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "三审-等待上级审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "三审-等待上级审核项目", -1L, null, null, 0, 1, 2 },
                    { -30028L, "itmctr-mgt", null, "projectSystemPendingApprovedList4", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "四审-等待上级审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "四审-等待上级审核项目", -1L, null, null, 0, 1, 2 },
                    { -30027L, "itmctr-mgt", null, "projectSystemApprovedList4", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "四审-已通过项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "四审-已通过项目", -1L, null, null, 0, 1, 2 },
                    { -30026L, "itmctr-mgt", null, "projectSystemReviewReturnedList4", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "四审-已退回项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "四审-已退回项目", -1L, null, null, 0, 1, 2 },
                    { -30025L, "itmctr-mgt", null, "projectSystemProjectReview4", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "四审-审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "四审-审核项目", -1L, null, null, 0, 1, 2 },
                    { -30024L, "itmctr-mgt", null, "projectSystemPendingReviewList4", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "四审-待审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "四审-待审核项目", -1L, null, null, 0, 1, 2 },
                    { -30023L, "itmctr-mgt", null, "projectSystemApprovedList3", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "三审-已通过项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "三审-已通过项目", -1L, null, null, 0, 1, 2 },
                    { -30022L, "itmctr-mgt", null, "projectSystemReviewReturnedList3", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "三审-已退回项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "三审-已退回项目", -1L, null, null, 0, 1, 2 },
                    { -30021L, "itmctr-mgt", null, "projectSystemProjectReview3", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "三审-审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "三审-审核项目", -1L, null, null, 0, 1, 2 },
                    { -30020L, "itmctr-mgt", null, "projectSystemProjectAssignReview", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "三审-分审项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "三审-分审项目", -1L, null, null, 0, 1, 2 },
                    { -30019L, "itmctr-mgt", null, "projectSystemPendingReviewList3", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "三审-待审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "三审-待审核项目", -1L, null, null, 0, 1, 2 },
                    { -30018L, "itmctr-mgt", null, "projectSystemPendingAssignReviewList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "三审-待分审项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "三审-待分审项目", -1L, null, null, 0, 1, 2 },
                    { -30017L, "itmctr-mgt", null, "projectSystemProjectAssign", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-分配项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-分配项目", -1L, null, null, 0, 1, 2 },
                    { -30016L, "itmctr-mgt", null, "projectSystemProjectReview2", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-审核项目", -1L, null, null, 0, 1, 2 },
                    { -30015L, "itmctr-mgt", null, "projectSystemReAssignList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-重新分配项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-重新分配项目", -1L, null, null, 0, 1, 2 },
                    { -30014L, "itmctr-mgt", null, "projectSystemApprovedList2", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-已通过项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-已通过项目", -1L, null, null, 0, 1, 2 },
                    { -30013L, "itmctr-mgt", null, "projectSystemReviewReturnedList2", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-已退回项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-已退回项目", -1L, null, null, 0, 1, 2 },
                    { -30012L, "itmctr-mgt", null, "projectSystemPendingReviewList2", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-待复审项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-待复审项目", -1L, null, null, 0, 1, 2 },
                    { -30011L, "itmctr-mgt", null, "projectSystemPendingAssignList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "二审-待分配项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "二审-待分配项目", -1L, null, null, 0, 1, 2 },
                    { -30010L, "itmctr-mgt", null, "projectSystemProjectSendNumber", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-审核项目", -1L, null, null, 0, 1, 2 },
                    { -30009L, "itmctr-mgt", null, "projectSystemApprovedList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-已通过项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-已通过项目", -1L, null, null, 0, 1, 2 },
                    { -30008L, "itmctr-mgt", null, "projectSystemProjectReviewEdit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-再修改项目审核", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-再修改项目审核", -1L, null, null, 0, 1, 2 },
                    { -30007L, "itmctr-mgt", null, "projectSystemProjectJudge", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-判断项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-判断项目", -1L, null, null, 0, 1, 2 },
                    { -30006L, "itmctr-mgt", null, "projectSystemPendingReviewList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-待审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-待审核项目", -1L, null, null, 0, 1, 2 },
                    { -30005L, "itmctr-mgt", null, "projectSystemPendingSendNumberList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-待发号项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-待发号项目", -1L, null, null, 0, 1, 2 },
                    { -30004L, "itmctr-mgt", null, "projectSystemNonTraditionalList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-非传统医学项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-非传统医学项目", -1L, null, null, 0, 1, 2 },
                    { -30003L, "itmctr-mgt", null, "projectSystemReturnEditList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-再修改退回列表", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-再修改退回列表", -1L, null, null, 0, 1, 2 },
                    { -30002L, "itmctr-mgt", null, "projectSystemApplyEditList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-再修改申请列表", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-再修改申请列表", -1L, null, null, 0, 1, 2 },
                    { -30001L, "itmctr-mgt", null, "projectSystemPendingJudgeList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "一审-待判断项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "一审-待判断项目", -1L, null, null, 0, 1, 2 },
                    { -20005L, "itmctr-mgt", null, "projectUserAdd", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "注册新项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "注册新项目", null, null, null, 0, 1, 2 },
                    { -20004L, "itmctr-mgt", null, "projectUserApprovedList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "已通过项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "已通过项目", -2L, null, null, 0, 1, 2 },
                    { -20003L, "itmctr-mgt", null, "projectUserPendingApproval", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "待审核项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "待审核项目", -2L, null, null, 0, 1, 2 },
                    { -20002L, "itmctr-mgt", null, "projectUserPendingSubmit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "待提交项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "待提交项目", -2L, null, null, 0, 1, 2 },
                    { -20001L, "itmctr-mgt", null, "projectUserAllList", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "我的项目", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "我的项目", -2L, null, null, 0, 1, 2 },
                    { -2L, "itmctr-mgt", null, "project", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "项目中心", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "项目中心", null, null, null, 0, 1, 1 },
                    { -1L, "itmctr-mgt", null, "projectApproval", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "项目审核", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "项目审核", null, null, null, 0, 1, 1 },
                    { 2L, "rbac-mgt", null, "system", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "系统管理模块", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "系统管理", null, null, null, 0, 1, 1 },
                    { 3L, "rbac-mgt", null, "system:user", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "用户管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "用户管理", 2L, null, null, 0, 1, 2 },
                    { 4L, "rbac-mgt", null, "system:role", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "角色管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "角色管理", 2L, null, null, 0, 1, 2 },
                    { 5L, "rbac-mgt", null, "system:organization", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "组织管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "组织管理", 2L, null, null, 0, 1, 2 },
                    { 7L, "rbac-mgt", null, "system:position", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "岗位管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "岗位管理", 2L, null, null, 0, 1, 2 },
                    { 8L, "rbac-mgt", null, "system:permission", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "权限管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "权限管理", 2L, null, null, 0, 1, 2 },
                    { 9L, null, null, "api-permissions-root", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2440), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2440), new TimeSpan(0, 0, 0, 0, 0)), null, "Api权限", null, null, null, 999, 1, 1 },
                    { 10L, "logging-mgt", null, "system:logging", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "日志管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "日志管理", 2L, null, null, 0, 1, 2 },
                    { 11L, "messaging-mgt", null, "Messaging", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "消息管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "消息管理", null, null, null, 0, 1, 1 },
                    { 111L, "messaging-mgt", null, "MessageProvider", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "服务商管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "服务商管理", 11L, null, null, 0, 1, 2 },
                    { 112L, "messaging-mgt", null, "MessageAccount", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "账户管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "账户管理", 11L, null, null, 0, 1, 2 },
                    { 113L, "messaging-mgt", null, "MessageTemplate", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "模板管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "模板管理", 11L, null, null, 0, 1, 2 },
                    { 114L, "messaging-mgt", null, "MessageSendRecords", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "发送记录页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "发送记录", 11L, null, null, 0, 1, 2 },
                    { 10001L, "files-mgt", null, "files", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "存储管理模块", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "存储管理", null, null, null, 0, 1, 1 },
                    { 10002L, "files-mgt", null, "files:storage", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "存储管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "存储管理", 10001L, null, null, 0, 1, 2 },
                    { 10003L, "files-mgt", null, "files:file-type", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "文件类型管理页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "文件类型管理", 10001L, null, null, 0, 1, 2 },
                    { 10011L, "files-mgt", null, "files:storage:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "新增存储按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "新增存储", 10002L, null, null, 1, 1, 3 },
                    { 10012L, "files-mgt", null, "files:storage:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑存储按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑存储", 10002L, null, null, 2, 1, 3 },
                    { 10013L, "files-mgt", null, "files:storage:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除存储按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除存储", 10002L, null, null, 3, 1, 3 },
                    { 10021L, "files-mgt", null, "files:file-type:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "新增文件类型按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "新增文件类型", 10003L, null, null, 1, 1, 3 },
                    { 10022L, "files-mgt", null, "files:file-type:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑文件类型按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑文件类型", 10003L, null, null, 2, 1, 3 },
                    { 10023L, "files-mgt", null, "files:file-type:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除文件类型按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除文件类型", 10003L, null, null, 3, 1, 3 },
                    { 10031L, "files-mgt", null, "files:file-type:storage:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "新增类型存储关系按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "新增类型存储关系", 10003L, null, null, 1, 1, 3 },
                    { 10032L, "files-mgt", null, "files:file-type:storage", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "类型存储关系配置按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "类型存储关系配置", 10003L, null, null, 2, 1, 3 },
                    { 10033L, "files-mgt", null, "files:file-type:storage:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除类型存储关系按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除类型存储关系", 10003L, null, null, 3, 1, 3 },
                    { 20001L, "dynamic-form-mgt", null, "form-management", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "表单管理模块", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "表单管理", null, null, null, 0, 1, 1 },
                    { 20002L, "dynamic-form-mgt", null, "form-management:form-list", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "表单定义页面", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "表单定义", 20001L, null, null, 0, 1, 2 },
                    { 20003L, "dynamic-form-mgt", null, "form-management:design", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "表单设计", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "表单设计", 20001L, null, null, 3, 1, 2 },
                    { 1939738162445484036L, "rbac-mgt", null, "system:user:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "添加用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "添加用户", 3L, null, null, 1, 1, 3 },
                    { 1939738162445484037L, "rbac-mgt", null, "system:user:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑用户", 3L, null, null, 2, 1, 3 },
                    { 1939738162445484038L, "rbac-mgt", null, "system:user:disable", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "禁用用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "禁用用户", 3L, null, null, 3, 1, 3 },
                    { 1939738162445484039L, "rbac-mgt", null, "system:user:enable", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "启用用户按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "启用用户", 3L, null, null, 3, 1, 3 },
                    { 1939738162445484040L, "rbac-mgt", null, "system:user:role", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "绑定角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "绑定角色", 3L, null, null, 3, 1, 3 },
                    { 1939738162445484041L, "rbac-mgt", null, "system:user:org", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "绑定组织架构按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "绑定组织架构", 3L, null, null, 3, 1, 3 },
                    { 1939738162445484042L, "rbac-mgt", null, "system:role:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "添加角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "添加角色", 4L, null, null, 1, 1, 3 },
                    { 1939738162445484043L, "rbac-mgt", null, "system:role:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑角色", 4L, null, null, 2, 1, 3 },
                    { 1939738162445484044L, "rbac-mgt", null, "system:role:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除角色按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除角色", 4L, null, null, 3, 1, 3 },
                    { 1939738162445484045L, "rbac-mgt", null, "system:role:permission", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "分配角色权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "分配角色权限", 4L, null, null, 3, 1, 3 },
                    { 1939738162445484046L, "rbac-mgt", null, "system:organization:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "添加组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "添加组织", 5L, null, null, 1, 1, 3 },
                    { 1939738162445484047L, "rbac-mgt", null, "system:organization:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑组织", 5L, null, null, 2, 1, 3 },
                    { 1939738162445484048L, "rbac-mgt", null, "system:organization:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除组织按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除组织", 5L, null, null, 3, 1, 3 },
                    { 1939738162445484049L, "rbac-mgt", null, "system:position:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "添加岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "添加岗位", 7L, null, null, 1, 1, 3 },
                    { 1939738162445484050L, "rbac-mgt", null, "system:position:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑岗位", 7L, null, null, 2, 1, 3 },
                    { 1939738162445484051L, "rbac-mgt", null, "system:position:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除岗位按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除岗位", 7L, null, null, 3, 1, 3 },
                    { 1939738162445484052L, "rbac-mgt", null, "system:permission:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "添加权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "添加权限", 8L, null, null, 1, 1, 3 },
                    { 1939738162445484053L, "rbac-mgt", null, "system:permission:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑权限", 8L, null, null, 2, 1, 3 },
                    { 1939738162445484054L, "rbac-mgt", null, "system:permission:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除权限按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除权限", 8L, null, null, 3, 1, 3 },
                    { 1939738162445484055L, "dynamic-form-mgt", null, "form:defined:add", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "新增表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "新增表单", 20002L, null, null, 1, 1, 3 },
                    { 1939738162445484056L, "dynamic-form-mgt", null, "form:defined:edit", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "编辑表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "编辑表单", 20002L, null, null, 2, 1, 3 },
                    { 1939738162445484057L, "dynamic-form-mgt", null, "form:defined:design", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "表单设计按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "表单设计", 20002L, null, null, 3, 1, 3 },
                    { 1939738162445484058L, "dynamic-form-mgt", null, "form:defined:delete", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "删除表单按钮", null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), null, "删除表单", 20002L, null, null, 4, 1, 3 }
                });

            migrationBuilder.InsertData(
                schema: "r",
                table: "positions",
                columns: new[] { "id", "code", "created_by", "created_time", "is_deleted", "description", "last_modified_by", "last_modified_time", "name", "role_id", "status" },
                values: new object[,]
                {
                    { 1L, "CHECKER_LEVEL_1", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), false, "", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), "总审核员", 2L, 1 },
                    { 2L, "CHECKER_LEVEL_2", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), false, "", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), "高级审核员", 3L, 1 },
                    { 3L, "CHECKER_LEVEL_3", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), false, "", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), "中级审核员", 4L, 1 },
                    { 4L, "CHECKER_LEVEL_4", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), false, "", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)), "初级审核员", 5L, 1 }
                });

            migrationBuilder.InsertData(
                schema: "r",
                table: "role_permissions",
                columns: new[] { "id", "created_by", "created_time", "is_deleted", "last_modified_by", "last_modified_time", "permission_id", "role_id" },
                values: new object[,]
                {
                    { 1939738162445484059L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 111L, 1L },
                    { 1939738162445484060L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 112L, 1L },
                    { 1939738162445484061L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 113L, 1L },
                    { 1939738162445484062L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 114L, 1L },
                    { 1939738162445484063L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 11L, 1L },
                    { 1939738162445484064L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 2L, 1L },
                    { 1939738162445484065L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10L, 1L },
                    { 1939738162445484066L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 3L, 1L },
                    { 1939738162445484067L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 4L, 1L },
                    { 1939738162445484068L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 5L, 1L },
                    { 1939738162445484069L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 7L, 1L },
                    { 1939738162445484070L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 8L, 1L },
                    { 1939738162445484071L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10001L, 1L },
                    { 1939738162445484072L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10002L, 1L },
                    { 1939738162445484073L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10003L, 1L },
                    { 1939738162445484074L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484036L, 1L },
                    { 1939738162445484075L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484037L, 1L },
                    { 1939738162445484076L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484038L, 1L },
                    { 1939738162445484077L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484039L, 1L },
                    { 1939738162445484078L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484040L, 1L },
                    { 1939738162445484079L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484041L, 1L },
                    { 1939738162445484080L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484042L, 1L },
                    { 1939738162445484081L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484043L, 1L },
                    { 1939738162445484082L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484044L, 1L },
                    { 1939738162445484083L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484045L, 1L },
                    { 1939738162445484084L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484046L, 1L },
                    { 1939738162445484085L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484047L, 1L },
                    { 1939738162445484086L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484048L, 1L },
                    { 1939738162445484087L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484049L, 1L },
                    { 1939738162445484088L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484050L, 1L },
                    { 1939738162445484089L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484051L, 1L },
                    { 1939738162445484090L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484052L, 1L },
                    { 1939738162445484091L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484053L, 1L },
                    { 1939738162445484092L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484054L, 1L },
                    { 1939738162445484093L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 9L, 1L },
                    { 1939738162445484094L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10011L, 1L },
                    { 1939738162445484095L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10012L, 1L },
                    { 1939738162445484096L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10013L, 1L },
                    { 1939738162445484097L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10021L, 1L },
                    { 1939738162445484098L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10022L, 1L },
                    { 1939738162445484099L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10023L, 1L },
                    { 1939738162445484100L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10031L, 1L },
                    { 1939738162445484101L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10032L, 1L },
                    { 1939738162445484102L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 10033L, 1L },
                    { 1939738162445484103L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 20001L, 1L },
                    { 1939738162445484104L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 20002L, 1L },
                    { 1939738162445484105L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484055L, 1L },
                    { 1939738162445484106L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484056L, 1L },
                    { 1939738162445484107L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484057L, 1L },
                    { 1939738162445484108L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 20003L, 1L },
                    { 1939738162445484109L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), 1939738162445484058L, 1L },
                    { 1939738162445484110L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -1L, 2L },
                    { 1939738162445484111L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -1L, 3L },
                    { 1939738162445484112L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -1L, 4L },
                    { 1939738162445484113L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -1L, 5L },
                    { 1939738162445484114L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30001L, 2L },
                    { 1939738162445484115L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30002L, 2L },
                    { 1939738162445484116L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30003L, 2L },
                    { 1939738162445484117L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30004L, 2L },
                    { 1939738162445484118L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30005L, 2L },
                    { 1939738162445484119L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30006L, 2L },
                    { 1939738162445484120L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30007L, 2L },
                    { 1939738162445484121L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30008L, 2L },
                    { 1939738162445484122L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30009L, 2L },
                    { 1939738162445484123L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30010L, 2L },
                    { 1939738162445484124L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30011L, 3L },
                    { 1939738162445484125L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30012L, 3L },
                    { 1939738162445484126L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30013L, 3L },
                    { 1939738162445484127L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30014L, 3L },
                    { 1939738162445484128L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30015L, 3L },
                    { 1939738162445484129L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30016L, 3L },
                    { 1939738162445484130L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30017L, 3L },
                    { 1939738162445484131L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30018L, 4L },
                    { 1939738162445484132L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30019L, 4L },
                    { 1939738162445484133L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30020L, 4L },
                    { 1939738162445484134L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30021L, 4L },
                    { 1939738162445484135L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30022L, 4L },
                    { 1939738162445484136L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30023L, 4L },
                    { 1939738162445484137L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30024L, 5L },
                    { 1939738162445484138L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30025L, 5L },
                    { 1939738162445484139L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30026L, 5L },
                    { 1939738162445484140L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30027L, 5L },
                    { 1939738162445484141L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30028L, 5L },
                    { 1939738162445484142L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30029L, 4L },
                    { 1939738162445484143L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30030L, 3L },
                    { 1939738162445484144L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -30030L, 2L },
                    { 1939738162445484145L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -2L, 6L },
                    { 1939738162445484146L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -20001L, 6L },
                    { 1939738162445484147L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -20002L, 6L },
                    { 1939738162445484148L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -20003L, 6L },
                    { 1939738162445484149L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -20004L, 6L },
                    { 1939738162445484150L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)), -20005L, 6L }
                });

            migrationBuilder.InsertData(
                schema: "r",
                table: "roles",
                columns: new[] { "id", "code", "created_by", "created_time", "is_deleted", "description", "last_modified_by", "last_modified_time", "name", "status", "type" },
                values: new object[,]
                {
                    { 1L, "ADMIN", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2030), new TimeSpan(0, 0, 0, 0, 0)), false, "系统管理员角色", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)), "系统管理员", 1, 3 },
                    { 2L, "CHECKER_LEVEL_1", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)), false, "总审核员", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)), "总审核员", 1, 2 },
                    { 3L, "CHECKER_LEVEL_2", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)), false, "高级审核员", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)), "高级审核员", 1, 2 },
                    { 4L, "CHECKER_LEVEL_3", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)), false, "中级审核员", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)), "中级审核员", 1, 2 },
                    { 5L, "CHECKER_LEVEL_4", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)), false, "初级审核员", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)), "初级审核员", 1, 2 },
                    { 6L, "RESEARCHER", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)), false, "研究员", "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)), "研究员", 1, 1 }
                });

            migrationBuilder.InsertData(
                schema: "r",
                table: "user_organizations",
                columns: new[] { "id", "created_by", "created_time", "is_deleted", "is_primary", "last_modified_by", "last_modified_time", "organization_id", "user_id" },
                values: new object[] { 1L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)), false, true, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)), 1L, 1L });

            migrationBuilder.InsertData(
                schema: "r",
                table: "user_roles",
                columns: new[] { "id", "created_by", "created_time", "is_deleted", "last_modified_by", "last_modified_time", "organization_id", "position_id", "role_id", "user_id" },
                values: new object[,]
                {
                    { 1L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2140), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2140), new TimeSpan(0, 0, 0, 0, 0)), null, null, 1L, 1L },
                    { 1939738162445484032L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), null, null, 2L, 1L },
                    { 1939738162445484033L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), null, null, 3L, 1L },
                    { 1939738162445484034L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), null, null, 4L, 1L },
                    { 1939738162445484035L, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), false, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)), null, null, 5L, 1L }
                });

            migrationBuilder.InsertData(
                schema: "r",
                table: "users",
                columns: new[] { "id", "avatar", "created_by", "created_time", "is_deleted", "email", "email_confirmed", "email_confirmed_time", "last_login_ip", "last_login_time", "last_modified_by", "last_modified_time", "mobile", "mobile_confirmed", "mobile_confirmed_time", "password_error_count", "password_hash", "password_salt", "password_update_time", "real_name", "status", "user_type", "username" },
                values: new object[] { 1L, null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1810), new TimeSpan(0, 0, 0, 0, 0)), false, null, true, null, null, null, "system", new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1810), new TimeSpan(0, 0, 0, 0, 0)), null, true, null, 0, "dZ9FWZeXEByDv52pui8tqc01/9UixvSwGE82+mLoQOg=", "Obswk9PIZOuGzIyYjxZUkQ==", null, "系统管理员", 1, 3, "xjsupport" });

            migrationBuilder.CreateIndex(
                name: "IX_data_permission_cache_expire",
                schema: "r",
                table: "data_permission_cache",
                column: "expire_time",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_data_permission_rules_role_resource",
                schema: "r",
                table: "data_permission_rules",
                columns: new[] { "role_id", "entity_type_name" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Organizations_Code",
                schema: "r",
                table: "organizations",
                column: "code",
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Organizations_ParentId",
                schema: "r",
                table: "organizations",
                column: "parent_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Organizations_Path",
                schema: "r",
                table: "organizations",
                column: "path",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_permission_delegations_delegatee",
                schema: "r",
                table: "permission_delegations",
                columns: new[] { "delegator_id", "status", "start_time", "end_time" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_permissions_app",
                schema: "r",
                table: "permissions",
                columns: new[] { "app_id", "app_code", "path" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_permissions_parent",
                schema: "r",
                table: "permissions",
                column: "parent_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_permissions_path",
                schema: "r",
                table: "permissions",
                column: "path",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UQ_permissions_code",
                schema: "r",
                table: "permissions",
                columns: new[] { "code", "app_code" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Positions_Code",
                schema: "r",
                table: "positions",
                column: "code",
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Positions_RoleId",
                schema: "r",
                table: "positions",
                column: "role_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_role_permissions_permission",
                schema: "r",
                table: "role_permissions",
                column: "permission_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UQ_role_permissions",
                schema: "r",
                table: "role_permissions",
                columns: new[] { "role_id", "permission_id" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_Code",
                schema: "r",
                table: "roles",
                column: "code",
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "ix_tokens_token",
                schema: "r",
                table: "tokens",
                column: "token");

            migrationBuilder.CreateIndex(
                name: "ix_tokens_type_expire",
                schema: "r",
                table: "tokens",
                columns: new[] { "token_type", "expire_time" });

            migrationBuilder.CreateIndex(
                name: "ix_tokens_user_id",
                schema: "r",
                table: "tokens",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_UserExt_UserId",
                schema: "r",
                table: "user_ext",
                column: "user_id",
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "ix_tokens_token",
                schema: "r",
                table: "user_organizations",
                column: "organization_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserOrganizations_UserId_OrgId",
                schema: "r",
                table: "user_organizations",
                columns: new[] { "user_id", "organization_id" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserPasswordHistories_UserId",
                schema: "r",
                table: "user_password_histories",
                column: "user_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserPositions_OrgId",
                schema: "r",
                table: "user_positions",
                column: "organization_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserPositions_PosId",
                schema: "r",
                table: "user_positions",
                column: "position_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserPositions_UserId_PosId_OrgId",
                schema: "r",
                table: "user_positions",
                columns: new[] { "user_id", "position_id", "organization_id" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_OrgId",
                schema: "r",
                table: "user_roles",
                column: "organization_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_PosId",
                schema: "r",
                table: "user_roles",
                column: "position_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                schema: "r",
                table: "user_roles",
                column: "role_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId_RoleId_OrgId_PosId",
                schema: "r",
                table: "user_roles",
                columns: new[] { "user_id", "role_id", "organization_id", "position_id" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                schema: "r",
                table: "users",
                column: "email",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Mobile",
                schema: "r",
                table: "users",
                column: "mobile",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                schema: "r",
                table: "users",
                column: "username",
                unique: true,
                filter: "[is_deleted] = 0");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "data_permission_cache",
                schema: "r");

            migrationBuilder.DropTable(
                name: "data_permission_rules",
                schema: "r");

            migrationBuilder.DropTable(
                name: "organizations",
                schema: "r");

            migrationBuilder.DropTable(
                name: "permission_delegations",
                schema: "r");

            migrationBuilder.DropTable(
                name: "permissions",
                schema: "r");

            migrationBuilder.DropTable(
                name: "positions",
                schema: "r");

            migrationBuilder.DropTable(
                name: "role_permissions",
                schema: "r");

            migrationBuilder.DropTable(
                name: "roles",
                schema: "r");

            migrationBuilder.DropTable(
                name: "tokens",
                schema: "r");

            migrationBuilder.DropTable(
                name: "user_ext",
                schema: "r");

            migrationBuilder.DropTable(
                name: "user_organizations",
                schema: "r");

            migrationBuilder.DropTable(
                name: "user_password_histories",
                schema: "r");

            migrationBuilder.DropTable(
                name: "user_positions",
                schema: "r");

            migrationBuilder.DropTable(
                name: "user_roles",
                schema: "r");

            migrationBuilder.DropTable(
                name: "users",
                schema: "r");
        }
    }
}
