namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 注册结果DTO
/// </summary>
public class RegisterResultDto
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 是否需要验证邮箱
    /// </summary>
    public bool RequireEmailVerification { get; set; }

    /// <summary>
    /// 是否需要验证手机号
    /// </summary>
    public bool RequirePhoneVerification { get; set; }
} 