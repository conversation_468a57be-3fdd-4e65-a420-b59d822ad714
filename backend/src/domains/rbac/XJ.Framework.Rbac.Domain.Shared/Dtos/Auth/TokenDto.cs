namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 令牌DTO
/// </summary>
public class TokenDto
{
    /// <summary>
    /// 主键
    /// </summary>
    public long Key { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserId { get; set; }
    
    /// <summary>
    /// 访问令牌
    /// </summary>
    public string? AccessToken { get; set; }
    
    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string? RefreshToken { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    public string? DeviceId { get; set; }
    
    /// <summary>
    /// 设备信息
    /// </summary>
    public string? DeviceInfo { get; set; }
    
    /// <summary>
    /// 客户端IP
    /// </summary>
    public string? ClientIp { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedTime { get; set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTimeOffset ExpiredTime { get; set; }
    
    /// <summary>
    /// 是否已撤销
    /// </summary>
    public bool IsRevoked { get; set; }
    
    /// <summary>
    /// 撤销时间
    /// </summary>
    public DateTimeOffset? RevokedTime { get; set; }
}
