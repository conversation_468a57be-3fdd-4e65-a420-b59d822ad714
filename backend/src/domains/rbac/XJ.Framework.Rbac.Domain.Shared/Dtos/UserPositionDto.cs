
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos;

/// <summary>
/// UserPosition DTO
/// </summary>
public class UserPositionDto : BaseDto<long>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 岗位ID
    /// </summary>
    public long PositionId { get; set; }

    /// <summary>
    /// 组织ID
    /// </summary>
    public long OrganizationId { get; set; }

    /// <summary>
    /// 是否主岗位
    /// </summary>
    public bool IsPrimary { get; set; }

    /// <summary>
    /// 任职开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 任职结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 状态（1-在任，0-离任）
    /// </summary>
    public UserPositionStatus Status { get; set; }

} 