using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;
using XJ.Framework.Rbac.Application.Contract.Interfaces;
using XJ.Framework.Rbac.Application.Contract.OperationDtos;
using XJ.Framework.Rbac.Application.Contract.QueryCriteria;
using XJ.Framework.Rbac.Domain.Shared.Dtos;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

namespace XJ.Framework.Rbac.Interface.Controllers;

/// <summary>
/// User 控制器
/// </summary>
[PublicPermission]
// [NameController(Domain.Shared.Consts.ModuleConst.ApplicationCode, Domain.Shared.Consts.ModuleConst.ApplicationName, Domain.Shared.Consts.ModuleConst.ModuleId, Domain.Shared.Consts.ModuleConst.ModuleName)]
public class UserController : BaseAppController<long, UserDto, IUserService, UserQueryCriteria>
{

    private readonly IAuthService _authService;
    private readonly IAuthInfoGetter _authInfoGetter;
    private readonly IUserService _userService;
    private readonly IUserPositionService _userPositionService;
    private readonly IUserRoleService _userRoleService;
    private readonly ICurrentUserContext _currentUserContext;

    public UserController(
        IServiceProvider serviceProvider,
        IAuthService authService, IAuthInfoGetter authInfoGetter, IUserService userService,
        IUserPositionService userPositionService, IUserRoleService userRoleService,
        ICurrentUserContext currentUserContext) : base(serviceProvider)
    {
        _authService = authService;
        _authInfoGetter = authInfoGetter;
        _userService = userService;
        _userPositionService = userPositionService;
        _userRoleService = userRoleService;
        _currentUserContext = currentUserContext;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    [HttpPost("login")]
    [AllowAnonymous]
    [UnitOfWork]
    public async Task<LoginResultDto> LoginAsync([FromBody] LoginRequestDto request)
    {
        // 从HTTP头中获取设备ID和设备信息
        (_, _, request.DeviceId, request.DeviceInfo, _, _, _, _) = await _authInfoGetter.GetAuthInfoAsync();

        // 获取客户端IP
        request.ClientIp = _currentUserContext.GetClientIp();

        return await _authService.LoginAsync(request);
    }

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    [HttpPost("refresh-token")]
    // [UnitOfWork]
    [AllowAnonymous]
    public async Task<TokenResponseDto> RefreshTokenAsync([FromBody] RefreshTokenRequestDto request)
    {
        return await _authService.RefreshTokenAsync(request);
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    [HttpPost("logout")]
    [IgnoreLogging]
    public async Task<bool> LogoutAsync()
    {
        return await _authService.LogoutAsync();
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    [HttpPost("change-password")]
    public async Task<bool> ChangePasswordAsync([FromBody] ChangePasswordRequestDto request)
    {
        return await _authService.ChangePasswordAsync(request);
    }

    /// <summary>
    /// 忘记密码
    /// </summary>
    [HttpPost("forgot-password")]
    [AllowAnonymous]
    public async Task<bool> ForgotPasswordAsync([FromBody] ForgotPasswordRequestDto request)
    {
        return await _authService.ForgotPasswordAsync(request);
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    [HttpPost("reset-password")]
    [AllowAnonymous]
    public async Task<bool> ResetPasswordAsync([FromBody] ResetPasswordRequestDto request)
    {
        return await _authService.ResetPasswordAsync(request);
    }

    // /// <summary>
    // /// 通过手机验证码重置密码
    // /// </summary>
    // [HttpPost("reset-password-by-phone")]
    // [AllowAnonymous]
    // public async Task<bool> ResetPasswordByPhoneAsync([FromBody] ResetPasswordByPhoneRequestDto request)
    // {
    //     return await _authService.ResetPasswordByPhoneAsync(request);
    // }

    /// <summary>
    /// 获取验证码
    /// </summary>
    [HttpGet("captcha")]
    [AllowAnonymous]
    [IgnoreLogging]
    public async Task<CaptchaDto> GetCaptchaAsync()
    {
        return await _authService.GetCaptchaAsync();
    }


    /// <summary>
    /// 获取用户信息
    /// </summary>
    [IgnoreLogging]
    [HttpGet("user-info")]
    public async Task<UserProfileDto> GetUserInfoAsync()
    {
        return await _authService.GetUserInfoAsync();
    }

    /// <summary>
    /// 修改用户基本信息
    /// </summary>
    [HttpPost("update-profile")]
    public async Task<bool> UpdateProfileAsync([FromBody] UpdateProfileRequestDto request)
    {
        return await _authService.UpdateProfileAsync(request);
    }

    /// <summary>
    /// 确认邮箱
    /// </summary>
    [HttpPost("confirm-email")]
    public async Task<bool> ConfirmEmailAsync([FromBody] ConfirmEmailRequestDto request)
    {
        return await _authService.ConfirmEmailAsync(request);
    }

    /// <summary>
    /// 确认手机号
    /// </summary>
    [HttpPost("confirm-phone")]
    public async Task<bool> ConfirmPhoneNumberAsync([FromBody] ConfirmPhoneNumberRequestDto request)
    {
        return await _authService.ConfirmPhoneNumberAsync(request);
    }

    /// <summary>
    /// 发送邮箱验证码
    /// </summary>
    [HttpPost("send-email-confirmation-code")]
    // [AllowAnonymous]
    public async Task<bool> SendEmailConfirmationCodeAsync([FromBody] SendEmailConfirmationCodeRequestDto request)
    {
        return await _authService.SendEmailConfirmationCodeAsync(request);
    }

    /// <summary>
    /// 发送手机验证码
    /// </summary>
    [HttpPost("send-phone-confirmation-code")]
    // [AllowAnonymous]
    public async Task<bool> SendPhoneConfirmationCodeAsync([FromBody] SendPhoneConfirmationCodeRequestDto request)
    {
        return await _authService.SendPhoneConfirmationCodeAsync(request);
    }

    // [HttpPost("validate-device-id/{deviceId}")]
    // public async Task<bool> ValidateDeviceIdAsync(string deviceId)
    // {
    //     return await _authService.ValidateDeviceIdAsync(deviceId);
    // }
    [HttpPost("register")]
    [AllowAnonymous]
    public async Task<bool> RegisterAsync([FromBody] RegisterOperationDto request)
    {
        // 这里可以添加注册逻辑
        // 例如，调用 _userService.CreateUserAsync(registerOperationDto) 方法来创建用户
        // 并返回操作结果
        return await _userService.Register(request);
    }

    [HttpGet("user-positions")]
    [IgnoreLogging]
    public async Task<List<PositionDto>> GetUserPositionsAsync()
    {
        return await _userPositionService.GetUserPositionsAsync();
    }

    [HttpGet("user-roles")]
    [IgnoreLogging]
    public async Task<List<string>> GetUserRolesAsync()
    {
        return await _userRoleService.GetUserRoleCodesAsync();
    }
}
