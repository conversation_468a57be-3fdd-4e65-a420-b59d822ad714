using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Application.Extensions;
using XJ.Framework.Library.Application.Options;
using XJ.Framework.Library.Interface;
using XJ.Framework.Messaging.ApiClient;
using XJ.Framework.Rbac.Application;
using XJ.Framework.Rbac.EntityFrameworkCore;

namespace XJ.Framework.Rbac.Interface;

public class RbacInterfaceWrapper : InterfaceWrapper
{
    public override string ApplicationCode => Domain.Shared.Consts.ModuleConst.ApplicationCode;
    public override string ApplicationName => Domain.Shared.Consts.ModuleConst.ApplicationName;

    public override void Init<TEntryProgram>(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<TEntryProgram, RbacApplicationWrapper, RbacInfrastructureWrapper>(
                configuration);

        services.Configure<JwtOptions>(configuration.GetSection("Jwt"));
        // 使用标准HttpClient注册MessagingApplicationApiClient
        services.AddHttpClient<MessagingApplicationApiClient>();
        services.AddTransient<MessagingApplicationApiClient>();
    }
}
