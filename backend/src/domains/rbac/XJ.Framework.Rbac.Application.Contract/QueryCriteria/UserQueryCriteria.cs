using XJ.Framework.Library.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.QueryCriteria;

/// <summary>
/// User 查询条件
/// </summary>
public class UserQueryCriteria : BaseQueryCriteria
{
    [Equal] public string? Username { get; set; }
    [Equal] public string? Email { get; set; }
    [Equal] public string? Mobile { get; set; }
    [Contains] public string? RealName { get; set; }

    [Equal] public UserStatus? Status { get; set; }

    [Equal] public UserType? UserType { get; set; }

    [NotEqual] public UserType? NotUserType { get; set; }
}