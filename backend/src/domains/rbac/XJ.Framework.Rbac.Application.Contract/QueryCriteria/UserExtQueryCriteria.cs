namespace XJ.Framework.Rbac.Application.Contract.QueryCriteria;

/// <summary>
/// User 查询条件
/// </summary>
public class UserExtQueryCriteria : BaseQueryCriteria
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Equal] public long UserId { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Equal] public string? Gender { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    [Equal] public string? Country { get; set; }

    /// <summary>
    /// 注册单位名称
    /// </summary>
    [Contains] public string? Unit { get; set; }

    /// <summary>
    /// 联系地址
    /// </summary>
    [Contains] public string? ContactAddress { get; set; }

    /// <summary>
    /// 固定电话
    /// </summary>
    [Contains] public string? Telephone { get; set; }
}
