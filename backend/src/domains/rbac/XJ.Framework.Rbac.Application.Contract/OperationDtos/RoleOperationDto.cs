
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// Role 操作 DTO
/// </summary>
public class RoleOperationDto : BaseOperationDto
{
    /// <summary>
    /// 角色编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 角色类型（1-通用角色，2-岗位角色，3-系统角色）
    /// </summary>
    public RoleType Type { get; set; }

    /// <summary>
    /// 状态（1-启用，0-禁用）
    /// </summary>
    public CommonStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

} 