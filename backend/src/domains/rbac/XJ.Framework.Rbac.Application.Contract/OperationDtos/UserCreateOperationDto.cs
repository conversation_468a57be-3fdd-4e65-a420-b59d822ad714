using XJ.Framework.Library.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// User 操作 DTO
/// </summary>
public class UserCreateOperationDto : BaseOperationDto
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = null!;

    /// <summary>
    /// 用户密码
    /// </summary>
    public string PasswordHash { get; set; } = null!;

    /// <summary>
    /// 用户密码盐
    /// </summary>
    public string PasswordSalt { get; set; } = null!;

    /// <summary>
    /// 真实姓名
    /// </summary>
    public string RealName { get; set; } = null!;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    // /// <summary>
    // /// 头像URL
    // /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 用户类型（1-内部用户，2-外部用户，3-系统用户）
    /// </summary>
    public UserType UserType { get; set; }

    /// <summary>
    /// 状态（1-启用，0-禁用，2-锁定）
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? Gender { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// 注册单位名称
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 联系地址
    /// </summary>
    public string? ContactAddress { get; set; }

    /// <summary>
    /// 固定电话
    /// </summary>
    public string? Telephone { get; set; }
}
