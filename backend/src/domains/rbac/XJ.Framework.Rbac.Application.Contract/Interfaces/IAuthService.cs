using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

namespace XJ.Framework.Rbac.Application.Contract.Interfaces;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录结果</returns>
    Task<LoginResultDto> LoginAsync(LoginRequestDto request);

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    /// <returns>注册结果</returns>
    Task<RegisterResultDto> RegisterAsync(RegisterRequestDto request);

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    /// <param name="request">刷新令牌请求</param>
    /// <returns>令牌响应</returns>
    Task<TokenResponseDto> RefreshTokenAsync(RefreshTokenRequestDto request);

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="request">修改密码请求</param>
    /// <returns>修改结果</returns>
    Task<bool> ChangePasswordAsync(ChangePasswordRequestDto request);

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="request">重置密码请求</param>
    /// <returns>重置结果</returns>
    Task<bool> ResetPasswordAsync(ResetPasswordRequestDto request);

    /// <summary>
    /// 发送密码重置验证码或链接，或通过验证码重置密码
    /// </summary>
    /// <param name="request">忘记密码请求</param>
    /// <returns>发送结果或重置结果</returns>
    Task<bool> ForgotPasswordAsync(ForgotPasswordRequestDto request);


    /// <summary>
    /// 获取验证码
    /// </summary>
    /// <returns>验证码信息</returns>
    Task<CaptchaDto> GetCaptchaAsync();

    /// <summary>
    /// 验证验证码
    /// </summary>
    /// <param name="captchaId">验证码ID</param>
    /// <param name="captchaCode">验证码</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateCaptchaAsync(string captchaId, string captchaCode);

    /// <summary>
    /// 获取用户权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户权限信息</returns>
    Task<UserPermissionDto> GetUserPermissionsAsync(long userId);

    /// <summary>
    /// 登出
    /// </summary>
    /// <returns>登出结果</returns>
    Task<bool> LogoutAsync();


    /// <summary>
    /// 获取用户在指定应用中的所有权限代码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="appCode">应用code</param>
    /// <returns>权限代码列表</returns>
    Task<List<string>> GetUserPermissionCodesAsync(long userId, string? appCode = null);

    /// <summary>
    /// 获取用户在指定应用中的数据权限规则
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="entityTypeName">实体类型名称</param>
    /// <returns>数据权限规则列表</returns>
    Task<IEnumerable<DataPermissionRuleDto>> GetUserDataPermissionRulesAsync(long userId, string entityTypeName);

    /// <summary>
    /// 验证用户对特定数据的访问权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="entityId">实体ID</param>
    /// <param name="entityTypeName">实体类型名称</param>
    /// <returns>是否有权限访问</returns>
    Task<bool> ValidateDataPermissionAsync(long userId, long entityId, string entityTypeName);

    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="ip">登录IP</param>
    /// <param name="deviceInfo">设备信息</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateLastLoginInfoAsync(long userId, string? ip, string? deviceInfo);

    /// <summary>
    /// 重置密码错误次数
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>重置结果</returns>
    Task<bool> ResetPasswordErrorCountAsync(long userId);

    /// <summary>
    /// 增加密码错误次数
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>当前错误次数</returns>
    Task<int> IncrementPasswordErrorCountAsync(long userId);

    /// <summary>
    /// 获取指定应用的所有权限列表
    /// </summary>
    /// <param name="appCode">应用code</param>
    /// <returns>权限列表</returns>
    Task<List<PermissionDto>> GetAppPermissionsAsync(string appCode);


    /// <summary>
    /// 更新用户状态
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="status">状态值</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateUserStatusAsync(long userId, UserStatus status);

    Task<UserProfileDto> GetUserInfoAsync();

    /// <summary>
    /// 修改用户基本信息
    /// </summary>
    Task<bool> UpdateProfileAsync(UpdateProfileRequestDto request);

    /// <summary>
    /// 发送邮箱验证码
    /// </summary>
    Task<bool> SendEmailConfirmationCodeAsync(SendEmailConfirmationCodeRequestDto request);

    /// <summary>
    /// 发送手机验证码
    /// </summary>
    Task<bool> SendPhoneConfirmationCodeAsync(SendPhoneConfirmationCodeRequestDto request);

    /// <summary>
    /// 确认邮箱
    /// </summary>
    Task<bool> ConfirmEmailAsync(ConfirmEmailRequestDto request);

    /// <summary>
    /// 确认手机号
    /// </summary>
    Task<bool> ConfirmPhoneNumberAsync(ConfirmPhoneNumberRequestDto request);

    // Task<bool> ValidateDeviceIdAsync(string deviceId);

    Task<LoginResultDto> CreateUserLoginResultAsync(
        string? appCode,
        string clientIp,
        string deviceId,
        string deviceInfo,
        string username);
}
