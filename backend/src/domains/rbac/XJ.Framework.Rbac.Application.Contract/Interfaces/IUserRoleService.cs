using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.Interfaces;

/// <summary>
/// UserRole 服务接口
/// </summary>
public interface IUserRoleService :
    IAppService<long, UserRoleDto, UserRoleQueryCriteria>,
    IEditableAppService<long, UserRoleOperationDto>
{
    Task<bool> IsAdminUser(long userId);
    Task<bool> SetUserRoles(long userId, List<UserRoleOperationDto> userRoles);

    Task<List<string>> GetUserRoleCodesAsync(long userId);
    Task<List<string>> GetUserRoleCodesAsync();
    Task<List<long>> GetRoleUsersAsync(string roleCode, params RoleType[] roleTypes);
}