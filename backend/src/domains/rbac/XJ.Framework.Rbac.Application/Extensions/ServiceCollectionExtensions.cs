using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Application.Options;
using XJ.Framework.Rbac.Application.Services;

namespace XJ.Framework.Rbac.Application.Extensions;

public static class ServiceCollectionExtensions
{
    public static void AddCaptcha(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<CaptchaOptions>(configuration.GetSection("Captcha"));
        // 如果使用简单验证码生成器
        services.AddScoped<ICaptchaService, SimpleCaptchaService>();
    }
}