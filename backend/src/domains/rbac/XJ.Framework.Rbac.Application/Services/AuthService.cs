using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Cache.Abstraction;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Messaging.ApiClient;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// 认证服务实现
/// </summary>
public partial class AuthService : IAuthService
{
    private readonly ILogger<AuthService> _logger;
    
    private readonly MessagingApplicationApiClient _messagingApplicationApiClient;
    
    private readonly IConfiguration _configuration;
    private readonly IUserRepository _userRepository;
    private readonly IUserExtRepository _userExtRepository;
    private readonly IPermissionRepository _permissionRepository;
    private readonly IDataPermissionRuleRepository _dataPermissionRuleRepository;
    private readonly ITokenService _tokenService;
    private readonly ICaptchaService _captchaService;
    private readonly IMapper _mapper;
    private readonly ICache _cache;
    private readonly IAuthProvider _authProvider;
    private readonly IUserService _userService;
    private readonly IUserRoleService _userRoleService;
    private readonly IPermissionService _permissionService;
    private readonly IUserOrganizationService _userOrganizationService;
    private readonly IUserExtService _userExtService;
    private readonly IUserPositionService _userPositionService;
    private readonly IAuthInfoGetter _authInfoGetter;
    public AuthService(
        ILogger<AuthService> logger,
        IConfiguration configuration,
        IUserRepository userRepository,
        IUserExtRepository userExtRepository,
        IPermissionRepository permissionRepository,
        IDataPermissionRuleRepository dataPermissionRuleRepository,
        ITokenService tokenService,
        ICaptchaService captchaService, IMapper mapper, ICurrentUserContext currentUserContext, ICache cache,
        IAuthProvider authProvider, IUserService userService, IUserRoleService userRoleService,
        IPermissionService permissionService, IUserOrganizationService userOrganizationService, 
        IUserExtService userExtService, IUserPositionService userPositionService, MessagingApplicationApiClient messagingApplicationApiClient, IAuthInfoGetter authInfoGetter)
    {
        _logger = logger;
        _configuration = configuration;
        _userRepository = userRepository;
        _userExtRepository = userExtRepository;
        _permissionRepository = permissionRepository;
        _dataPermissionRuleRepository = dataPermissionRuleRepository;
        _tokenService = tokenService;
        this._captchaService = captchaService;
        this._mapper = mapper;
        this._mapper = mapper;
        this._captchaService = captchaService;
        _captchaService = captchaService;
        _mapper = mapper;
        _cache = cache;
        _authProvider = authProvider;
        _userService = userService;
        _userRoleService = userRoleService;
        _permissionService = permissionService;
        _userOrganizationService = userOrganizationService;
        _currentUserContext = currentUserContext;
        _userExtService = userExtService;
        _userPositionService = userPositionService;
        _messagingApplicationApiClient = messagingApplicationApiClient;
        this._authInfoGetter = authInfoGetter;
        _authInfoGetter = authInfoGetter;
    }

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    public async Task<TokenResponseDto> RefreshTokenAsync(RefreshTokenRequestDto request)
    {
        return await _tokenService.RefreshTokenAsync(request.RefreshToken, request.DeviceId!);
    }


    /// <summary>
    /// 获取验证码
    /// </summary>
    public async Task<CaptchaDto> GetCaptchaAsync()
    {
        try
        {
            // 使用验证码服务生成新的验证码
            var captcha = await _captchaService.GenerateCaptchaAsync();

            _logger.LogInformation("生成验证码，ID: {CaptchaId}", captcha.Id);

            return captcha;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成验证码失败");
            throw;
        }
    }

    /// <summary>
    /// 验证验证码
    /// </summary>
    public async Task<bool> ValidateCaptchaAsync(string captchaId, string captchaCode)
    {
        try
        {
            // 使用验证码服务验证用户输入的验证码
            var isValid = await _captchaService.ValidateCaptchaAsync(captchaId, captchaCode);

            if (!isValid)
            {
                _logger.LogWarning("验证码验证失败，ID: {CaptchaId}, 输入: {CaptchaCode}", captchaId, captchaCode);
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证验证码失败，ID: {CaptchaId}", captchaId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户权限
    /// </summary>
    public async Task<UserPermissionDto> GetUserPermissionsAsync(long userId)
    {
        // 1. 获取用户权限代码
        var permissionCodes = await _permissionRepository.GetUserPermissionCodesAsync(userId);

        // 2. 获取应用权限列表
        var permissionDtos = new List<PermissionDto>();
        var permissions = await _permissionRepository.GetByCodesAsync(permissionCodes);

        _mapper.Map(permissions, permissionDtos);

        return new UserPermissionDto
        {
            UserId = userId,
            Permissions = permissionDtos
        };
    }

    // /// <summary>
    // /// 验证用户在指定应用中的权限
    // /// </summary>
    // public async Task<bool> ValidatePermissionAsync(long userId, string permissionCode, string? appCode = null)
    // {
    //     return await _permissionRepository.HasPermissionAsync(userId, permissionCode, appCode);
    // }


    /// <summary>
    /// 获取用户在指定应用中的所有权限代码
    /// </summary>
    public async Task<List<string>> GetUserPermissionCodesAsync(long userId, string? appCode = null)
    {
        return await _permissionRepository.GetUserPermissionCodesAsync(userId, appCode);
    }

    /// <summary>
    /// 获取用户在指定应用中的数据权限规则
    /// </summary>
    public async Task<IEnumerable<DataPermissionRuleDto>> GetUserDataPermissionRulesAsync(long userId,
        string entityTypeName)
    {
        var rules = await _dataPermissionRuleRepository.GetUserRulesAsync(userId);

        var ruleDtos = new List<DataPermissionRuleDto>();

        return _mapper.Map(rules.Where(r => r.EntityTypeName == entityTypeName), ruleDtos);
    }

    /// <summary>
    /// 验证用户对特定数据的访问权限
    /// </summary>
    public async Task<bool> ValidateDataPermissionAsync(long userId, long entityId, string entityTypeName)
    {
        return await _dataPermissionRuleRepository.CheckDataPermissionAsync<UserEntity>(userId, entityId,
            entityTypeName);
    }

    /// <summary>
    /// 获取指定应用的所有权限列表
    /// </summary>
    public async Task<List<PermissionDto>> GetAppPermissionsAsync(string appCode)
    {
        var permissions = await _permissionRepository.GetAppPermissionsAsync(appCode);

        var permissionsDtos = new List<PermissionDto>();
        return _mapper.Map(permissions, permissionsDtos);
    }
}
