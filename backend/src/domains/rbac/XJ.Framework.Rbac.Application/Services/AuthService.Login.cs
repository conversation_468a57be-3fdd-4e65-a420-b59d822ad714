using Microsoft.Extensions.Logging;
using System.Security.Authentication;
using XJ.Framework.Library.Application.Contract;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

namespace XJ.Framework.Rbac.Application.Services;

public partial class AuthService
{
    private readonly ICurrentUserContext _currentUserContext;

    /// <summary>
    /// 用户登录
    /// </summary>
    public async Task<LoginResultDto> LoginAsync(LoginRequestDto request)
    {
        try
        {
            if (!await _captchaService.ValidateCaptchaAsync(request.CaptchaId, request.CaptchaCode))
            {
                throw new AuthenticationException("验证码不正确/Captcha code is incorrect");
            }

            // 1. 查找用户
            var user = await _userRepository.FindByUsernameAsync(request.Username);
            if (user == null)
            {
                throw new AuthenticationException("用户名或密码错误/Username or password is incorrect");
            }

            var password = PasswordHelper.DecryptAES(request.Password);

            var verifyMd5Result = PasswordHelper.VerifyMd5Password(password, user.PasswordSalt, user.PasswordHash);

            var verifyHashResult = PasswordHelper.VerifyPassword(password, user.PasswordSalt, user.PasswordHash);

            //PasswordHelper.VerifyMd5Password(password,user.PasswordSalt,user.PasswordHash)

            // 2. 验证密码
            if (!verifyMd5Result && !verifyHashResult)
            {
                // 增加密码错误次数
                var errorCount = await IncrementPasswordErrorCountAsync(user.Key);

                // 如果错误次数超过限制，锁定账户
                if (errorCount >= 5)
                {
                    await UpdateUserStatusAsync(user.Key, 0); // 0 表示锁定状态
                    throw new AuthenticationException(
                        "密码错误次数过多，账户已被锁定/Password error count exceeded, account has been locked");
                }

                throw new AuthenticationException("用户名或密码错误/Username or password is incorrect");
            }

            // 3. 检查用户状态
            if (user.Status != UserStatus.Enabled) // 1 表示激活状态
            {
                throw new AuthenticationException(
                    "账户状态异常，请联系管理员/Account status is abnormal, please contact the administrator");
            }

            // 4. 重置密码错误次数
            if (verifyMd5Result)
            {
                _logger.LogWarning("用户 {UserId} 使用MD5密码登录，需要重置密码", user.Key);

                // 重置密码次数和加密方式
                var salt = PasswordHelper.GenerateSalt();
                var passwordHash = PasswordHelper.HashPassword(password, salt);

                user.PasswordHash = passwordHash;
                user.PasswordSalt = salt;
                user.PasswordErrorCount = 0;
                user.PasswordUpdateTime = DateTimeOffset.UtcNow;

                await _userRepository.UpdateAsync(user);
            }
            else
            {
                await ResetPasswordErrorCountAsync(user.Key);
            }

            return await CreateUserLoginResultAsync(
                request.AppCode,
                request.ClientIp!,
                request.DeviceId!,
                request.DeviceInfo!,
                user
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登录失败: {Username}/ Authentication failed. Invalid credentials or account locked.",
                request.Username);
            throw;
            // return new LoginResultDto
            // {
            //     Success = false,
            //     Message = "登录过程中发生错误，请稍后重试"
            // };
        }
    }

    public async Task<LoginResultDto> CreateUserLoginResultAsync(
        string? appCode,
        string clientIp,
        string deviceId,
        string deviceInfo,
        string username)
    {
        var user = await _userRepository.FindByUsernameAsync(username);
        return await CreateUserLoginResultAsync(appCode, clientIp, deviceId, deviceInfo, user!);
    }

    private async Task<LoginResultDto> CreateUserLoginResultAsync(
        string? appCode,
        string clientIp,
        string deviceId,
        string deviceInfo
        , UserEntity user)
    {
        // 5. 获取用户角色和权限
        var permissions = await _permissionService.GetUserAvailableMenuPermissionsAsync(user.Key, appCode);

        var roles = await _userRoleService.GetUserRoleCodesAsync(user.Key);

        var organizationCodes = await _userOrganizationService.GetUserOrganizationCodesAsync(user.Key);

        var positions = (await _userPositionService.GetUserPositionsAsync(user.Key)).Select(p => p.Code).ToList();

        // 6. 生成访问令牌和刷新令牌
        var accessToken = await _tokenService.GenerateAccessTokenAsync(user.Key, user.Username
            , clientIp, deviceId, deviceInfo
            // , roles, permissions
        );
        var refreshToken =
            await _tokenService.GenerateRefreshTokenAsync(user.Key, clientIp, deviceId,
                deviceInfo);

        // 7. 更新最后登录信息
        await UpdateLastLoginInfoAsync(user.Key, clientIp, deviceInfo);

        // 8. 返回登录结果
        return new LoginResultDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            TokenType = "Bearer",
            ExpiresIn = 3600, // TODO: 从配置中获取
            MenuPermissions = permissions.menus,
            ButtonPermissions = permissions.buttons,
            Roles = roles,
            Positions = positions,
            Organizations = organizationCodes,
        };
    }


    /// <summary>
    /// 登出
    /// </summary>
    public async Task<bool> LogoutAsync()
    {
        var userId = _currentUserContext.GetCurrentUserId();
        try
        {
            // 1. 清除用户令牌
            var result = await _tokenService.RevokeAllUserTokensAsync(userId!.Value);

            var authInfo = await _authInfoGetter.GetAuthInfoAsync();
            var tokenId = authInfo.tokenId;
            var deviceId = authInfo.deviceId;

            var key = string.Format(CacheKeys.RemoveUserAllKey, tokenId, deviceId);
            await _cache.RemoveByPatternAsync(key);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登出失败: {UserId}", userId);
            return false;
        }
    }

    public async Task<UserProfileDto> GetUserInfoAsync()
    {
        return await Task.FromResult(_currentUserContext.GetCurrentUser()!);
    }
}
