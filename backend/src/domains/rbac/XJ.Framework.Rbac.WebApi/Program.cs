using XJ.Framework.Library.Interface.Services;
using XJ.Framework.Rbac.Application.Services;
using XJ.Framework.Rbac.Domain.Shared.Consts;
using XJ.Framework.Rbac.Interface;
using XJ.Framework.Rbac.Module;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http; // 这里包含 WithServices 扩展

var builder = WebApplication.CreateBuilder(args);

await builder
    .Init<Program, RbacAuthProvider, WebApiAuthInfoGetter, RbacWebApiWrapper, RbacInterfaceWrapper>(
        ModuleConst.ApplicationCode, "").RunAsync();

