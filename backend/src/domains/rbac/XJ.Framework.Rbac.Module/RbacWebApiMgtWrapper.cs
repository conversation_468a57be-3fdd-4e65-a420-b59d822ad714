using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using XJ.Framework.Library.Interface.Extensions;
using XJ.Framework.Library.Modular.Extensions;
using XJ.Framework.Library.WebApi;
using XJ.Framework.Rbac.Interface.Mgt;

namespace XJ.Framework.Rbac.Module;

public class RbacWebApiMgtWrapper : WebApiWrapper
{
    public override void Init<TEntryProgram, TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder applicationBuilder,
        string environmentName,
        IConfigurationRoot configuration)

    {
        // 添加模块化支持
        applicationBuilder.Services.AddSimplifiedModular();

        applicationBuilder.Services
            .InitInterface<TEntryProgram, TAuthProvider, TAuthInfoGetter, RbacMgtInterfaceWrapper>(environmentName,
                configuration);
    }


    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}
