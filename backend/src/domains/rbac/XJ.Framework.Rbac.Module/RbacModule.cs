using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Interface.Extensions;
using XJ.Framework.Library.Modular.Abstractions;
using XJ.Framework.Rbac.Application.Services;
using XJ.Framework.Rbac.Interface;
using XJ.Framework.Rbac.Interface.Mgt;

namespace XJ.Framework.Rbac.Module;

/// <summary>
/// Rbac模块
/// </summary>
public class
    RbacModule<TEntryProgram, TAuthInfoGetter> : BaseModule<TEntryProgram, RbacAuthProvider, TAuthInfoGetter,
    RbacWebApiWrapper, RbacInterfaceWrapper>
    where TAuthInfoGetter : class, IAuthInfoGetter
{
    public override List<string> ReferencedModuleIds { get; } = [Domain.Shared.Consts.MgtModuleConst.ModuleId];
    public override string ModuleId => Domain.Shared.Consts.ModuleConst.ModuleId;
    public override string ModuleName => Domain.Shared.Consts.ModuleConst.ModuleName;
    public override string ApplicationCode => Domain.Shared.Consts.ModuleConst.ApplicationCode;
    public override string Version => "1.0.0";
    public override string RoutePrefix => "rbac";


    public override void ConfigureApplication(WebApplication app)
    {
        // Rbac模块特定的中间件配置（如果需要）
    }
}

/// <summary>
/// Rbac管理模块
/// </summary>
public class
    RbacMgtModule<TEntryProgram, TAuthProvider, TAuthInfoGetter> : BaseModule<TEntryProgram, TAuthProvider,
    TAuthInfoGetter, RbacWebApiMgtWrapper,
    RbacMgtInterfaceWrapper>
    where TAuthProvider : class, IAuthProvider
    where TAuthInfoGetter : class, IAuthInfoGetter
{
    public override List<string> ReferencedModuleIds { get; } = [Domain.Shared.Consts.ModuleConst.ModuleId];
    public override string ModuleId => Domain.Shared.Consts.MgtModuleConst.ModuleId;
    public override string ModuleName => Domain.Shared.Consts.MgtModuleConst.ModuleName;
    public override string ApplicationCode => Domain.Shared.Consts.MgtModuleConst.ApplicationCode;
    public override string Version => "1.0.0";
    public override string RoutePrefix => "rbac-mgt";


    public override void ConfigureApplication(WebApplication app)
    {
        // Rbac管理模块特定的中间件配置（如果需要）
    }
}
