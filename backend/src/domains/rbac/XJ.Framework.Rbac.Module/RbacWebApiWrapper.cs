using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using XJ.Framework.Library.Interface.Extensions;
using XJ.Framework.Library.Modular.Extensions;
using XJ.Framework.Library.WebApi;
using XJ.Framework.Rbac.Interface;

namespace XJ.Framework.Rbac.Module;

public class RbacWebApiWrapper : WebApiWrapper
{
    public override void Init<TEntryProgram, TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder applicationBuilder,
        string environmentName,
        IConfigurationRoot configuration)

    {
        // 添加模块化支持
        applicationBuilder.Services.AddSimplifiedModular();

        applicationBuilder.Services
            .InitInterface<TEntryProgram, TAuthProvider, TAuthInfoGetter, RbacInterfaceWrapper>(environmentName,
                configuration);
    }


    public override void UseMiddleware(WebApplication app)
    {
        // 使用数据权限中间件
        // app.UseDataPermission();
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}
