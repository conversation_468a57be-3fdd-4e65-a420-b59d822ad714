using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;
using XJ.Framework.Library.Interface.Services;
using XJ.Framework.Rbac.Application.Contract.Interfaces;
using XJ.Framework.Rbac.Application.Contract.OperationDtos;
using XJ.Framework.Rbac.Application.Contract.QueryCriteria;
using XJ.Framework.Rbac.Domain.Shared.Dtos;

namespace XJ.Framework.Rbac.Interface.Mgt.Controllers;

/// <summary>
/// 权限相关
/// </summary>
// [NameController(Domain.Shared.Consts.MgtModuleConst.ApplicationCode, Domain.Shared.Consts.MgtModuleConst.ApplicationName, Domain.Shared.Consts.MgtModuleConst.ModuleId, Domain.Shared.Consts.MgtModuleConst.ModuleName)]
public class PermissionController : BaseEditableAppController<long, PermissionDto, PermissionOperationDto,
    IPermissionService, PermissionQueryCriteria>
{

    private readonly ApiExplorer _apiExplorer;

    public PermissionController(IServiceProvider serviceProvider,
        ApiExplorer apiExplorer) : base(
        serviceProvider)
    {
        _apiExplorer = apiExplorer;
    }

    /// <summary>
    /// 获取所有API终结点
    /// </summary>
    /// <returns></returns>
    [HttpGet("api-permissions")]
    [RequirePermission("api-permissions")]
    public async Task<IEnumerable<ApiEndpointInfo>> GetApiPermissionsAsync()
    {
        return await Task.FromResult(_apiExplorer.GetAllApiEndpoints());
    }

    /// <summary>
    /// 批量更新API权限
    /// </summary>
    /// <param name="appCode"></param>
    /// <param name="dtos"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost("batch/{appCode}")]
    public async Task<bool> BatchInsertAsync(
        string appCode,
        [FromBody] IEnumerable<PermissionTreeDto> dtos)
    {
        return await Service.BatchCreateApiPermissionAsync(appCode, dtos);
    }

    /// <summary>
    /// 插入权限
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(PermissionOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    /// <summary>
    /// 根据ID获取权限
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:long}")]
    public async Task<PermissionDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    /// <summary>
    /// 根据ID更新权限
    /// </summary>
    /// <param name="id"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, PermissionOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    /// <summary>
    /// 根据ID删除权限
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpDelete("{id:long}")]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    /// <summary>
    /// 分页查询权限列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("page")]
    public async Task<PageDtoData<long, PermissionDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<PermissionQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    /// <summary>
    /// 查询权限列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<PermissionDto>> GetListAsync([FromQuery] PermissionQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
