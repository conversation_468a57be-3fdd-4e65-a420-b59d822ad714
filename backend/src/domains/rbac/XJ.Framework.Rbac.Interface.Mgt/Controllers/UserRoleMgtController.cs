using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;
using XJ.Framework.Rbac.Application.Contract.Interfaces;
using XJ.Framework.Rbac.Application.Contract.OperationDtos;
using XJ.Framework.Rbac.Application.Contract.QueryCriteria;
using XJ.Framework.Rbac.Domain.Shared.Dtos;

namespace XJ.Framework.Rbac.Interface.Mgt.Controllers;

/// <summary>
/// 用户角色相关
/// </summary>
// [NameController(Domain.Shared.Consts.MgtModuleConst.ApplicationCode, Domain.Shared.Consts.MgtModuleConst.ApplicationName, Domain.Shared.Consts.MgtModuleConst.ModuleId, Domain.Shared.Consts.MgtModuleConst.ModuleName)]
public class UserRoleController : BaseEditableAppController<long, UserRoleDto, UserRoleOperationDto, IUserRoleService,
    UserRoleQueryCriteria>
{
    public UserRoleController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    /// <summary>
    /// 更新用户角色
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="userRoles"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{userId:long}")]
    public async Task<bool> UpdateAsync(long userId, [FromBody] List<UserRoleOperationDto> userRoles)
    {
        return await Service.SetUserRoles(userId, userRoles);
    }


    /// <summary>
    /// 查询用户角色
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    [PublicPermission(true)]
    public async Task<IEnumerable<UserRoleDto>> GetListAsync([FromQuery] UserRoleQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
