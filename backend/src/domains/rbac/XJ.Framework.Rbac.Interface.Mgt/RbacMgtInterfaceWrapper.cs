using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Application.Extensions;
using XJ.Framework.Library.Interface;
using XJ.Framework.Messaging.ApiClient;
using XJ.Framework.Rbac.ApiClient;
using XJ.Framework.Rbac.Application;
using XJ.Framework.Rbac.EntityFrameworkCore;

namespace XJ.Framework.Rbac.Interface.Mgt;

public class RbacMgtInterfaceWrapper : InterfaceWrapper
{
    public override string ApplicationCode => Domain.Shared.Consts.MgtModuleConst.ApplicationCode;
    public override string ApplicationName => Domain.Shared.Consts.MgtModuleConst.ApplicationName;

    public override void Init<TEntryProgram>(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<TEntryProgram, RbacApplicationWrapper, RbacInfrastructureWrapper>(
                configuration);

        // 使用标准HttpClient注册API客户端
        services.AddHttpClient<UserApiClient>();
        services.AddTransient<UserApiClient>();
        
        services.AddHttpClient<MessagingApplicationApiClient>();
        services.AddTransient<MessagingApplicationApiClient>();
    }
}
