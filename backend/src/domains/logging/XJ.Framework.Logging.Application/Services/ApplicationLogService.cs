
namespace XJ.Framework.Logging.Application.Services;

/// <summary>
/// ApplicationLog 服务实现
/// </summary>
public sealed class ApplicationLogService :
    BaseEditableAppService<long, ApplicationLogEntity, ApplicationLogDto, ApplicationLogOperationDto, IApplicationLogRepository, ApplicationLogQueryCriteria>,
    
    IApplicationLogService
{
    public ApplicationLogService(IApplicationLogRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 
