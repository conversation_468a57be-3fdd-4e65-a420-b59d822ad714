using Microsoft.AspNetCore.Builder;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Modular.Abstractions;
using XJ.Framework.Logging.Interface.Mgt;

namespace XJ.Framework.Logging.Module;

/// <summary>
/// Logging管理模块
/// </summary>
public class LoggingMgtModule<TEntryProgram, TAuthProvider, TAuthInfoGetter> : BaseModule<TEntryProgram, TAuthProvider,
    TAuthInfoGetter,
    LoggingWebApiMgtWrapper,
    LoggingMgtInterfaceWrapper>
    where TAuthProvider : class, IAuthProvider
    where TAuthInfoGetter : class, IAuthInfoGetter
{
    public override List<string> ReferencedModuleIds { get; } = [];
    public override string ModuleId => Domain.Shared.Consts.MgtModuleConst.ModuleId;
    public override string ModuleName => Domain.Shared.Consts.MgtModuleConst.ModuleName;
    public override string ApplicationCode => Domain.Shared.Consts.MgtModuleConst.ApplicationCode;
    public override string Version => "1.0.0";
    public override string RoutePrefix => "logging-mgt";


    public override void ConfigureApplication(WebApplication app)
    {
        // Logging管理模块特定的中间件配置（如果需要）
    }
}
