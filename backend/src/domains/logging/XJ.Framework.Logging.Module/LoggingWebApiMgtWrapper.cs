using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using XJ.Framework.Library.Interface.Extensions;
using XJ.Framework.Library.Modular.Extensions;
using XJ.Framework.Library.WebApi;
using XJ.Framework.Logging.Interface.Mgt;

namespace XJ.Framework.Logging.Module;

public class LoggingWebApiMgtWrapper : WebApiWrapper
{
    public override void Init<TEntryProgram, TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder applicationBuilder,
        string environmentName,
        IConfigurationRoot configuration)

    {
        // 添加模块化支持
        applicationBuilder.Services.AddSimplifiedModular();

        applicationBuilder.Services
            .InitInterface<TEntryProgram, TAuthProvider, TAuthInfoGetter, LoggingMgtInterfaceWrapper>(environmentName,
                configuration);
    }

    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}
