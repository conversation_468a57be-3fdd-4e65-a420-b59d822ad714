using Microsoft.EntityFrameworkCore.Infrastructure;

namespace XJ.Framework.Logging.EntityFrameworkCore;

public class LoggingDbContext : BaseDbContext
{
    public LoggingDbContext(DbContextOptions<LoggingDbContext> options, IServiceProvider serviceProvider) :
        base(options, serviceProvider)
    {
    }

    protected override void Configure(DbContextOptionsBuilder builder)
    {
        builder.EnableSensitiveDataLogging().LogTo(Console.WriteLine);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
    }
}
