using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using XJ.Framework.Files.Application.Contract.Interfaces;
using XJ.Framework.Files.Application.Contract.QueryCriteria;
using XJ.Framework.Files.Domain.Shared.Dtos;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;

namespace XJ.Framework.Files.Interface.Controllers;

/// <summary>
/// FileInfo 控制器
/// </summary>
[PublicPermission]
// [NameController(Domain.Shared.Consts.ModuleConst.ApplicationCode, Domain.Shared.Consts.ModuleConst.ApplicationName, Domain.Shared.Consts.ModuleConst.ModuleId, Domain.Shared.Consts.ModuleConst.ModuleName)]
public class FileInfoController : BaseAppController<Guid, FileInfoDto, IFileInfoService, FileInfoQueryCriteria>
{
    private readonly IFileChunkService _fileChunkService;
    private readonly IFileInfoService _fileInfoService;
    private readonly IFileTypeService _fileTypeService;
    private readonly ILogger<FileInfoController> _logger;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly IFileDownloadTokenService _fileDownloadTokenService;

    public FileInfoController(IServiceProvider serviceProvider, IFileChunkService fileChunkService,
        IFileInfoService fileInfoService, ILogger<FileInfoController> logger,
        ICurrentUserContext currentUserContext, IFileDownloadTokenService fileDownloadTokenService,
        IFileTypeService fileTypeService) : base(
        serviceProvider)
    {
        _fileChunkService = fileChunkService;
        _fileInfoService = fileInfoService;
        _logger = logger;
        _currentUserContext = currentUserContext;
        _fileDownloadTokenService = fileDownloadTokenService;
        _fileTypeService = fileTypeService;
    }

    [HttpGet("define/{typeCode}")]
    [IgnoreLogging]
    // [PublicPermission]
    [AllowAnonymous]
    public async Task<FileTypeDto?> GetFileTypeDefineAsync(string typeCode)
    {
        return await _fileTypeService.GetByCodeAsync(typeCode);
    }

    [HttpPost("download-token/{fileId}")]
    [PublicPermission]
    public async Task<string> GenerateDownloadTokenAsync(Guid fileId)
    {
        return await _fileInfoService.GenerateDownloadTokenAsync(fileId);
    }


    /// <summary>
    /// 上传文件
    /// </summary>
    /// <returns></returns>
    [HttpPost("chunk")]
    // [AllowAnonymous]
    [IgnoreLogging]
    [UnitOfWork]
    public async Task<FileUploadResponseDto> UploadChunkAsync([FromForm] FileChunkUploadDto request, IFormFile file,
        CancellationToken cancellationToken = default)
    {
        return await _fileChunkService.UploadChunkAsync(request, file.OpenReadStream(),
            _currentUserContext.GetCurrentUserId(), cancellationToken);
    }

    /// <summary>
    /// 上传文件
    /// </summary>
    /// <returns></returns>
    [HttpPost("app/upload")]
    // [AllowAnonymous]
    [IgnoreLogging]
    [UnitOfWork]
    [ApplicationPermission]
    public async Task<FileInfoDto> AppUploadAsync([FromForm] FileUploadMgtDto request, IFormFile file,
        CancellationToken cancellationToken = default)
    {
        return await _fileChunkService.UploadAsync(request, file.OpenReadStream(), request.Operator,
            cancellationToken);
    }

    [HttpGet("app/{fileId:guid}/{fileName}")]
    [HttpHead("app/{fileId:guid}/{fileName}")]
    [ApplicationPermission]
    [IgnoreLogging]
    [IgnoreFormat]
    [AllowAnonymous]
    public async Task AppDownloadAsync(Guid fileId, string fileName, CancellationToken cancellationToken = default)
    {
        await GetFileStreamAsync(fileId, fileName, false, null, cancellationToken);
    }

    [HttpGet("{fileId:guid}/{fileName}")]
    [HttpHead("{fileId:guid}/{fileName}")]
    [IgnoreLogging]
    [IgnoreFormat]
    [AllowAnonymous]
    public async Task DownloadAsync(Guid fileId, string fileName, [FromQuery] string? token,
        CancellationToken cancellationToken = default)
    {
        await GetFileStreamAsync(fileId, fileName, true, token, cancellationToken);
    }


    [HttpGet("info/{fileId:guid}/{fileName}")]
    [HttpHead("info/{fileId:guid}/{fileName}")]
    [IgnoreLogging]
    [IgnoreFormat]
    [AllowAnonymous]
    public async Task<FileDto> GetFileInfoAsync(Guid fileId, string fileName, [FromQuery] string? token,
        CancellationToken cancellationToken = default)
    {
        var fileInfo = await _fileInfoService.GetByIdAsync(fileId);
        if (fileInfo == null)
        {
            throw new NotFoundException($"File with ID {fileId} not found.");
        }

        if (!fileInfo.FileName.Equals(fileName, StringComparison.OrdinalIgnoreCase))
        {
            throw new NotFoundException($"File with ID {fileId} not found.");
        }

        await _fileInfoService.CheckCanAccessAsync(fileId, fileInfo.FileTypeCode, token);

        if (fileInfo.FileSize == -1)
        {
            var fileSize = await _fileInfoService.ReCalculateFileSizeAsync(fileId);
            fileInfo.FileSize = fileSize;
        }

        return new FileDto()
        {
            FileId = fileId.ToString()!,
            FileName = fileInfo.FileName!,
            FileType = fileInfo.FileTypeCode!,
            FileSize = fileInfo.FileSize
        };
    }

    private async Task GetFileStreamAsync(Guid fileId, string fileName, bool isRequestFromUser,
        string? token,
        CancellationToken cancellationToken = default)
    {
        // 查询文件信息，设置响应头
        var fileInfo = await _fileInfoService.GetByIdAsync(fileId);
        if (fileInfo == null)
        {
            throw new NotFoundException($"File with ID {fileId} not found.");
        }

        if (!fileInfo.FileName.Equals(fileName, StringComparison.OrdinalIgnoreCase))
        {
            throw new NotFoundException($"File with ID {fileId} not found.");
        }

        if (isRequestFromUser)
        {
            await _fileInfoService.CheckCanAccessAsync(fileId, fileInfo.FileTypeCode, token);
        }


        if (fileInfo.FileSize == -1)
        {
            var fileSize = await _fileInfoService.ReCalculateFileSizeAsync(fileId);
            fileInfo.FileSize = fileSize;
        }


        var rangeHeader = Request.Headers["Range"].FirstOrDefault();
        long? startPosition = null;
        long? endPosition = null;

        if (!string.IsNullOrEmpty(rangeHeader))
        {
            var match = System.Text.RegularExpressions.Regex.Match(rangeHeader, @"bytes=(\d*)-(\d*)");
            if (match.Success)
            {
                if (!string.IsNullOrEmpty(match.Groups[1].Value))
                    startPosition = long.Parse(match.Groups[1].Value);
                if (!string.IsNullOrEmpty(match.Groups[2].Value))
                    endPosition = long.Parse(match.Groups[2].Value);
            }
        }


        var orginalFileName = fileInfo.FileName;
        var totalSize = fileInfo.FileSize;
        var start = startPosition ?? 0;
        var end = endPosition ?? (totalSize - 1);
        var contentLength = end - start + 1;

        if (!string.IsNullOrEmpty(rangeHeader))
        {
            HttpContext.Response.StatusCode = 206;
            HttpContext.Response.Headers["Content-Range"] = $"bytes {start}-{end}/{totalSize}";
            HttpContext.Response.ContentLength = contentLength;

            HttpContext.Response.Headers["Accept-Ranges"] = "bytes";
        }
        else
        {
            HttpContext.Response.StatusCode = 200;
            HttpContext.Response.ContentLength = totalSize;
        }


        HttpContext.Response.ContentType = "application/octet-stream";

        HttpContext.Response.Headers["Content-Disposition"] =
            $"attachment; filename*=UTF-8''{Uri.EscapeDataString(orginalFileName)}";


        await this.Response.Body.FlushAsync(cancellationToken);

        if (HttpContext.Request.Method == HttpMethods.Head)
            return;

        // 分块流式写入
        var chunkSize = 1024 * 1024;
        var delayMs = 50; // 可调
        await foreach (var buffer in _fileChunkService.GetFileStreamAsync(fileId, start, end, chunkSize, delayMs,
                           cancellationToken))
        {
            await this.Response.Body.WriteAsync(buffer, 0, buffer.Length, cancellationToken);
            await this.Response.Body.FlushAsync(cancellationToken);
        }
    }

    /// <summary>
    /// 获取文件上传进度
    /// </summary>
    /// <param name="fileId"></param>
    /// <returns></returns>
    [HttpGet("progress/{fileId:guid}")]
    public async Task<FileUploadResponseDto> GetUploadProgressAsync(Guid fileId)
    {
        return await _fileChunkService.GetUploadProgressAsync(fileId);
    }
}
