using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using XJ.Framework.Files.Interface.Mgt;
using XJ.Framework.Library.Interface.Extensions;
using XJ.Framework.Library.Modular.Extensions;
using XJ.Framework.Library.WebApi;

namespace XJ.Framework.Files.Module;

public class FilesWebApiMgtWrapper : WebApiWrapper
{
    public override void Init<TEntryProgram, TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder applicationBuilder,
        string environmentName,
        IConfigurationRoot configuration)

    {
        // 添加模块化支持
        applicationBuilder.Services.AddSimplifiedModular();

        applicationBuilder.Services
            .InitInterface<TEntryProgram, TAuthProvider, TAuthInfoGetter, FilesMgtInterfaceWrapper>(environmentName,
                configuration);
    }

    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}
