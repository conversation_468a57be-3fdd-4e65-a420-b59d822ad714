using XJ.Framework.Files.Domain.Shared.Enums;

namespace XJ.Framework.Files.Application.Contract.OperationDtos;

/// <summary>
/// FileInfo 操作 DTO
/// </summary>
public class FileInfoOperationDto : BaseOperationDto
{
    /// <summary>
    /// 文件原始名称
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// 文件总大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 总计分块数量
    /// </summary>
    public int ChunkCount { get; set; }

    /// <summary>
    /// 文件类型code
    /// </summary>
    public string FileTypeCode { get; set; } = null!;

    /// <summary>
    /// 上传用户ID
    /// </summary>
    public long? UploaderId { get; set; }

    /// <summary>
    /// 状态（0-上传中，1-完成，2-删除）
    /// </summary>
    public FileStatus Status { get; set; }
}