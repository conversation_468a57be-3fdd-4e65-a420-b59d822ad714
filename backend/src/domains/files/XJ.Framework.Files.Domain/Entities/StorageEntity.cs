using XJ.Framework.Files.Domain.Shared.Enums;

namespace XJ.Framework.Files.Domain.Entities;

/// <summary>
/// Storage 实体
/// </summary>
[Table("storages", Schema = "f")]
public class StorageEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 存储编码
    /// </summary>
    [Column("storage_code")]
    [StringLength(128)]
    public required string StorageCode { get; set; } = null!;

    /// <summary>
    /// 存储名称
    /// </summary>
    [Column("storage_name")]
    [StringLength(128)]
    public required string StorageName { get; set; } = null!;

    /// <summary>
    /// 存储服务地址
    /// </summary>
    [Column("endpoint")]
    [StringLength(510)]
    public required string Endpoint { get; set; } = null!;

    /// <summary>
    /// 协议（如oss、s3、ftp）
    /// </summary>
    [Column("protocol")]
    public required StorageProtocol Protocol { get; set; }

    /// <summary>
    /// 访问key
    /// </summary>
    [Column("access_key")]
    [StringLength(256)]
    public required string AccessKey { get; set; } = null!;

    /// <summary>
    /// 访问secret
    /// </summary>
    [Column("secret_key")]
    [StringLength(256)]
    public required string SecretKey { get; set; } = null!;

    /// <summary>
    /// 桶/容器名
    /// </summary>
    [Column("bucket")]
    [StringLength(256)]
    public required string Bucket { get; set; } = null!;

    /// <summary>
    /// 备注
    /// </summary>
    [Column("remark")]
    [StringLength(510)]
    public string? Remark { get; set; }
}
