using XJ.Framework.Files.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Files.Domain.Entities;

/// <summary>
/// FileChunk 实体
/// </summary>
[Table("file_chunks", Schema = "f")]
[SoftDeleteIndex("IX_file_chunks_file_id", nameof(FileId))]
[SoftDeleteIndex("IX_file_chunks_storage_code", nameof(StorageCode))]
[SoftDeleteIndex("IX_file_chunks_file_id_chunk_index", nameof(FileId), nameof(ChunkIndex))]
public class FileChunkEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 文件ID
    /// </summary>
    [Column("file_id")]
    public required Guid FileId { get; set; }

    /// <summary>
    /// 存储信息code
    /// </summary>
    [Column("storage_code")]
    public required string StorageCode { get; set; }

    /// <summary>
    /// 分块序号（从0开始）
    /// </summary>
    [Column("chunk_index")]
    public required int ChunkIndex { get; set; }

    /// <summary>
    /// 分块hash值
    /// </summary>
    [Column("chunk_hash")]
    [StringLength(128)]
    public required string ChunkHash { get; set; } = null!;

    /// <summary>
    /// 分块大小（字节）
    /// </summary>
    [Column("chunk_size")]
    public required long ChunkSize { get; set; }

    /// <summary>
    /// 状态（0-未上传，1-已上传）
    /// </summary>
    [Column("status")]
    public required FileChunkStatus Status { get; set; }

    /// <summary>
    /// 分块存储路径
    /// </summary>
    [Column("storage_path")]
    [StringLength(1024)]
    public required string StoragePath { get; set; } = null!;

    /// <summary>
    /// 分块文件名称
    /// </summary>
    [Column("file_name")]
    [StringLength(1024)]
    public required string FileName { get; set; } = null!;
}
