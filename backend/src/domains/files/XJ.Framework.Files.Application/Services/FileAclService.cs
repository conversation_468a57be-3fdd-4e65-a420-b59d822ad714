
namespace XJ.Framework.Files.Application.Services;

/// <summary>
/// FileAcl 服务实现
/// </summary>
public sealed class FileAclService :
    BaseEditableAppService<long, FileAclEntity, FileAclDto, FileAclOperationDto, IFileAclRepository, FileAclQueryCriteria>,
    IFileAclService
{
    public FileAclService(IFileAclRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 