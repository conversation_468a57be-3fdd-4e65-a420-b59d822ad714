using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Files.Application;
using XJ.Framework.Files.EntityFrameworkCore;
using XJ.Framework.Library.Application.Extensions;
using XJ.Framework.Library.Interface;
using XJ.Framework.Library.Modular.Extensions;
using XJ.Framework.Rbac.ApiClient;

namespace XJ.Framework.Files.Interface.Mgt;

public class FilesMgtInterfaceWrapper : InterfaceWrapper
{
    public override string ApplicationCode => Domain.Shared.Consts.MgtModuleConst.ApplicationCode;
    public override string ApplicationName => Domain.Shared.Consts.MgtModuleConst.ApplicationName;

    public override void Init<TEntryProgram>(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<TEntryProgram, FilesApplicationWrapper, FilesInfrastructureWrapper>(configuration);

        // 使用标准HttpClient注册UserApiClient
        services.AddHttpClient<UserApiClient>();
        services.AddTransient<UserApiClient>();
    }
}
