
using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Files.Application.Contract.Interfaces;
using XJ.Framework.Files.Application.Contract.OperationDtos;
using XJ.Framework.Files.Application.Contract.QueryCriteria;
using XJ.Framework.Files.Domain.Shared.Dtos;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;

namespace XJ.Framework.Files.Interface.Mgt.Controllers;

/// <summary>
/// Storage 控制器
/// </summary>
// [NameController(Domain.Shared.Consts.ModuleConst.ApplicationCode, Domain.Shared.Consts.ModuleConst.ApplicationName, Domain.Shared.Consts.ModuleConst.ModuleId, Domain.Shared.Consts.ModuleConst.ModuleName)]
public class StorageController : BaseEditableAppController<long, StorageDto, StorageOperationDto, IStorageService, StorageQueryCriteria>
{
    public StorageController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(StorageOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<StorageDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, StorageOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, StorageDto>> GetPageAsync([FromQuery] PagedQueryCriteria<StorageQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<StorageDto>> GetListAsync([FromQuery] StorageQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
