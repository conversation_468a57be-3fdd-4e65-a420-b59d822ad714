using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Files.Application.Contract.Interfaces;
using XJ.Framework.Files.Application.Contract.OperationDtos;
using XJ.Framework.Files.Application.Contract.QueryCriteria;
using XJ.Framework.Files.Domain.Shared.Dtos;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Interface.Attributes;
using XJ.Framework.Library.Interface.Controllers;

namespace XJ.Framework.Files.Interface.Mgt.Controllers;

/// <summary>
/// FileInfo 控制器
/// </summary>
// [NameController(Domain.Shared.Consts.ModuleConst.ApplicationCode, Domain.Shared.Consts.ModuleConst.ApplicationName, Domain.Shared.Consts.ModuleConst.ModuleId, Domain.Shared.Consts.ModuleConst.ModuleName)]
public class FileInfoController : BaseEditableAppController<Guid, FileInfoDto, FileInfoOperationDto, IFileInfoService,
    FileInfoQueryCriteria>
{

    private readonly IFileInfoService _fileInfoService;
    private readonly IFileTypeService _fileTypeService;
    private readonly IFileChunkService _fileChunkService;

    public FileInfoController(IServiceProvider serviceProvider, IFileInfoService fileInfoService,
        IFileTypeService fileTypeService, IFileChunkService fileChunkService) : base(serviceProvider)
    {
        _fileInfoService = fileInfoService;
        _fileTypeService = fileTypeService;
        _fileChunkService = fileChunkService;
    }


    [HttpGet("{id:guid}")]
    public async Task<FileInfoDto?> GetAsync(Guid id)
    {
        return await Service.GetByIdAsync(id);
    }


    [HttpGet("page")]
    public async Task<PageDtoData<Guid, FileInfoDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<FileInfoQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<FileInfoDto>> GetListAsync([FromQuery] FileInfoQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }


    [HttpGet("{fileId:guid}/{fileName}")]
    [IgnoreLogging]
    [IgnoreFormat]
    public async Task GetFileStreamAsync(Guid fileId, string fileName,
        CancellationToken cancellationToken = default)
    {
        // 查询文件信息，设置响应头
        var fileInfo = await _fileInfoService.GetByIdAsync(fileId);
        if (fileInfo == null)
        {
            throw new NotFoundException($"File with ID {fileId} not found.");
        }

        if (!fileInfo.FileName.Equals(fileName, StringComparison.OrdinalIgnoreCase))
        {
            throw new NotFoundException($"File with ID {fileId} not found.");
        }

        var rangeHeader = Request.Headers["Range"].FirstOrDefault();
        long? startPosition = null;
        long? endPosition = null;

        if (!string.IsNullOrEmpty(rangeHeader))
        {
            var match = System.Text.RegularExpressions.Regex.Match(rangeHeader, @"bytes=(\d*)-(\d*)");
            if (match.Success)
            {
                if (!string.IsNullOrEmpty(match.Groups[1].Value))
                    startPosition = long.Parse(match.Groups[1].Value);
                if (!string.IsNullOrEmpty(match.Groups[2].Value))
                    endPosition = long.Parse(match.Groups[2].Value);
            }
        }


        var orginalFileName = fileInfo.FileName;
        var totalSize = fileInfo.FileSize;
        var start = startPosition ?? 0;
        var end = endPosition ?? (totalSize - 1);
        var contentLength = end - start + 1;

        if (!string.IsNullOrEmpty(rangeHeader))
        {
            HttpContext.Response.StatusCode = 206;
            HttpContext.Response.Headers["Content-Range"] = $"bytes {start}-{end}/{totalSize}";
            HttpContext.Response.ContentLength = contentLength;

            HttpContext.Response.Headers["Accept-Ranges"] = "bytes";
        }
        else
        {
            HttpContext.Response.StatusCode = 200;
            HttpContext.Response.ContentLength = totalSize;
        }


        HttpContext.Response.ContentType = "application/octet-stream";

        HttpContext.Response.Headers["Content-Disposition"] =
            $"attachment; filename*=UTF-8''{Uri.EscapeDataString(orginalFileName)}";


        await this.Response.Body.FlushAsync(cancellationToken);

        if (HttpContext.Request.Method == HttpMethods.Head)
            return;

        // 分块流式写入
        var chunkSize = 1024 * 1024;
        var delayMs = 50; // 可调
        await foreach (var buffer in _fileChunkService.GetFileStreamAsync(fileId, start, end, chunkSize, delayMs,
                           cancellationToken))
        {
            await this.Response.Body.WriteAsync(buffer, 0, buffer.Length, cancellationToken);
            await this.Response.Body.FlushAsync(cancellationToken);
        }
    }
}
