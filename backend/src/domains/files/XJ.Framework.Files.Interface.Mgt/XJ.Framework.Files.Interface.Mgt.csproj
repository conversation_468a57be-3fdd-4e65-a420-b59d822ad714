<Project Sdk="Microsoft.NET.Sdk">



    
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Interface\XJ.Framework.Library.Interface.csproj"/>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Modular\XJ.Framework.Library.Modular.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Files.Application\XJ.Framework.Files.Application.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Files.Domain.Shared\XJ.Framework.Files.Domain.Shared.csproj"/>
    </ItemGroup>


</Project>
