using XJ.Framework.Files.Domain.Shared.Consts;
using XJ.Framework.Library.Domain.Shared.Attributes;

namespace XJ.Framework.Files.ApiClient;

[NameModule(ModuleConst.ModuleId)]
public class FilesApiClient : BaseApiClient
{

    public FilesApiClient(IServiceProvider serviceProvider, HttpClient httpClient)
        : base(serviceProvider, httpClient)
    {
    }

    public async Task<byte[]> DownloadFileAsync(string fileId, string fileName,
        CancellationToken cancellationToken)
    {
        var url = $"{BaseUrl}/FileInfo/{fileId}/{fileName}";
        return await GetByteArrayAsync(url: url, cancellationToken: cancellationToken);
    }
}
