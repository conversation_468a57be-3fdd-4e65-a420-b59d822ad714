namespace XJ.Framework.Files.EntityFrameworkCore;

public class FilesDbContext : BaseDbContext
{
    public FilesDbContext(DbContextOptions<FilesDbContext> options, IServiceProvider serviceProvider) :
        base(options, serviceProvider)
    {
    }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
    }
}
