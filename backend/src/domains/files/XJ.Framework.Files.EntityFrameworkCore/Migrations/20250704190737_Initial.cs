using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace XJ.Framework.Files.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "f");

            migrationBuilder.CreateTable(
                name: "file_acls",
                schema: "f",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    file_id = table.Column<long>(type: "bigint", nullable: false, comment: "文件ID"),
                    principal_type = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: false, comment: "主体类型（user、role、token等）"),
                    principal_id = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false, comment: "主体ID"),
                    expire_at = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "过期时间"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_file_acls", x => x.id);
                },
                comment: "FileAcl 实体");

            migrationBuilder.CreateTable(
                name: "file_chunks",
                schema: "f",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    file_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "文件ID"),
                    storage_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "存储信息code"),
                    chunk_index = table.Column<int>(type: "int", nullable: false, comment: "分块序号（从0开始）"),
                    chunk_hash = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false, comment: "分块hash值"),
                    chunk_size = table.Column<long>(type: "bigint", nullable: false, comment: "分块大小（字节）"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态（0-未上传，1-已上传）"),
                    storage_path = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false, comment: "分块存储路径"),
                    file_name = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false, comment: "分块文件名称"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_file_chunks", x => x.id);
                },
                comment: "FileChunk 实体");

            migrationBuilder.CreateTable(
                name: "file_infos",
                schema: "f",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(510)", maxLength: 510, nullable: false, comment: "文件原始名称"),
                    file_size = table.Column<long>(type: "bigint", nullable: false, comment: "文件总大小（字节）"),
                    chunk_count = table.Column<int>(type: "int", nullable: false, comment: "总计分块数量"),
                    file_type_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "文件类型code"),
                    uploader_id = table.Column<long>(type: "bigint", maxLength: 100, nullable: true, comment: "上传用户ID"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "状态（0-上传中，1-完成，2-删除）"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_file_infos", x => x.id);
                },
                comment: "FileInfo 实体");

            migrationBuilder.CreateTable(
                name: "file_type_storage_rels",
                schema: "f",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    file_type_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "文件类型code"),
                    storage_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "存储信息code"),
                    priority = table.Column<int>(type: "int", nullable: false, comment: "优先级，数字越小优先级越高"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_file_type_storage_rels", x => x.id);
                },
                comment: "FileTypeStorageRel 实体");

            migrationBuilder.CreateTable(
                name: "file_types",
                schema: "f",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    type_code = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: false, comment: "类型编码"),
                    type_name = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false, comment: "类型名称"),
                    extensions = table.Column<string>(type: "nvarchar(510)", maxLength: 510, nullable: false, comment: "允许的文件后缀（如.jpg,.png)"),
                    size_limit = table.Column<long>(type: "bigint", nullable: false, comment: "大小限制（字节）"),
                    chunk_limit = table.Column<long>(type: "bigint", nullable: false, comment: "分块大小限制（字节）"),
                    remark = table.Column<string>(type: "nvarchar(510)", maxLength: 510, nullable: true, comment: "备注"),
                    PermissionRequired = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "所需权限类型 none=无 user=用户 role=角色"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_file_types", x => x.id);
                },
                comment: "FileType 实体");

            migrationBuilder.CreateTable(
                name: "storages",
                schema: "f",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    storage_code = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false, comment: "存储编码"),
                    storage_name = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false, comment: "存储名称"),
                    endpoint = table.Column<string>(type: "nvarchar(510)", maxLength: 510, nullable: false, comment: "存储服务地址"),
                    protocol = table.Column<int>(type: "int", nullable: false, comment: "协议（如oss、s3、ftp）"),
                    access_key = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false, comment: "访问key"),
                    secret_key = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false, comment: "访问secret"),
                    bucket = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false, comment: "桶/容器名"),
                    remark = table.Column<string>(type: "nvarchar(510)", maxLength: 510, nullable: true, comment: "备注"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_storages", x => x.id);
                },
                comment: "Storage 实体");

            migrationBuilder.InsertData(
                schema: "f",
                table: "file_types",
                columns: new[] { "id", "chunk_limit", "created_by", "created_time", "is_deleted", "extensions", "last_modified_by", "last_modified_time", "PermissionRequired", "remark", "size_limit", "type_code", "type_name" },
                values: new object[,]
                {
                    { 1941212317200224257L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), false, ".jpg,.jpeg,.png", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), "login", "头像文件类型", 5242880L, "avatar", "头像" },
                    { 1941212317200224258L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "ethic_committee_approved_file", "伦理委员会审批件" },
                    { 1941212317200224259L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "mpa_approved_file", "国家药监局批准附件" },
                    { 1941212317200224260L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "study_protocol", "研究方案" },
                    { 1941212317200224261L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "informed_consent_file", "知情同意书" },
                    { 1941212317200224262L, 5242880L, "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), false, ".pdf", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), "login", "", 5242880L, "statistical_results_file", "上传试验完成后的统计结果" }
                });

            migrationBuilder.InsertData(
                schema: "f",
                table: "storages",
                columns: new[] { "id", "access_key", "bucket", "created_by", "created_time", "is_deleted", "endpoint", "last_modified_by", "last_modified_time", "protocol", "remark", "secret_key", "storage_code", "storage_name" },
                values: new object[] { 1941212317200224256L, "", "default", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), false, "", "system", new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)), 1, null, "", "default", "默认存储" });

            migrationBuilder.CreateIndex(
                name: "IX_file_acl_file_id",
                schema: "f",
                table: "file_acls",
                column: "file_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_acl_principal",
                schema: "f",
                table: "file_acls",
                columns: new[] { "principal_type", "principal_id", "expire_at" },
                filter: "[is_deleted] = 0")
                .Annotation("SqlServer:Include", new[] { "file_id" });

            migrationBuilder.CreateIndex(
                name: "IX_file_chunks_file_id",
                schema: "f",
                table: "file_chunks",
                column: "file_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_chunks_file_id_chunk_index",
                schema: "f",
                table: "file_chunks",
                columns: new[] { "file_id", "chunk_index" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_chunks_storage_code",
                schema: "f",
                table: "file_chunks",
                column: "storage_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_info_file_type_code",
                schema: "f",
                table: "file_infos",
                column: "file_type_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_info_uploader_id",
                schema: "f",
                table: "file_infos",
                column: "uploader_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_type_storage_rel_file_type_code",
                schema: "f",
                table: "file_type_storage_rels",
                columns: new[] { "file_type_code", "priority" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_type_storage_rel_storage_code",
                schema: "f",
                table: "file_type_storage_rels",
                column: "storage_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_file_types_type_code",
                schema: "f",
                table: "file_types",
                column: "type_code",
                unique: true,
                filter: "[is_deleted] = 0");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "file_acls",
                schema: "f");

            migrationBuilder.DropTable(
                name: "file_chunks",
                schema: "f");

            migrationBuilder.DropTable(
                name: "file_infos",
                schema: "f");

            migrationBuilder.DropTable(
                name: "file_type_storage_rels",
                schema: "f");

            migrationBuilder.DropTable(
                name: "file_types",
                schema: "f");

            migrationBuilder.DropTable(
                name: "storages",
                schema: "f");
        }
    }
}
