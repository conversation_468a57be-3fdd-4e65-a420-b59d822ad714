# Git
.git
.gitignore
.gitattributes

# Visual Studio Code
.vscode/
*.code-workspace

# Visual Studio
.vs/
*.user
*.userosscache
*.suo
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
frontend/
doc/
tests/
prototype/
deploy/
components/
database/

# Keep necessary files
!backend/src/
!backend/*.props 