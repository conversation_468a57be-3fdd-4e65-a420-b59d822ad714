{"name": "admin-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build --mode development", "build:sit": "vite build --mode sit", "build:release": "vite build --mode production", "build:release-aliyun": "vite build --mode production-aliyun", "build:DockerDev": "vite build --mode DockerDev", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.3.0", "axios": "^1.4.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "element-plus": "^2.10.6", "leader-line": "^1.0.8", "lodash-es": "^4.17.21", "ml-matrix": "^6.12.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pdfjs-dist": "5.3.93", "pinia": "^2.1.6", "qrcode": "^1.5.3", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.4.0", "vue-router": "^4.2.4"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20.5.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "sass": "^1.66.1", "typescript": "^5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.9", "vue-tsc": "^2.2.10"}}