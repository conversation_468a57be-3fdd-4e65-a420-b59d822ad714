import {getDeviceId, getDeviceInfoString} from './device'
import {useUserStore} from '@/stores/user'
import {getApiBaseUrl} from "@/config/env";

const baseURL = getApiBaseUrl('files')

// 获取文件下载直链
export function getDownloadUrl(fileId: string, fileName: string) {

    // 直接拼接后端下载接口
    return `${baseURL}/FileInfo/${fileId}/${encodeURIComponent(fileName)}`
}

export function getAuthDownloadUrl(fileId: string, fileName: string, token: string) {
    // 直接拼接后端下载接口
    return `${baseURL}/FileInfo/${fileId}/${encodeURIComponent(fileName)}?token=${token}`
}

/**
 * 只负责 fileId/fileName → blobUrl，不做任何业务兜底
 * @param fileId 文件ID
 * @param fileName 文件名
 * @returns ref<string|undefined> blobUrl
 */
export function useAuthImageUrl(
    fileId: Ref<string> | string,
    fileName: Ref<string> | string
) {
    const blobUrl = ref<string | undefined>(undefined);
    let revokeUrl: string | null = null;

    watch([fileId, fileName], async ([id, name]) => {
        if (!id || !name) {
            blobUrl.value = undefined;
            return;
        }
        try {
            const userStore = useUserStore();
            const token = userStore.token;
            if (!token) throw new Error('未登录');
            const url = getDownloadUrl(id, name); // 直接用已有方法
            const res = await fetch(url, {
                headers: {
                    "Authorization": `Bearer ${token}`,
                    "X-Device-Info": getDeviceInfoString(),
                    "X-Device-ID": getDeviceId()
                },
            });
            if (!res.ok) throw new Error('图片获取失败');
            const blob = await res.blob();
            const newBlobUrl = URL.createObjectURL(blob);
            if (revokeUrl) URL.revokeObjectURL(revokeUrl);
            revokeUrl = newBlobUrl;
            blobUrl.value = newBlobUrl;
        } catch {
            blobUrl.value = undefined;
        }
    }, {immediate: true});

    onUnmounted(() => {
        if (revokeUrl) URL.revokeObjectURL(revokeUrl);
    });

    return blobUrl;
}

export function formatSize(size: number) {
    if (size > 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + ' MB'
    if (size > 1024) return (size / 1024).toFixed(2) + ' KB'
    return size + ' B'
}