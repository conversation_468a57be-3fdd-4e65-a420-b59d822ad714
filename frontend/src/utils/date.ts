import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 将 ISO 字符串或 Date 对象格式化为 yyyy-MM-dd HH:mm:ss
 * @param input ISO字符串或Date对象
 * @returns 格式化后的字符串
 */
export function formatDateTime(input: string | Date | undefined | null): string {
  if (!input) return "";
  const date = typeof input === "string" ? new Date(input) : input;
  if (isNaN(date.getTime())) return "";
  const pad = (n: number) => n.toString().padStart(2, "0");
  return (
    date.getFullYear() +
    "-" +
    pad(date.getMonth() + 1) +
    "-" +
    pad(date.getDate()) +
    " " +
    pad(date.getHours()) +
    ":" +
    pad(date.getMinutes()) +
    ":" +
    pad(date.getSeconds())
  );
}

export function formatToLocal(input: string | Date | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!input) return "";
  return dayjs(input).tz(dayjs.tz.guess()).format(fmt);
}

/**
 * 将时间戳（秒或毫秒）格式化为本地时间字符串
 * @param timestamp 时间戳（number，支持秒或毫秒）
 * @param fmt 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 本地时间字符串
 */
export function formatTimestampToLocal(timestamp: number | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!timestamp && timestamp !== 0) return "";
  // 判断是否为秒级时间戳（10位），自动转为毫秒
  const ts = timestamp < 1e12 ? timestamp * 1000 : timestamp;
  return dayjs(ts).tz(dayjs.tz.guess()).format(fmt);
}
export function formatTimestampToLocalString(timestamp: string | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!timestamp) return "";
  const num = Number(timestamp);
  if (isNaN(num)) return "";
  return formatTimestampToLocal(num, fmt);
}

export function formatDateString(input: string | Date | undefined | null, fmt = "YYYY-MM-DD") {
  if (!input) return "";
  return dayjs(input).format(fmt);
}
export function formatDateTimeString(input: string | Date | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!input) return "";
  return dayjs(input).format(fmt);
}

/**
 * 将 DateTimeOffset 字符串转换为本地时间
 * @param dateTimeOffset DateTimeOffset 字符串，如 "2025-06-29T13:51:07.512093+00:00"
 * @param fmt 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 本地时间字符串
 */
export function formatDateTimeOffsetToLocal(dateTimeOffset: string | undefined | null, fmt = "YYYY-MM-DD HH:mm:ss"): string {
  if (!dateTimeOffset) return "";

  try {
    // dayjs 会自动将 DateTimeOffset 转换为本地时间
    return dayjs(dateTimeOffset).format(fmt);
  } catch (error) {
    console.warn('Failed to parse DateTimeOffset:', dateTimeOffset, error);
    return "";
  }
}

/**
 * 将 DateTimeOffset 字符串转换为本地日期
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 本地日期字符串 YYYY-MM-DD
 */
export function formatDateTimeOffsetToLocalDate(dateTimeOffset: string | undefined | null): string {
  return formatDateTimeOffsetToLocal(dateTimeOffset, "YYYY-MM-DD");
}

/**
 * 将 DateTimeOffset 字符串转换为本地时间（包含秒）
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 本地时间字符串 YYYY-MM-DD HH:mm:ss
 */
export function formatDateTimeOffsetToLocalDateTime(dateTimeOffset: string | undefined | null): string {
  return formatDateTimeOffsetToLocal(dateTimeOffset, "YYYY-MM-DD HH:mm:ss");
}

/**
 * 将 DateTimeOffset 字符串转换为本地时间（包含毫秒）
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 本地时间字符串 YYYY-MM-DD HH:mm:ss.SSS
 */
export function formatDateTimeOffsetToLocalWithMs(dateTimeOffset: string | undefined | null): string {
  return formatDateTimeOffsetToLocal(dateTimeOffset, "YYYY-MM-DD HH:mm:ss.SSS");
}

/**
 * 将 DateTimeOffset 字符串转换为相对时间
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 相对时间字符串，如 "2小时前"、"3天前"
 */
export function formatDateTimeOffsetToRelative(dateTimeOffset: string | undefined | null): string {
  if (!dateTimeOffset) return "";

  try {
    const now = dayjs();
    const target = dayjs(dateTimeOffset);
    const diffInMinutes = now.diff(target, 'minute');
    const diffInHours = now.diff(target, 'hour');
    const diffInDays = now.diff(target, 'day');

    if (diffInMinutes < 1) {
      return "刚刚";
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`;
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInDays < 7) {
      return `${diffInDays}天前`;
    } else {
      // 超过7天显示具体日期
      return formatDateTimeOffsetToLocal(dateTimeOffset, "MM-DD HH:mm");
    }
  } catch (error) {
    console.warn('Failed to parse DateTimeOffset for relative time:', dateTimeOffset, error);
    return "";
  }
}

/**
 * 将本地时间转换为 DateTimeOffset 字符串（用于发送到服务器）
 * @param date Date 对象或时间字符串
 * @returns DateTimeOffset 格式的字符串
 */
export function toDateTimeOffset(date: Date | string | undefined | null): string {
  if (!date) return "";

  try {
    const d = typeof date === 'string' ? dayjs(date) : dayjs(date);

    if (!d.isValid()) return "";

    // 转换为 ISO 8601 格式的 DateTimeOffset 字符串
    return d.toISOString();
  } catch (error) {
    console.warn('Failed to convert to DateTimeOffset:', date, error);
    return "";
  }
}

/**
 * 检查 DateTimeOffset 字符串是否有效
 * @param dateTimeOffset DateTimeOffset 字符串
 * @returns 是否为有效的 DateTimeOffset
 */
export function isValidDateTimeOffset(dateTimeOffset: string | undefined | null): boolean {
  if (!dateTimeOffset) return false;

  try {
    const parsed = dayjs(dateTimeOffset);
    return parsed.isValid();
  } catch {
    return false;
  }
}