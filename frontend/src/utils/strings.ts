const specialCharMap = new Map<string, string>([
    ["。", "."],
    ["“", '"'],
    ["”", '"'],
    ["‘", "'"],
    ["’", "'"],
    ["—", "-"],
    ["…", "..."],
    ["；", ";"],
    ["，", ","],
    ["？", "?"],
    ["！", "!"],
    ["（", "("],
    ["）", ")"],
    ["、", " "],
]);

// Add circled numbers 1-20
for (let i = 0; i < 20; i++) {
    specialCharMap.set(String.fromCharCode(0x2460 + i), `${i + 1})`);
}

// Add circled numbers 21-35
for (let i = 0; i < 15; i++) {
    specialCharMap.set(String.fromCharCode(0x3251 + i), `${21 + i})`);
}

// Add circled numbers 36-50
for (let i = 0; i < 15; i++) {
    specialCharMap.set(String.fromCharCode(0x32b1 + i), `${36 + i})`);
}


// 添加罗马数字大写 Ⅰ(1) 到 ⅩⅩ(20)
const romanNumerals = [
    "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X",
    "XI", "XII", "XIII", "XIV", "XV", "XVI", "XVII", "XVIII", "XIX", "XX",
    "XXI", "XXII", "XXIII", "XXIV", "XXV", "XXVI", "XXVII", "XXVIII", "XXIX", "XXX",
    "XXXI", "XXXII", "XXXIII", "XXXIV", "XXXV", "XXXVI", "XXXVII", "XXXVIII", "XXXIX", "XL",
    "XLI", "XLII", "XLIII", "XLIV", "XLV", "XLVI", "XLVII", "XLVIII", "XLIX", "L"
];
for (let i = 0; i < romanNumerals.length; i++) {
    specialCharMap.set(String.fromCharCode(0x2160 + i), romanNumerals[i]);
}

/**
 * Cleans and processes a string intended for English input by:
 * 1. Converting special punctuation to their English equivalents.
 * 2. Converting full-width characters to half-width.
 * 3. Removing unsupported Chinese characters and symbols.
 * 4. Removing invalid XML control characters.
 * @param text The input string to process.
 * @returns The cleaned and processed string.
 */
export function cleanAndProcessEnglishText(text: string | null | undefined, withNewline: boolean): string {
    if (!text) {
        return "";
    }

    let result = "";
    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        const charCode = char.charCodeAt(0);

        // 1. Check special character map first
        if (specialCharMap.has(char)) {
            result += specialCharMap.get(char);
            continue;
        }

        // 2. Convert full-width ASCII characters to half-width
        if (charCode >= 0xff01 && charCode <= 0xff5e) {
            result += String.fromCharCode(charCode - 0xfee0);
        }
        // Convert full-width space to half-width space
        else if (charCode === 0x3000) {
            result += " ";
        } else {
            result += char;
        }
    }

    // 3. Remove Chinese characters and unconvertible Chinese punctuation/symbols
    result = result.replace(/[\u4e00-\u9fa5\u300a\u300b\u3010\u3011\u3008\u3009\u3014\u3015]/g, "");

    // 4. Remove invalid XML control characters
    result = result.replace(/[\u0000-\u0008\u000b\u000c\u000e-\u001f]/g, "");

    // 5. Replace line breaks with space
    if (!withNewline) {
        result = result.replace(/[\r\n]+/g, " ");
    }

    return result;
}

export const ENGLISH_PATTERN = /^[\u0020-\u007e\u00a0-\u00ff\u2000-\u206f\u2100-\u214f\u03bc\u2013\u2019≥≤\u0391-\u03a9\u03b1-\u03c9]*$/;
export const ENGLISH_PATTERN_WITH_NEW_LINE = /^[\u0020-\u007e\u00a0-\u00ff\u2000-\u206f\u2100-\u214f\u03bc\u2013\u2019≥≤\u0391-\u03a9\u03b1-\u03c9\r\n]*$/;

/**
 * 传入一个字符，执行所有替换逻辑后，返回不满足 ENGLISH_PATTERN 的去重字符数组
 * @param char 输入字符
 * @returns 不满足 ENGLISH_PATTERN 的去重字符数组
 */
export function getInvalidEnglishChars(char: string, stringwithNewline: boolean): string[] {
    const processed = cleanAndProcessEnglishText(char, stringwithNewline);
    const invalidChars = new Set<string>();
    for (const c of processed) {
        if (stringwithNewline) {
            if (!ENGLISH_PATTERN_WITH_NEW_LINE.test(c)) {
                invalidChars.add(c);
            }
        } else {
            if (!ENGLISH_PATTERN.test(c)) {
                invalidChars.add(c);
            }
        }

    }
    return Array.from(invalidChars);
}