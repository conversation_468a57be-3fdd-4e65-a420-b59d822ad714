import {FieldDto, FormDefinitionDto} from "@/dtos/dynamic-form.dto";
import {FormInstanceStatus} from "@/enums";
import {fieldTypeMap} from "@/components/dynamic-form/form-fields";

// 获取状态文本
export function getStatusText(status: FormInstanceStatus) {
    switch (status) {
        case FormInstanceStatus.Draft:
            return "project.status.draft";
        case FormInstanceStatus.Submitted:
            return "project.status.submitted";
        case FormInstanceStatus.Confirmed:
            return "project.status.confirmed";
        case FormInstanceStatus.Cancelled:
            return "project.status.cancelled";
        case FormInstanceStatus.Rejected:
            return "project.status.rejected";
        default:
            return "project.status.unknown";
    }
};

export function getRowFullStatusText(row: any) {

    const processStatus = getFormDataValue(row.formData, "ProcessStatus");

    const projectTag = getFormDataValue(row.formData, "ProjectTag");

    if (projectTag == null || projectTag == "") {
        if (row.status == 5) {
            return "已退回";
        }
        return "待判断";
    } else if (projectTag == "Hide") {
        return "已隐藏";
    } else if (projectTag == "Termination") {
        const projectTerminationCause = getFormDataValue(row.formData, "ProjectTerminationCause");
        if (projectTerminationCause == "NonTraditionalProject") {
            return "非传统医学项目";
        } else if (projectTerminationCause == "MultipleSubmissions") {
            return "重复提交";
        } else if (projectTerminationCause == "Other") {
            return "其他";
        } else {
            return "项目终止"
        }
    } else if (projectTag == "Normal") {
        let result = "未知";
        switch (processStatus) {
            case "Approved":
                result = "已发号";
                break;
            case "PendingFirstApproval":
                result = "待发号";
                break;
            case "PendingSecondAssignment":
                result = "等待高级审核员分配";
                break;
            case "PendingThirdAssignment":
                result = "等待中级审核员分配";
                break;
            case "PendingFourthApproval":
                result = "待初审";
                break;
            case "RejectToApply":
                result = "驳回到用户";
                break;
            case "PendingThirdApproval":
                result = "待复审";
                break;
            case "PendingSecondApproval":
                result = "待核审";
                break;
        }


        const editProcessStatus = getFormDataValue(row.formData, "EditProcessStatus");
        if (editProcessStatus != null && editProcessStatus != "") {
            if (editProcessStatus == "Rejected") {
                result = "再修改申请驳回";
            } else if (editProcessStatus == "PendingFirstConfirmation") {
                result = "再修改申请审核中";
            } else if (editProcessStatus == "Approved") {
                if (processStatus == "Approved") {
                    result = "再修改申请审核通过";
                } else {
                    if (processStatus == "PendingFirstApproval") {
                        result = "再修改待复核";
                    } else {
                        result = result += "(再修改)";
                    }
                }
            }
        }
        return result;
    } else {
        return "未知状态";
    }
}

// 获取状态标签类型
export function getStatusTagType(status: FormInstanceStatus) {
    switch (status) {
        case FormInstanceStatus.Draft:
            return "info";
        case FormInstanceStatus.Submitted:
            return "warning";
        case FormInstanceStatus.Confirmed:
            return "success";
        case FormInstanceStatus.Cancelled:
            return "danger";
        case FormInstanceStatus.Rejected:
            return "danger";
        default:
            return "info";
    }


};

export function getMultiLanguageDisplay(row: any, fieldCode: string) {
    const data = row[fieldCode];

    let result = "";
    if (data.zh) {
        result += data.zh;
    }
    if (data.en) {
        if (data.zh) {
            result += "/";
        }
        result += data.en;
    }
    return result;
}

// getDisplay方法
export function getDisplay(field: FieldDto, row: any, fieldCode: string): string {

    if (!field) return "";
    // 这里假设有fieldTypeMap
    return fieldTypeMap[field.type].getDisplay(row.value[fieldCode], field);
}

export function getValueByDefinition(formDefinition: FormDefinitionDto, fieldCode: string): string | null {
    if (!formDefinition || !formDefinition.groups) return null;
    const result = formDefinition.groups
        .map(q => q.fields)
        .flat()
        .find(q => q.code === fieldCode);
    if (result != null) {
        return result.value;
    }
    return null;
}

export function getDisplayByDefinition(formDefinition: FormDefinitionDto, row: any, fieldCode: string): string {
    if (!formDefinition || !formDefinition.groups) return "";
    const field = formDefinition.groups
        .map((q: any) => q.fields)
        .flat()
        .find((q: any) => q.code === fieldCode);
    if (!field) return "";

    return getDisplay(field, row, fieldCode);
}

export function getFormDataValue(formData: Record<string, string>, code: string) {

    if (!formData || !formData[code])
        return "";

    return formData[code];
}