export interface ProjectSearchResultDto {
  language: string;
  BusinessId: string;
  Version: string;
  FormCode: string;
  registration_number?: string;
  registration_status?: string;
  publictitle_zh?: string;
  publictitle_en?: string;
  english_acronym_zh?: string;
  english_acronym_en?: string;
  scientific_title_zh?: string;
  scientific_title_en?: string;
  scientific_title_acronym_zh?: string;
  scientific_title_acronym_en?: string;
  study_subject_id?: string;
  partner_registry_number?: string;
  applicant_zh?: string;
  applicant_en?: string;
  study_leader_zh?: string;
  study_leader_en?: string;
  applicant_telephone?: string;
  study_leader_telephone?: string;
  applicant_fax?: string;
  study_leader_fax?: string;
  applicant_email?: string;
  study_leader_email?: string;
  applicant_website?: string;
  study_leader_website?: string;
  applicant_address_zh?: string;
  applicant_address_en?: string;
  study_leader_address_zh?: string;
  study_leader_address_en?: string;
  applicant_postcode?: string;
  study_leader_postcode?: string;
  applicant_affiliation_zh?: string;
  applicant_affiliation_en?: string;
  study_leader_affiliation_zh?: string;
  study_leader_affiliation_en?: string;
  ethic_committee_approved?: string;
  ethic_committee_approved_no?: string;
  ethic_committee_name_zh?: string;
  ethic_committee_name_en?: string;
  ethic_committee_approved_date?: string;
  ethic_committee_contact_zh?: string;
  ethic_committee_contact_en?: string;
  ethic_committee_address_zh?: string;
  ethic_committee_address_en?: string;
  ethic_committee_phone?: string;
  ethic_committee_email?: string;
  mpa_approved_no?: string;
  mpa_approved_date?: string;
  primary_sponsor_zh?: string;
  primary_sponsor_en?: string;
  primary_sponsor_address_zh?: string;
  primary_sponsor_address_en?: string;
  funding_source_zh?: string;
  funding_source_en?: string;
  target_disease_zh?: string;
  target_disease_en?: string;
  target_disease_code?: string;
  study_type?: string;
  study_design?: string;
  study_phase?: string;
  study_objectives_zh?: string;
  study_objectives_en?: string;
  treatment_description_zh?: string;
  treatment_description_en?: string;
  inclusion_criteria_zh?: string;
  inclusion_criteria_en?: string;
  exclusion_criteria_zh?: string;
  exclusion_criteria_en?: string;
  study_time_start?: string;
  study_time_end?: string;
  recruiting_time_start?: string;
  recruiting_time_end?: string;
  gold_standard_zh?: string;
  gold_standard_en?: string;
  index_test_zh?: string;
  index_test_en?: string;
  target_condition_zh?: string;
  target_condition_en?: string;
  target_sample_size?: string;
  confounding_condition_zh?: string;
  confounding_condition_en?: string;
  confounding_sample_size?: string;
  intervention_total_sample_size?: string;
  recruiting_status?: string;
  age_range_min?: string;
  age_range_max?: string;
  gender?: string;
  randomization_procedure_zh?: string;
  randomization_procedure_en?: string;
  sign_informed_consent?: string;
  follow_up_length?: string;
  follow_up_unit?: string;
  allocation_concealment_zh?: string;
  allocation_concealment_en?: string;
  blinding_zh?: string;
  blinding_en?: string;
  unblinding_rules_zh?: string;
  unblinding_rules_en?: string;
  statistical_methods_zh?: string;
  statistical_methods_en?: string;
  calculated_results_zh?: string;
  calculated_results_en?: string;
  calculated_results_public?: string;
  utn?: string;
  ipd_sharing?: string;
  ipd_sharing_way_zh?: string;
  ipd_sharing_way_en?: string;
  data_collection_zh?: string;
  data_collection_en?: string;
  safety_committee?: string;
  publication_info_zh?: string;
  publication_info_en?: string;
}

export interface ProjectHumanSampleDto {
  sample_name_zh?: string;
  sample_name_en?: string;
  tissue_zh?: string;
  tissue_en?: string;
  fate_of_sample?: string;
  sample_note_zh?: string;
  sample_note_en?: string;
  row_index?: number;
}

export interface ProjectInterventionDto {
  intervention_group_zh?: string;
  intervention_group_en?: string;
  intervention_sample_size?: string;
  intervention_name_zh?: string;
  intervention_name_en?: string;
  intervention_code?: string;
  row_index?: number;
}

export interface ProjectSponsorDto {
  sponsor_country_zh?: string;
  sponsor_country_en?: string;
  sponsor_province_zh?: string;
  sponsor_province_en?: string;
  sponsor_city_zh?: string;
  sponsor_city_en?: string;
  sponsor_institution_zh?: string;
  sponsor_institution_en?: string;
  sponsor_address_zh?: string;
  sponsor_address_en?: string;
  row_index?: number;
}

export interface ProjectResearchSiteDto {
  site_country_zh?: string;
  site_country_en?: string;
  site_province_zh?: string;
  site_province_en?: string;
  site_city_zh?: string;
  site_city_en?: string;
  site_institution_zh?: string;
  site_institution_en?: string;
  site_level_zh?: string;
  site_level_en?: string;
  row_index?: number;
}

export interface ProjectMeasurementDto {
  outcome_name_zh?: string;
  outcome_name_en?: string;
  outcome_type?: string;
  measure_time_point_zh?: string;
  measure_time_point_en?: string;
  measure_method_zh?: string;
  measure_method_en?: string;
  row_index?: number;
}

export interface ProjectViewDto {
  language: string;
  business_id: string;
  version: string;
  form_code: string;
  registration_number?: string;
  registration_status?: string;
  publictitle_zh?: string;
  publictitle_en?: string;
  english_acronym_zh?: string;
  english_acronym_en?: string;
  scientific_title_zh?: string;
  scientific_title_en?: string;
  scientific_title_acronym_zh?: string;
  scientific_title_acronym_en?: string;
  study_subject_id?: string;
  partner_registry_number?: string;
  applicant_zh?: string;
  applicant_en?: string;
  study_leader_zh?: string;
  study_leader_en?: string;
  applicant_telephone?: string;
  study_leader_telephone?: string;
  applicant_fax?: string;
  study_leader_fax?: string;
  applicant_email?: string;
  study_leader_email?: string;
  applicant_website?: string;
  study_leader_website?: string;
  applicant_address_zh?: string;
  applicant_address_en?: string;
  study_leader_address_zh?: string;
  study_leader_address_en?: string;
  applicant_postcode?: string;
  study_leader_postcode?: string;
  applicant_affiliation_zh?: string;
  applicant_affiliation_en?: string;
  study_leader_affiliation_zh?: string;
  study_leader_affiliation_en?: string;
  ethic_committee_approved?: string;
  ethic_committee_approved_no?: string;
  ethic_committee_name_zh?: string;
  ethic_committee_name_en?: string;
  ethic_committee_approved_date?: string;
  ethic_committee_contact_zh?: string;
  ethic_committee_contact_en?: string;
  ethic_committee_address_zh?: string;
  ethic_committee_address_en?: string;
  ethic_committee_phone?: string;
  ethic_committee_email?: string;
  mpa_approved_no?: string;
  mpa_approved_date?: string;
  primary_sponsor_zh?: string;
  primary_sponsor_en?: string;
  primary_sponsor_address_zh?: string;
  primary_sponsor_address_en?: string;
  funding_source_zh?: string;
  funding_source_en?: string;
  target_disease_zh?: string;
  target_disease_en?: string;
  target_disease_code?: string;
  study_type?: string;
  study_design?: string;
  study_phase?: string;
  study_objectives_zh?: string;
  study_objectives_en?: string;
  treatment_description_zh?: string;
  treatment_description_en?: string;
  inclusion_criteria_zh?: string;
  inclusion_criteria_en?: string;
  exclusion_criteria_zh?: string;
  exclusion_criteria_en?: string;
  study_time_start?: string;
  study_time_end?: string;
  recruiting_time_start?: string;
  recruiting_time_end?: string;
  gold_standard_zh?: string;
  gold_standard_en?: string;
  index_test_zh?: string;
  index_test_en?: string;
  target_condition_zh?: string;
  target_condition_en?: string;
  target_sample_size?: string;
  confounding_condition_zh?: string;
  confounding_condition_en?: string;
  confounding_sample_size?: string;
  intervention_total_sample_size?: string;
  recruiting_status?: string;
  age_range_min?: string;
  age_range_max?: string;
  gender?: string;
  randomization_procedure_zh?: string;
  randomization_procedure_en?: string;
  sign_informed_consent?: string;
  follow_up_length?: string;
  follow_up_unit?: string;
  allocation_concealment_zh?: string;
  allocation_concealment_en?: string;
  blinding_zh?: string;
  blinding_en?: string;
  unblinding_rules_zh?: string;
  unblinding_rules_en?: string;
  statistical_methods_zh?: string;
  statistical_methods_en?: string;
  calculated_results_zh?: string;
  calculated_results_en?: string;
  calculated_results_public?: string;
  utn?: string;
  ipd_sharing?: string;
  ipd_sharing_way_zh?: string;
  ipd_sharing_way_en?: string;
  data_collection_zh?: string;
  data_collection_en?: string;
  safety_committee?: string;
  publication_info_zh?: string;
  publication_info_en?: string;
  first_submit_time?: string;
  humanSample?: ProjectHumanSampleDto[];
  intervention?: ProjectInterventionDto[];
  sponsor?: ProjectSponsorDto[];
  researchSite?: ProjectResearchSiteDto[];
  measurement?: ProjectMeasurementDto[];
}