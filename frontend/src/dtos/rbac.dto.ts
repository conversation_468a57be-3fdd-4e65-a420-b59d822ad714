import { CommonStatus, UserStatus, UserType } from "@/enums"

// 登录结果
export interface LoginResult {
    accessToken?: string
    refreshToken?: string
    tokenType?: string
    expiresIn?: number
    menuPermissions: string[]
    buttonPermissions: string[]
    roles?: string[]
    positions?: string[]
}


// 更新个人资料参数
export interface UpdateProfileParams {
    realName?: string
    avatar?: string
    gender?: string
    country?: string
    unit?: string
    contactAddress?: string
    telephone?: string
}

// 用户信息
export interface UserInfo {
    key: string
    username?: string
    realName?: string
    email?: string
    mobile?: string
    avatar?: string
    userType: UserType
    status: UserStatus
    emailConfirmed?: boolean
    mobileConfirmed?: boolean
    gender?: string
    country?: string
    unit?: string
    contactAddress?: string
    telephone?: string
}


// 验证码结果
export interface CaptchaResult {
    id: string
    image: string
    expiresIn: number
}


// 登录参数
export interface LoginParams {
    username: string
    password: string
    captchaId?: string
    captchaCode?: string
    clientIp?: string
    deviceInfo?: string
    deviceId?: string
    appId?: string
    appCode?: string
}


// 刷新令牌参数
export interface RefreshTokenParams {
    refreshToken: string
    clientIp?: string
    deviceInfo?: string
    deviceId?: string
}

// 发送邮箱确认码参数
export interface SendEmailConfirmationCodeParams {
    email: string
    captchaId: string
    captchaCode: string
}

// 确认邮箱参数
export interface ConfirmEmailParams {
    email: string
    code: string
}

// 发送手机确认码参数
export interface SendPhoneConfirmationCodeParams {
    phoneNumber: string
    captchaId: string
    captchaCode: string
}

// 修改密码参数
export interface ChangePasswordParams {
    oldPassword: string
    newPassword: string
    confirmNewPassword: string
    captchaId?: string
    captchaCode?: string
}
// 重置密码参数
export interface ResetPasswordParams {
    username: string
    phoneNumber?: string
    email?: string
    code: string
    newPassword: string
    confirmNewPassword: string
}
// 用户注册
export interface RegisterParams {
    username: string
    password: string
    repassword: string
    oripassword: string
    orirepassword: string
    email: string
    realname: string
    gender: string
    country: string
    unit: string
    contactaddress: string
    mobile: string
    telephone: string
}

export interface PositionResult {
    roleid: string
    code: string
    name: string
    status: CommonStatus
    description: string
}

export interface PositionDto {
    key: string
    code: string
    name: string
    status: CommonStatus
    description?: string
}