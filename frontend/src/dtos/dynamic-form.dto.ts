import {FormInstanceStatus, FieldSortDirection} from '@/enums'

/**
 * 选项DTO
 */
export interface OptionDto {
    labelZh: string
    labelEn: string
    value: string
}

/**
 * 多类型值
 */
export interface MultiTypeValue {
    stringValue?: string
    intValue?: number
    stringArray?: string[]
    keyValue?: Record<string, any>
    objectArray?: Record<string, any>[]
}

/**
 * 字段DTO
 */
export interface FieldDto {
    [x: string]: {}

    id?: string
    code: string
    type: string
    newLine?: boolean
    labelZh: string
    labelEn: string
    required?: boolean
    unit?: number
    annotations: Record<string, string | any>
    options?: OptionDto[]
    value?: MultiTypeValue
    previousValue?: MultiTypeValue
    extends?: Record<string, any>
    fields?: FieldDto[]
}

/**
 * 分组DTO
 */
export interface GroupDto {
    id?: string
    code: string
    labelZh: string
    labelEn: string
    fields?: FieldDto[]
}

/**
 * 表单定义DTO
 */
export interface FormDefinitionDto {
    language: string
    groups: GroupDto[]
    formData: Record<string, string | any>
}

export interface NewFormInstanceDto {
    version: string
    businessId: string
}

/**
 * 表单实例DTO
 */
export interface FormInstanceDto {
    key: string // 使用string代替number，避免精度问题
    formCode: string
    formVersion: string
    businessId: string
    version: string
    previousInstanceId?: string // 使用string代替number，避免精度问题
    versionTime: string
    previousVersion?: string
    status: FormInstanceStatus
    isObsoleted?: boolean
}

export interface FormInstanceJsonDto {
    formCode: string
    formVersion: string
    version: string
    businessId: string
    status: FormInstanceStatus
    value: Record<string, any>
    formData: Record<string, string>
}

/**
 * 排序请求项接口
 */
export interface IOrderByRequestItem {
    dataField?: string
    sortDirection: FieldSortDirection
}

/**
 * 表单实例分页查询参数
 */
export interface UserFormInstancePageQueryParams {
    formCode?: string
    status?: FormInstanceStatus[]
    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    dynamicQueries?: Record<string, any>
    $orderBy?: 'asc' | 'desc'
}


export interface InternalPageQueryParams {

    $pageIndex?: number
    $pageSize?: number
    $sortBy?: string
    $orderBy?: 'asc' | 'desc'
    dynamicQueries?: Record<string, any>
    formDataDynamicQueries?: Record<string, any>
}