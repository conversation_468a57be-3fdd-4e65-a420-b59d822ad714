<template>
  <div class="pdf-viewer-container">
    <div class="pdf-controls">
      <!-- 分页控件 -->
      <div class="pagination">
        <button @click="prevPage" :disabled="currentPage <= 1">上一页</button>
        <span>{{ currentPage }} / {{ totalPages }}</span>
        <button @click="nextPage" :disabled="currentPage >= totalPages">
          下一页
        </button>
      </div>
      <!-- 缩放控件 -->
      <div class="zoom-controls">
        <span>缩放: {{ (scale * 100).toFixed(0) }}%</span>
        <button @click="zoomIn">+</button>
        <button @click="zoomOut">-</button>
        <!-- 后续可以添加缩放按钮或滑块 -->
      </div>
    </div>
    <!-- 新增容器用于包裹 Canvas 并实现滚动 -->
    <div class="canvas-scroll-container" ref="canvasScrollContainerRef">
      <!-- PDF 渲染 Canvas -->
      <canvas ref="pdfCanvas"></canvas>
      <!-- 高亮/选择框绘制 Canvas (叠加在 pdfCanvas 上方) -->
      <!-- selectionCanvas 现在只用于绘制外部传入的高亮，不再用于实时选择 -->
      <canvas ref="selectionCanvas" class="selection-canvas"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  watch,
  onBeforeUnmount,
  nextTick,
  PropType,
} from "vue";
// 导入 PDF.js 库
import * as pdfjsLib from "pdfjs-dist";
import {
  PDFDocumentProxy,
  PDFPageProxy,
  TextContent,
  TextItem,
  TextMarkedContent,
} from "pdfjs-dist/types/src/display/api";
// 导入 FormCompareReferenceAreaDto 类型
import type {FormCompareReferenceAreaDto} from "@/dtos/itmctr";

// 设置 PDF.js worker 路径
pdfjsLib.GlobalWorkerOptions.workerSrc =
    import.meta.env.BASE_URL + "pdf.worker.min.mjs";

// 定义高亮数据结构
interface HighlightArea {
  page: number; // 高亮所在的页码 (1-based)
  x1: number; // 高亮矩形左上角的 Canvas x 坐标
  y1: number; // 高亮矩形左上角的 Canvas y 坐标
  x2: number; // 高亮矩形右下角的 Canvas x 坐标
  y2: number; // 高亮矩形右下角的 Canvas y 坐标
  text?: string; // 可选：高亮对应的文本内容，仅用于参考
}

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  // 接收 FormCompareReferenceAreaDto[] 类型的 prop
  highlightedAreas: {
    type: Array as PropType<FormCompareReferenceAreaDto[]>,
    default: () => [],
  },
  scale: {
    type: Number,
    default: 1.5,
  },
});

const pdfCanvas = ref<HTMLCanvasElement | null>(null);
// 添加选择框绘制 Canvas 的引用
const selectionCanvas = ref<HTMLCanvasElement | null>(null);
// 添加对 canvas-scroll-container 的引用
const canvasScrollContainerRef = ref<HTMLDivElement | null>(null);

let pdfDoc: PDFDocumentProxy | null = null;
// 跟踪当前的渲染任务
let renderTask: pdfjsLib.RenderTask | null = null;
// 跟踪上一个渲染任务是否已完成清理
let lastRenderTaskFinished = true;

// 页码和总页数
const currentPage = ref(1);
const totalPages = ref(0);

// 缩放比例
const scale = ref(props.scale); // 初始缩放比例由props决定

const renderPage = async (pageNum: number) => {
  if (!pdfDoc || !pdfCanvas.value || !selectionCanvas.value) return; // 确保 selectionCanvas 也存在

  // 如果有正在进行的渲染任务，先取消它
  if (renderTask) {
    console.log("PdfViewer: Canceling previous render task");
    renderTask.cancel();
  }

  // 等待上一个任务清理完毕（如果它被取消）
  while (!lastRenderTaskFinished) {
    await new Promise((resolve) => setTimeout(resolve, 50));
  }

  lastRenderTaskFinished = false; // 标记新的任务开始
  renderTask = null; // 在这里重置，确保即使取消也进入 finally

  try {
    const page: PDFPageProxy = await pdfDoc.getPage(pageNum);
    const viewport = page.getViewport({scale: scale.value}); // 使用当前的缩放比例

    const canvas = pdfCanvas.value;
    const context = canvas.getContext("2d");
    const selCanvas = selectionCanvas.value; // 获取 selectionCanvas
    const selContext = selCanvas.getContext("2d"); // 获取 selectionCanvas 的 context

    if (!context || !selContext) return;

    canvas.height = viewport.height;
    canvas.width = viewport.width;

    // 设置选择 Canvas 的尺寸，使其与 PDF Canvas 相同
    selCanvas.height = viewport.height;
    selCanvas.width = viewport.width;

    // 在渲染新页面前清除 selectionCanvas 上的旧高亮
    selContext.clearRect(0, 0, selCanvas.width, selCanvas.height);

    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };

    // 启动新的渲染任务并存储它
    renderTask = page.render(renderContext);

    await renderTask.promise;
    console.log(`PdfViewer: Page ${pageNum} rendered`);

    // 渲染完成后清除任务变量
    renderTask = null;

    // 在 PDF 页面渲染完成后绘制高亮
    drawHighlightsOnPage(pageNum, props.highlightedAreas, true);

    // 在页面渲染完成后更新滚动容器高度
    updateScrollContainerHeight();

    // 在页面渲染完成后将滚动容器滚动到顶部
    if (canvasScrollContainerRef.value) {
      canvasScrollContainerRef.value.scrollTop = 0;
      console.log("PdfViewer: Scrolled canvas container to top.");
    }
  } catch (error) {
    // 如果错误是由于任务取消引起的，则忽略
    if ((error as any).name === "RenderingCancelledException") {
      console.log("PdfViewer: Rendering cancelled");
    } else {
      console.error("PdfViewer: Error rendering page:", error);
    }
  } finally {
    // 确保在任务结束 (成功或失败，包括取消) 后清理任务变量并标记完成
    renderTask = null;
    lastRenderTaskFinished = true;
    console.log(`PdfViewer: Page ${pageNum} render task finished`);
  }
};

// 在 Selection Canvas 上绘制高亮矩形
const drawHighlightsOnPage = (
    currentPageNum: number,
    allHighlightsData: FormCompareReferenceAreaDto[],
    autoScrollToFirstHighlight = false // 新增参数，默认不自动滚动
) => {
  console.log(
      "PdfViewer: Attempting to draw highlights on page",
      currentPageNum,
      "with data:",
      allHighlightsData
  );
  if (!selectionCanvas.value || !pdfDoc) return;

  const selCanvas = selectionCanvas.value;
  const selContext = selCanvas.getContext("2d");
  if (!selContext) return;

  // 清除当前页之前绘制的高亮
  selContext.clearRect(0, 0, selCanvas.width, selCanvas.height);

  // 获取当前页的高亮数据，确保 pageNumber 存在且与当前页匹配
  const pageHighlights = allHighlightsData.filter((h) => {
    const isCurrentPage = h.pageNumber === currentPageNum;
    const isValidArea =
        h.x0 != null && h.y0 != null && h.x1 != null && h.y1 != null;
    if (isCurrentPage && !isValidArea) {
      console.warn(
          "PdfViewer: Skipping invalid highlight area on current page:",
          h
      );
    }
    return isCurrentPage && isValidArea;
  });

  console.log(
      "PdfViewer: Filtered highlights for current page",
      currentPageNum,
      ":",
      pageHighlights
  );

  if (pageHighlights.length === 0) {
    console.log("PdfViewer: No highlights to draw for page", currentPageNum);
    return; // 如果当前页没有高亮，直接返回
  }

  selContext.save(); // 保存当前 Canvas 状态

  selContext.fillStyle = "yellow"; // 高亮颜色
  selContext.globalAlpha = 0.3; // 半透明

  pageHighlights.forEach((area) => {
    // 假设 area 的 x0, x1, y0, y1 是距离边缘的边距值 (在当前 scale 的 Canvas 坐标系下)
    // 计算矩形的左上角坐标和宽高
    const canvasWidth = selCanvas.width;
    const canvasHeight = selCanvas.height;

    const rectX = area.x0! * scale.value;
    const rectY = area.y0! * scale.value;
    const rectWidth = (area.x1! - area.x0!) * scale.value;
    const rectHeight = (area.y1! - area.y0!) * scale.value;

    console.log(
        `PdfViewer: Drawing highlight on page ${currentPageNum} ` +
        `at [${rectX}, ${rectY}] with size [${rectWidth}, ${rectHeight}] ` +
        `based on margins [${area.x0}, ${area.y0}, ${area.x1}, ${area.y1}] on canvas size [${canvasWidth}, ${canvasHeight}]`,
        area
    ); // Added log

    // 检查计算出的宽高是否有效
    if (rectWidth > 0 && rectHeight > 0) {
      selContext.fillRect(rectX, rectY, rectWidth, rectHeight);
    } else {
      console.warn(
          "PdfViewer: Skipping highlight with zero or negative calculated width/height based on margins:",
          area
      );
    }
  });

  selContext.restore(); // 恢复之前保存的 Canvas 状态
  console.log(
      `PdfViewer: Highlights drawn on selection canvas for page ${currentPageNum}`
  );

  // === 新增：自动滚动到第一个高亮区域完整可见 ===
  if (autoScrollToFirstHighlight && canvasScrollContainerRef.value && pageHighlights.length > 0) {
    nextTick(() => {
      const first = pageHighlights[0];
      const rectY = first.y0! * scale.value;
      const rectHeight = (first.y1! - first.y0!) * scale.value;
      const container = canvasScrollContainerRef.value!;
      const viewHeight = container.clientHeight;
      const currentScroll = container.scrollTop;
      console.log('[PdfViewer] 自动滚动调试: rectY=', rectY, 'rectHeight=', rectHeight, 'scrollTop=', currentScroll, 'clientHeight=', viewHeight);

      if (rectY < currentScroll) {
        // 高亮顶部在可视区上方，顶部对齐
        container.scrollTop = rectY;
        console.log('[PdfViewer] 自动滚动: 高亮顶部在可视区上方，顶部对齐 scrollTop =', rectY);
      } else if (rectY + rectHeight > currentScroll + viewHeight) {
        // 高亮底部在可视区下方，底部对齐
        container.scrollTop = rectY + rectHeight - viewHeight;
        console.log('[PdfViewer] 自动滚动: 高亮底部在可视区下方，底部对齐 scrollTop =', container.scrollTop);
      } else {
        console.log('[PdfViewer] 自动滚动: 高亮区域本就在可视区内，无需滚动');
      }
    });
  }
};

const loadPdf = async (url: string) => {
  if (!url) return;

  // 在加载新的 PDF 之前，取消任何正在进行的渲染任务
  if (renderTask) {
    console.log("PdfViewer: Canceling render task before loading new PDF");
    renderTask.cancel();
  }
  // 确保上一个任务清理完毕
  while (!lastRenderTaskFinished) {
    await new Promise((resolve) => setTimeout(resolve, 50));
  }

  try {
    // 销毁旧的 PDF 文档
    if (pdfDoc) {
      pdfDoc.destroy();
      pdfDoc = null;
    }

    const loadingTask = pdfjsLib.getDocument(url);
    pdfDoc = await loadingTask.promise;

    console.log("PdfViewer: PDF loaded");
    totalPages.value = pdfDoc.numPages; // 获取总页数
    currentPage.value = 1; // 重置到第一页
    // 使用 nextTick 确保 DOM 更新后执行渲染操作
    nextTick(() => {
      renderPage(currentPage.value); // 渲染当前页
    });
  } catch (error) {
    console.error("PdfViewer: Error loading PDF:", error);
    pdfDoc = null;
    totalPages.value = 0;
    currentPage.value = 0;
  }
};

// 分页方法
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    // renderPage 会被 watch 触发
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    // renderPage 会被 watch 触发
  }
};

// 缩放方法
const zoomIn = () => {
  scale.value += 0.25;
  // renderPage 会被 watch 触发
};

const zoomOut = () => {
  if (scale.value > 0.25) {
    // 防止缩放过小
    scale.value -= 0.25;
    // renderPage 会被 watch 触发
  }
};

const setScale = (newScale: number) => {
  scale.value = newScale;
  // renderPage 会被 watch 触发
};

// 组件挂载时加载 PDF
onMounted(() => {
  loadPdf(props.src);
  window.addEventListener("resize", updateScrollContainerHeight);
  // 初始设置高度，需要等待DOM渲染
  // nextTick might be needed if initial rendering causes layout shifts
  // For now, call directly, might need adjustment
  updateScrollContainerHeight();
});

// 组件销毁时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener("resize", updateScrollContainerHeight);
  // 销毁 PDF 文档
  if (pdfDoc) {
    pdfDoc.destroy();
    pdfDoc = null;
  }
});

// 监听 src 变化重新加载 PDF
watch(
    () => props.src,
    (newSrc) => {
      loadPdf(newSrc);
    }
);

// 监听 currentPage 和 scale 变化重新渲染页面并重置滚动条
watch([currentPage, scale], () => {
  renderPage(currentPage.value);
});

// 监听 highlightedAreas prop 变化，重新绘制当前页的高亮
watch(
    () => props.highlightedAreas,
    (newHighlights) => {
      console.log("PdfViewer: highlightedAreas prop changed.", newHighlights);
      // 在 highlightedAreas 数据变化时，重新绘制当前页的高亮
      // 不需要重新渲染整个 PDF 页面，只需要更新 selectionCanvas
      drawHighlightsOnPage(currentPage.value, newHighlights, true);
    },
    {deep: true, immediate: true}
); // deep: true 监听内部对象变化， immediate: true 在 watch 建立时立即执行一次

// 监听 props.scale 变化时同步 scale
watch(
    () => props.scale,
    (newScale) => {
      scale.value = newScale;
    }
);

// 获取指定页面的文本内容和位置信息 (保留此函数，外部可能需要它来计算高亮位置)
const getPageTextContentWithPositions = async (pageNum: number) => {
  if (!pdfDoc) return null;

  try {
    const page = await pdfDoc.getPage(pageNum);
    const textContent = await page.getTextContent();

    const viewport = page.getViewport({scale: scale.value}); // 使用当前的 viewer scale 获取 viewport

    const textItems = textContent.items.filter(
        (item): item is TextItem => typeof (item as any).str === "string"
    ) as TextItem[];

    const textItemsWithBboxes = textItems.map((item) => {
      // item.transform 是一个 [fontScaleX, skewY, skewX, fontScaleY, offsetX, offsetY] 数组
      // offsetX (item.transform[4]) 和 offsetY (item.transform[5]) 是文本基线在 PDF 原始坐标系中的位置
      const pdfX = item.transform[4];
      const pdfY = item.transform[5];

      // PDF 坐标系原点在左下角，Canvas/DOM 坐标系原点在左上角
      // 需要将 PDF 坐标转换为 Canvas 坐标
      // Canvas X = PDF X * scale
      // Canvas Y = (Viewport Height - PDF Y) * scale
      // item.height is the vertical extent of the font in PDF space
      const itemHeight = item.height; // 原始 PDF 字体高度

      // 计算文本项在 Canvas 坐标系中的边界框
      // x1, y1 是左上角，x2, y2 是右下角

      // 计算文本块顶部在 Canvas 坐标系中的 Y 坐标
      // PDF 文本基线 Y 是 pdfY，高度是 itemHeight
      // 文本块顶部在 PDF 坐标系中的 Y 坐标大约是 pdfY + itemHeight
      // 转换为 Canvas 坐标需要翻转 Y 轴并应用缩放
      const canvasYTop = (viewport.height - (pdfY + itemHeight)) * scale.value; // 顶部 Y 坐标
      const canvasYBottom = (viewport.height - pdfY) * scale.value; // 基线 Y 坐标 (近似底部)

      const bbox = {
        x1: pdfX * scale.value, // 应用当前的 viewer 缩放
        y1: canvasYTop, // 应用 Y 轴翻转和当前的 viewer 缩放
        x2: (pdfX + item.width) * scale.value, // 应用文本宽度和 scaleX，再应用当前的 viewer 缩放
        y2: canvasYBottom, // 应用 Y 轴翻转和当前的 viewer 缩放
      };

      // 确保 y1 < y2
      if (bbox.y1 > bbox.y2) {
        // 应对某些特殊字符或排版导致的 Y 坐标异常
        [bbox.y1, bbox.y2] = [bbox.y2, bbox.y1];
      }
      // 确保 x1 < x2
      if (bbox.x1 > bbox.x2) {
        [bbox.x1, bbox.x2] = [bbox.x2, bbox.x1];
      }

      return {
        text: item.str,
        bbox: bbox,
        transform: item.transform, // 原始 transform 也可能有用
      };
    });

    console.log(
        `Text content and positions for page ${pageNum}:`,
        textItemsWithBboxes
    );
    return textItemsWithBboxes;
  } catch (error) {
    console.error(`Error getting text content for page ${pageNum}:`, error);
    return null;
  }
};

// 动态计算并设置滚动容器的高度
const updateScrollContainerHeight = () => {
  const container = canvasScrollContainerRef.value;
  if (container) {
    const rect = container.getBoundingClientRect();
    const availableHeight = window.innerHeight - rect.top;
    // 设置高度，确保不会出现负值
    // 减去一个偏移量，以account for 顶部控制栏的高度和可能的底部间距
    const offset = 75; // 根据实际布局调整此值
    container.style.height = `${Math.max(0, availableHeight - offset)}px`;
    console.log(
        `PdfViewer: Canvas scroll container height set to ${Math.max(
            0,
            availableHeight - offset
        )}px`
    );
  }
};

// 暴露方法供父组件使用
defineExpose({
  renderPage,
  loadPdf,
  prevPage,
  nextPage,
  zoomIn,
  zoomOut,
  setScale,
  currentPage,
  totalPages,
  pdfDoc, // 暴露 pdfDoc 实例用于后续高亮，但要注意内存管理
  getPageTextContentWithPositions, // 暴露获取文本内容和位置的方法
});
</script>

<style lang="scss" scoped>
.pdf-viewer-container {
  display: flex; /* 设置为 Flex 容器 */
  flex-direction: column; /* 子元素垂直布局 */
  flex-grow: 1; /* 填充父容器的剩余垂直空间 */
  min-height: 0; /* 允许缩小 */
  /* 滚动条现在由 canvas-scroll-container 控制，这里移除 overflow-y */
  overflow-y: hidden; /* 隐藏自身的滚动条 */
  /* 移除相对定位，由 canvas-scroll-container 提供 */
  /* position: relative; */
}

.pdf-controls {
  flex-shrink: 0; /* 控制区域不缩小 */
  padding: 10px;
  //background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.pagination,
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination {
  button {
    background-color: #fff;
  }

  button:disabled {
    background-color: #fff;
  }
}

.canvas-scroll-container {
  flex-grow: 1; /* 填充 pdf-viewer-container 的剩余垂直空间 */
  min-height: 0; /* 允许缩小 */
  overflow-y: auto; /* 内容溢出时显示垂直滚动条 */
  position: relative; /* 为内部绝对定位的 canvas 提供定位上下文 */
}

canvas {
  display: block;
  margin: 0 auto;
  max-width: 100%; /* 确保 Canvas 不超出父容器宽度 */
  /* Canvas 的高度由脚本根据 PDF 页面尺寸和缩放比例设置 */
  /* height: auto; */
}

/* 叠加 Canvas 的样式 */
/* 确保 selectionCanvas 叠加在 pdfCanvas 上方且位置一致 */
.selection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none; /* 允许鼠标事件穿透到 pdfCanvas */
}

/* 高亮样式 (不再需要单独的元素，直接绘制在 canvas 上) */
/* .highlight { ... } */
</style>
