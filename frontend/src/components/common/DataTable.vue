<template>
  <el-card class="table-card">
    <template #header>
      <div class="card-header">
        <span>{{ title }}</span>
        <div class="right-buttons">
          <slot name="toolbar"></slot>
        </div>
      </div>
    </template>
    <el-config-provider :locale="elementLocale">
      <el-table
          v-loading="loading"
          :data="data"
          border
          style="width: 100%"
          :row-key="rowKey"
          :default-sort="defaultSort"
          @sort-change="$emit('sort-change', $event)"
          :max-height="maxHeight"
          @selection-change="handleSelectionChange"
      >
        <!-- 选择列 -->
        <el-table-column 
          v-if="showSelection" 
          type="selection" 
          width="55" 
          :fixed="columns.some(c => c.fixed)"
          :selectable="selectable"
          :reserve-selection="reserveSelection"
        />
        
        <el-table-column v-if="showIndex" type="index" width="50" label="#" :fixed="columns.some(c => c.fixed)" />

        <template v-for="(column, index) in columns" :key="index">
          <el-table-column
              :sort-orders="column.sortOrders??['ascending', 'descending']"
              :prop="column.prop"
              :label="
            typeof column.label === 'object' ? column.label.zh : column.label
          "
              :min-width="column.width || 120"
              :formatter="column.formatter"
              :fixed="column.fixed"
              :sortable="column.sortable?'custom':false"
              show-overflow-tooltip
          >
            <template #default=" scope
        " v-if="column.slot">
              <slot
                  :name="column.slot"
                  :row="scope.row"
                  :index="scope.$index"
              ></slot>
            </template>
            <template v-if="typeof column.label === 'object'" #header>
              <div>{{ column.label.zh }}</div>
              <div>{{ column.label.en }}</div>
            </template>
          </el-table-column>
        </template>

        <el-table-column
            v-if="$slots.action"
            :label="actionColumnLabel"
            :width="actionWidth"
            fixed="right"
        >
          <template #default="scope">
            <slot name="action" :row="scope.row" :index="scope.$index"></slot>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container" v-if="showPagination">
        <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="pageSizes"
            :layout="paginationLayout"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-config-provider>
  </el-card>
</template>

<script setup lang="ts">
import {ref, watch, PropType} from "vue";
import {useI18n} from 'vue-i18n'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'

const {locale} = useI18n()
const elementLocale = computed(() => (locale.value === 'zh-CN' ? zhCn : en))

// 定义列类型
export interface TableColumn {
  prop: string;
  label: string | { zh: string; en: string };
  width?: number | string;
  fixed?: boolean,
  formatter?: (row: any, column: any, cellValue: any, index: number) => any;
  sortable?: boolean | string;
  slot?: string;
  sortOrders?: Array<"ascending" | "descending" | null>;
}

// 定义组件 props
const props = defineProps({
  title: {
    type: String,
    default: "数据列表",
  },
  data: {
    type: Array,
    required: true,
  },
  columns: {
    type: Array as () => TableColumn[],
    required: true,
  },
  rowKey: {
    type: String,
    default: "key",
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showIndex: {
    type: Boolean,
    default: true,
  },
  showPagination: {
    type: Boolean,
    default: true,
  },
  currentPageProp: {
    type: Number,
    default: 1,
  },
  pageSizeProp: {
    type: Number,
    default: 10,
  },
  pageSizes: {
    type: Array as () => number[],
    default: () => [10, 20, 50, 100],
  },
  total: {
    type: Number,
    default: 0,
  },
  paginationLayout: {
    type: String,
    default: "total, sizes, prev, pager, next, jumper",
  },
  actionWidth: {
    type: [Number, String],
    default: 280,
  },
  actionColumnLabel: {
    type: String,
    default: "操作",
  },
  defaultSort: {
    type: Object as () => { prop: string; order: "ascending" | "descending" },
    default: () => ({prop: "", order: "ascending"}),
  },
  maxHeight: {
    type: [Number, String],
    default: "auto",
  },
  showSelection: {
    type: Boolean,
    default: false,
  },
  selectable: {
    type: Function as PropType<(row: any, index: number) => boolean>,
    default: () => () => true,
  },
  reserveSelection: {
    type: Boolean,
    default: false,
  },
  selectedRows: {
    type: Array as () => any[],
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits(['sort-change', 'page-change', 'size-change', 'selection-change']);

// 分页数据
const currentPage = ref(props.currentPageProp);
const pageSize = ref(props.pageSizeProp);

// 监听分页属性变化
watch(
    () => props.currentPageProp,
    (val) => {
      console.log(
          "DataTable - 监听到 currentPageProp 变化:",
          val,
          "当前值:",
          currentPage.value
      );
      currentPage.value = val;
    },
    {immediate: true}
);

watch(
    () => props.pageSizeProp,
    (val) => {
      console.log(
          "DataTable - 监听到 pageSizeProp 变化:",
          val,
          "当前值:",
          pageSize.value
      );
      pageSize.value = val;
    },
    {immediate: true}
);

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  console.log("DataTable - 每页条数变化:", size, "当前值:", pageSize.value);
  pageSize.value = size;
  console.log("DataTable - 每页条数已更新:", pageSize.value);
  emit("size-change", size);
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  emit("page-change", page);
};

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  emit("selection-change", selection);
};
</script>
