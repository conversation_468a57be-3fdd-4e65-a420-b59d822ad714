<template>
  <div class="input-property-editor">
    <el-form-item label="自动高度" v-if="props.field.type === 'input'">
      <el-checkbox v-model="autoSize"
                   placeholder=""
                   @input="onChange"/>

    </el-form-item>
    <el-form-item label="最小长度">
      <el-input
          v-model="minLength"
          placeholder=""
          @input="onChange"
      />
    </el-form-item>
    <el-form-item label="最大长度">
      <el-input
          v-model="maxLength"
          placeholder=""
          @input="onChange"
      />
    </el-form-item>


    <el-form-item label="使用正则表达式">
      <el-input
          v-model="pattern"
          placeholder=""
          @input="onChange"
      />
    </el-form-item>
    <el-form-item label="正则验证提示（中文）">
      <el-input
          v-model="patternMsgZh"
          placeholder=""
          @input="onChange"
      />
    </el-form-item>
    <el-form-item label="正则验证提示（英文）">
      <el-input
          v-model="patternMsgEn"
          placeholder=""
          @input="onChange"
      />
    </el-form-item>
  </div>

</template>

<script setup lang="ts">
import {ref, watch, onMounted} from "vue";
import {ElFormItem, ElInput} from "element-plus";

const props = defineProps({
  field: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["change"]);

// 确保 extends 对象存在
if (!props.field.extends) {
  props.field.extends = {};
}
if (!props.field.extends.patternMsg) {
  props.field.extends.patternMsg = {zh: "", en: ""};
}


const minLength = ref(props.field.extends.minLength || null);
const maxLength = ref(props.field.extends.maxLength || null);
const pattern = ref(props.field.extends.pattern || "");
const patternMsgZh = ref(props.field.extends.patternMsg.zh || "");
const patternMsgEn = ref(props.field.extends.patternMsg.en || "");
const autoSize = ref(props.field.extends.autoSize || false);
// 监听属性变化
watch(autoSize, (newVal) => {
  props.field.extends.autoSize = newVal;
  // onChange();
});
watch(minLength, (newVal) => {
  props.field.extends.minLength = newVal;
  // onChange();
});
watch(maxLength, (newVal) => {
  props.field.extends.maxLength = newVal;
  // onChange();
});
watch(pattern, (newVal) => {
  props.field.extends.pattern = newVal;
  // onChange();
});
watch(patternMsgZh, (newVal) => {
  props.field.extends.patternMsg ??= {};
  props.field.extends.patternMsg.zh = newVal;
  // onChange();
});
watch(patternMsgEn, (newVal) => {
  props.field.extends.patternMsg ??= {};
  props.field.extends.patternMsg.en = newVal;
  // onChange();
});

// 初始化
onMounted(() => {
});

// 当字段属性变更时触发
function onChange() {
  emit("change", props.field);
}
</script>

<style scoped>
.input-property-editor {
  /* 日期区间字段属性编辑器样式 */
}
</style>
