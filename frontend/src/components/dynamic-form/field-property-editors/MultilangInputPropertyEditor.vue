<template>
  <div class="input-property-editor">
    <el-form-item label="自动高度" v-if="props.field.type === 'text_multilang'">
      <el-checkbox v-model="autoSize"
                   placeholder=""
                   @input="onChange"/>

    </el-form-item>

    <el-form-item label="最小长度">
      <el-row>
        <el-col :span="12">
          <el-input
            v-model="zhMinLength"
            placeholder=""
            @input="onChange"
          >
            <template #prepend>中</template>
          </el-input>
        </el-col>
        <el-col :span="12">
          <el-input
            v-model="enMinLength"
            placeholder=""
            @input="onChange"
          >

            <template #prepend>英</template>
          </el-input>
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item label="最大长度">
      <el-row>
        <el-col :span="12">
          <el-input
            v-model="zhMaxLength"
            placeholder=""
            @input="onChange"
          >
            <template #prepend>中</template>
          </el-input>
        </el-col>
        <el-col :span="12">
          <el-input
            v-model="enMaxLength"
            placeholder=""
            @input="onChange"
          >

            <template #prepend>英</template>
          </el-input>
        </el-col>
      </el-row>
    </el-form-item>

  </div>

</template>

<script setup lang="ts">
import {ref, watch, onMounted} from "vue";
import {ElFormItem, ElInput} from "element-plus";

const props = defineProps({
  field: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["change"]);

// 确保 extends 对象存在
if (!props.field.extends) {
  props.field.extends = {};
}

const autoSize = ref(props.field.extends.autoSize || false);

const zhMinLength = ref(props.field.extends.minLengthZh || null);
const zhMaxLength = ref(props.field.extends.maxLengthZh || null);
const enMinLength = ref(props.field.extends.minLengthEn || null);
const enMaxLength = ref(props.field.extends.maxLengthEn || null);
// 监听属性变化
watch(autoSize, (newVal) => {
  props.field.extends.autoSize = newVal;
  // onChange();
});
watch(zhMinLength, (newVal) => {
  props.field.extends.minLengthZh = newVal;
  // onChange();
});
watch(zhMaxLength, (newVal) => {
  props.field.extends.maxLengthZh = newVal;
  // onChange();
});

watch(enMinLength, (newVal) => {
  props.field.extends.minLengthEn = newVal;
  // onChange();
});
watch(enMaxLength, (newVal) => {
  props.field.extends.maxLengthEn = newVal;
  // onChange();
});
// 初始化
onMounted(() => {
});

// 当字段属性变更时触发
function onChange() {
  emit("change", props.field);
}
</script>

<style scoped>
.input-property-editor {
  /* 日期区间字段属性编辑器样式 */
}
</style>
