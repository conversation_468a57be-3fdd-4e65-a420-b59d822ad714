<template>
  <Navbar :show-user-name="props.showUserName" :show-lang="props.showLang"/>
  <div class="horizontal-menu">
    <Menu
        mode="horizontal"
        :background-color="'#3164a0'"
        :text-color="'#ffffff'"
        :active-text-color="'#ffffff'"
        :menus="menuItems"
    />
  </div>
</template>
<script setup lang="ts">
import Navbar from "@/components/layout/Navbar.vue";
import Menu from "@/components/layout/Menu.vue";

const props = defineProps({
  showUserName: {type: Boolean, default: true},
  showLang: {type: Boolean, default: true}
})
const menuItems = [
  {
    path: '/',
    name: 'home',
    meta: {
      title: '首页', icon: 'el-icon-s-home', order: 0,
      titleKey: 'menu.index', isExternal: true
    }
  },
  {
    path: '/search',
    component: () => import('@/views/project/search.vue'),
    name: 'trialSearch',
    meta: {
      inMenu: true,
      openInNewTab: false,
      title: '项目检索',
      icon: 'el-icon-search',
      order: 4,
      titleKey: 'menu.trialSearch'
    },
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录', icon: 'el-icon-stamp', order: 1,
      titleKey: 'menu.login'
    }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/views/register/index.vue'),
    meta: {
      title: '注册', icon: 'el-icon-promotion', order: 2,
      titleKey: 'menu.register'
    }
  }
]
</script>
<style lang="scss" scoped>
.horizontal-menu {
  background-color: #3164a0;
  padding: 0;

  .el-menu {
    height: 35px;
  }

  :deep(.el-menu--horizontal) {
    border-bottom: none;
    display: flex;
    justify-content: center;


    .el-menu-item,
    .el-sub-menu__title {
      height: 35px;
      line-height: 35px;
      color: #ffffff;

      &:hover,
      &:focus {
        background-color: #2a5689;
      }

      .el-icon {
        margin-right: 5px;
        vertical-align: middle;
      }
    }

    .el-menu-item.is-active {
      background-color: #2a5689;
      border-bottom: 3px solid #ffffff;
    }
  }

  :deep(.el-sub-menu__title) {
    &:hover {
      background-color: #2a5689;
    }
  }

  :deep(.el-sub-menu.is-active) {
    .el-sub-menu__title {
      border-bottom: 3px solid #ffffff;
    }
  }
}
</style>