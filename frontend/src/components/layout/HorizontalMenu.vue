<template>
  <div class="horizontal-menu">
    <Menu
        mode="horizontal"
        :background-color="'#3164a0'"
        :text-color="'#ffffff'"
        :active-text-color="'#ffffff'"
        :menus="menuItems"
    />
  </div>
</template>

<script setup lang="ts">
import {computed} from "vue";
import Menu from "@/components/layout/Menu.vue";
import {RouteConfig} from "@/router/route-config";
import {useUserStore} from '@/stores/user';
import type {RouteRecordRaw} from 'vue-router';

// 递归过滤路由，保留name在menus中的项
function filterRoutesByNames(routes: RouteRecordRaw[], names: string[]): RouteRecordRaw[] {
  return routes
      .filter((route: RouteRecordRaw) => !route.name || names.includes(route.name as string))
      .map((route: RouteRecordRaw) => {
        const r = {...route};
        if (r.children) {
          r.children = filterRoutesByNames(r.children, names);
        }
        return r;
      });
}

const userStore = useUserStore();

const menuItems = computed(() => {
  var baseRoutes = RouteConfig.baseRoutes;
  var permissionRoutes = filterRoutesByNames(RouteConfig.permissionRoutes, userStore.menus || []);
  var publicMenuRoutes = (RouteConfig.publicRoutes || []).filter(r => r.meta && r.meta.inMenu);

  console.log(permissionRoutes);

  // 合并后排序
  return [...publicMenuRoutes, ...baseRoutes, ...permissionRoutes].sort((a, b) => {
    const orderA = typeof a.meta?.order === 'number' ? a.meta.order : Number.MAX_SAFE_INTEGER;
    const orderB = typeof b.meta?.order === 'number' ? b.meta.order : Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });
});
</script>

<style lang="scss" scoped>
.horizontal-menu {
  background-color: #3164a0;
  padding: 0;

  .el-menu {
    height: 35px;
  }

  :deep(.el-menu--horizontal) {
    border-bottom: none;
    display: flex;
    justify-content: center;


    .el-menu-item,
    .el-sub-menu__title {
      height: 35px;
      line-height: 35px;
      color: #ffffff;

      &:hover,
      &:focus {
        background-color: #2a5689;
      }

      .el-icon {
        margin-right: 5px;
        vertical-align: middle;
      }
    }

    .el-menu-item.is-active {
      background-color: #2a5689;
      border-bottom: 3px solid #ffffff;
    }
  }

  :deep(.el-sub-menu__title) {
    &:hover {
      background-color: #2a5689;
    }
  }

  :deep(.el-sub-menu.is-active) {
    .el-sub-menu__title {
      border-bottom: 3px solid #ffffff;
    }
  }
}
</style>
