<template>
  <el-menu
    :default-active="activeMenu.toString()"
    :mode="mode"
    :collapse="isCollapse"
    :background-color="backgroundColor"
    :text-color="textColor"
    :active-text-color="activeTextColor"
    :unique-opened="uniqueOpened"
    :collapse-transition="false"
    router
  >
    <template v-for="(menu, index) in menus" :key="menu.path || menu.name || `level1-${index}`">
      <template v-if="!menu.meta?.hidden && !menu.meta?.hideInMenu">
        <!-- 有可见子菜单时，始终渲染为el-sub-menu -->
        <el-sub-menu
          v-if="hasVisibleChildren(menu)"
          :index="menu.path || (typeof menu.name === 'string' ? menu.name : `submenu-${index}`)"
        >
          <template #title>
            <el-icon v-if="menu.meta?.icon">
              <component :is="getIconComponent(menu.meta?.icon as string)" />
            </el-icon>
            <span>{{ t(menu.meta?.titleKey as string || '') }}</span>
          </template>

          <template v-for="(child, childIndex) in menu.children" :key="child.path || child.name || `level2-${index}-${childIndex}`">
            <template v-if="!child.meta?.hidden && !child.meta?.hideInMenu">
              <!-- 子菜单的子菜单 -->
              <el-sub-menu
                v-if="hasVisibleChildren(child)"
                :index="child.path || (typeof child.name === 'string' ? child.name : `submenu-${index}-${childIndex}`)"
              >
                <template #title>
                  <el-icon v-if="child.meta?.icon">
                    <component :is="getIconComponent(child.meta?.icon as string)" />
                  </el-icon>
                  <span>{{ t(child.meta?.titleKey as string || '') }}</span>
                </template>

                <template v-for="(grandChild, grandChildIndex) in child.children" :key="grandChild.path || grandChild.name || `level3-${index}-${childIndex}-${grandChildIndex}`">
                  <el-menu-item
                    v-if="!grandChild.meta?.hidden && !grandChild.meta?.hideInMenu"
                    :index="grandChild.path || getRedirectPath(grandChild.redirect) || (typeof grandChild.name === 'string' ? grandChild.name : `menuitem-${index}-${childIndex}-${grandChildIndex}`)"
                  >
                    <el-icon v-if="grandChild.meta?.icon">
                      <component :is="getIconComponent(grandChild.meta?.icon as string)" />
                    </el-icon>
                    <template #title>{{ t(grandChild.meta?.titleKey as string || '')}}</template>
                  </el-menu-item>
                </template>
              </el-sub-menu>

              <!-- 子菜单的菜单项 -->
              <el-menu-item v-else :index="child.path || getRedirectPath(child.redirect) || (typeof child.name === 'string' ? child.name : `menuitem-${index}-${childIndex}`)">
                <el-icon v-if="child.meta?.icon">
                  <component :is="getIconComponent(child.meta?.icon as string)" />
                </el-icon>
                <template #title>{{ t(child.meta?.titleKey as string || '') }}</template>
              </el-menu-item>
            </template>
          </template>
        </el-sub-menu>

        <!-- 没有子菜单时，判断是否有redirect或外链 -->
        <template v-else>
          <a
            v-if="isExternalLink(menu)"
            :href="menu.meta.link"
            target="_blank"
            rel="noopener"
            class="el-menu-item external-link"
            :style="{ display: 'flex', alignItems: 'center', height: '100%' }"
          >
            <el-icon v-if="menu.meta?.icon">
              <component :is="getIconComponent(menu.meta?.icon as string)" />
            </el-icon>
            <span>{{ t(menu.meta?.titleKey as string || '') }}</span>
          </a>
          <el-menu-item
            v-else
            @click="onMenuItemClick(menu)"
          >
            <el-icon v-if="menu.meta?.icon">
              <component :is="getIconComponent(menu.meta?.icon as string)" />
            </el-icon>
            <template #title>{{ t(menu.meta?.titleKey as string || '') }}</template>
          </el-menu-item>
        </template>
      </template>
    </template>
  </el-menu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import getIconComponent from '@/utils/ui'
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const route = useRoute()
const $router = useRouter()
const props = defineProps<{
  mode?: 'horizontal' | 'vertical'
  backgroundColor?: string
  textColor?: string
  activeTextColor?: string
  isCollapse?: boolean
  uniqueOpened?: boolean
  menus: RouteRecordRaw[]
}>()


// 获取当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta?.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 检查是否有可见的子菜单
function hasVisibleChildren(item: RouteRecordRaw): boolean {
  if (!item.children || item.children.length === 0) {
    return false
  }
  
  return item.children.some(child => !child.meta?.hidden && !child.meta?.hideInMenu)
}

// 获取redirect的字符串路径
function getRedirectPath(redirect: any): string {
  if (typeof redirect === 'string') return redirect
  if (redirect && typeof redirect === 'object' && 'path' in redirect) return redirect.path
  return ''
}

function onMenuItemClick(menu) {
  const path = getRedirectPath(menu.redirect) || menu.path || (typeof menu.name === 'string' ? menu.name : '')
  if (!path) return
  if (menu.meta?.openInNewTab) {
    const url = $router.resolve(path).href
    window.open(url, '_blank')
  } else {
    $router.push(path)
  }
}

function isExternalLink(menu: any): boolean {
  return !!menu.meta?.isExternal && !!menu.meta?.link
}
</script>

<style lang="scss" scoped>
.el-menu {
  border-right: none;
}

:deep(.el-sub-menu .el-menu-item) {
  min-width: 160px;
}

:deep(.el-menu--horizontal) {
  & > .el-menu-item,
  & > .el-sub-menu .el-sub-menu__title {
    height: 35px;
    line-height: 35px;

    &.is-active {
      border-bottom: 3px solid #ffffff;
    }
  }
}

.external-link {
  color: inherit;
  text-decoration: none;
  width: 100%;
  height: 100%;
  padding: 0 20px;
}
</style>