<script setup lang="ts">
import {onMounted} from "vue";
import {useRoute, useRouter} from "vue-router";

const router = useRouter();
const route = useRoute();
import {getProjectHistoryNewestPid} from "@/api/itmctr";


onMounted(async () => {
  const businessId = route.params.businessId;
  const response = await getProjectHistoryNewestPid(String(businessId));
  const pid = response.data;

  console.log(pid);
  await router.push({
    path: `/project/view/${pid}/false`
  })
})
</script>

<style scoped lang="scss">

</style>