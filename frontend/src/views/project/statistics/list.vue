<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
        :form-items="searchFormItems"
        :initial-values="queryParams"
        @search="handleSearch"
        @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
        title="统计批次列表"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :current-page-prop="queryParams.$pageIndex"
        :page-size-prop="queryParams.$pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @sort-change="handleSortChange"
        :default-sort="{ prop: queryParams.$sortBy!, order: queryParams.$orderBy === 'asc' ? 'ascending' : 'descending' }"
    >
      <template #toolbar>
        <el-button
            type="primary"
            @click="dialogVisible=true"
        >
          <el-icon>
            <Plus/>
          </el-icon>
          新增统计批次
        </el-button>
      </template>


      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
            type="primary"
            link
            @click="handleView(row)"
        >
          <el-icon>
            <View/>
          </el-icon>
          查看
        </el-button>

      </template>
    </DataTable>

    <!-- 存储表单对话框 -->
    <el-dialog
        title="新增统计批次"
        v-model="dialogVisible"
        width="500px"
        append-to-body
        destroy-on-close
    >
      <el-form
          ref="createBatchFormRef"
          label-width="100px"
      >
        <el-form-item label="汇总类型">
          <el-select v-model="category" placeholder="请选择汇总类型" style="width: 100%">
            <el-option label="XML结果集" value="ExportXml"/>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
              v-model="dataRange"
              type="daterange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              :default-time="defaultTime"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreateBatch">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from "vue";
import {Plus, Edit, Delete} from "@element-plus/icons-vue";
import {ElMessage, FormRules} from "element-plus";
import SearchForm, {FormItem} from "@/components/common/SearchForm.vue";
import DataTable, {TableColumn} from "@/components/common/DataTable.vue";
import {getBatchPage, createBatch} from "@/api/itmctr-mgt";
import {ProjectStatisticsDto, ProjectStatisticsOperationDto, ProjectStatisticsQueryParams} from "@/dtos/itmctr";
import {formatDateTimeOffsetToLocal} from "@/utils/date";
import router from "@/router";

// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([]);
const dialogVisible = ref<boolean>(false);

// 表格列配置
const tableColumns = ref<TableColumn[]>([
  {
    prop: "category", label: "统计类型", width: 150, formatter: (row) => {
      switch (row.category) {
        case "ExportXml":
          return "XML结果集";
          break;
        default:
          return "未知";
      }
    }
  },
  {prop: "startTime", label: "开始时间", width: 150, formatter: (row) => formatDateTimeOffsetToLocal(row.startTime)},
  {prop: "endTime", label: "结束时间", width: 150, formatter: (row) => formatDateTimeOffsetToLocal(row.endTime)},
  {
    prop: "createdTime",
    label: "创建时间",
    width: 150,
    sortable: true,
    formatter: (row) => formatDateTimeOffsetToLocal(row.createdTime)
  },
  // {prop: "total", label: "总数", width: 150},
  // {prop: "reEdit", label: "再修改数量", width: 150},
  // {prop: "normal", label: "常规数量", width: 150},
]);

const loading = ref(false);
const total = ref(0);
const list = ref<ProjectStatisticsDto[]>([]);

const dataRange = ref('');
const category = ref('ExportXml');
const defaultTime = ref<[Date, Date]>([
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
])


const handleCreateBatch = async () => {
  if (!dataRange.value) {
    ElMessage.error("请选择统计时间范围");
    return;
  }
  try {
    await createBatch(category.value, dataRange.value[0], dataRange.value[1]);
    ElMessage.success("创建成功");
    dialogVisible.value = false;
    await getList();
  } catch (error) {
    ElMessage.error("创建失败");
  }
}

const queryParams = reactive<ProjectStatisticsQueryParams>({
  $pageIndex: 1,
  $pageSize: 10,
  $sortBy: "createdTime", // 初始化排序字段
  $orderBy: "desc", // 初始化排序方向
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    const response = await getBatchPage(queryParams);

    const {data} = response;
    list.value = data.rows || [];

    // 尝试多种方式转换
    let totalNumber = 0;

    if (data.totals !== undefined && data.totals !== null) {
      if (typeof data.totals === "number") {
        totalNumber = data.totals;
      } else if (typeof data.totals === "string") {
        // 尝试将字符串转换为数字
        totalNumber = parseInt(data.totals, 10) || 0;
      }
    }

    total.value = totalNumber;
  } catch (error) {
    console.error("获取列表失败", error);
    ElMessage.error("获取列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};
const handleView = async (row) => {
  await router.push({path: `/project-statistics/items/${row.key}`});
}

// 处理排序事件
const handleSortChange = async (sort: { prop: string; order: "ascending" | "descending" | null } | undefined) => {
  if (sort && sort.prop && sort.order) {
    queryParams.$sortBy = sort.prop;
    queryParams.$orderBy = sort.order === "ascending" ? "asc" : "desc";
  } else {
    queryParams.$sortBy = "";
    queryParams.$orderBy = "asc";
  }
  await getList();
};
</script>

<style lang="scss" scoped>

</style>
