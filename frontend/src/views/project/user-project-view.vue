<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="page-with-sticky-actions">
    <div class="app-container page-content">
      <el-card class="table-card">
        <el-row v-if="isReject">
          <el-col :span="4">
            <el-header>驳回原因/Reason for rejection：</el-header>
          </el-col>
          <el-col :span="20">
            <el-text style="white-space: pre-line;">{{ rejectReason }}</el-text>
          </el-col>
        </el-row>
        <template #header>
          <div class="card-header">
            <span>查看项目信息/View project</span>
          </div>
        </template>
        <FormCanvas :formSchema="formSchema" render-mode="view"/>
      </el-card>
    </div>
    <!-- 悬浮操作按钮 -->
    <div class="floating-actions">
      <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, computed, watch} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import {getProject, getProjectWithVersion} from "@/api/itmctr";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";
import {getSecondaryLink} from "@/utils/itmctr";

const router = useRouter();
const route = useRoute();

const rejectReason = ref<string>("");

const isReject = ref<boolean>(false);

// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const businessId = computed(() => {
  return route.params.businessId;
});
const version = computed(() => {
  return route.params.version ? route.params.version.replace(/-/g, '.') : null;
});


watch(
    () => formSchema.value,
    (newVal) => {
      if (newVal) {

        isReject.value = newVal.formData?.ProcessStatus == "RejectToApply";

        if (newVal.formData && newVal.formData.ApprovalHistory) {

          let approvalHistory = JSON.parse(newVal.formData.ApprovalHistory);
          //将approvalHistory按OperateTime降序排序后取第一个Action="Reject"的数据
          approvalHistory = approvalHistory
              .filter((item: any) => item.Action === "Reject")
              .sort((a: any, b: any) => new Date(b.OperateTime) - new Date(a.OperateTime));
          if (approvalHistory.length > 0) {
            rejectReason.value = approvalHistory[0].Description;
          } else {
            rejectReason.value = "";
          }
        }
      }
    },
    {immediate: true}
);

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = version.value ? await getProjectWithVersion(String(businessId.value), String(version.value)) : await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      formSchema.value.groups.forEach((group) => {
        group.fields.forEach((field) => {
          if (field.code == "partner_registry_number") {
            const text = field.value;
            const url = getSecondaryLink(businessId.value, text);
            field.extends.externalLink = {
              link: url,
              target: "_blank",
              text: text
            };
            field.value = ''
            if (formSchema.value.formData.secondaryID) {
              field.value = formSchema.value.formData.secondaryID;
            }
          }
        });
      });


      loading.value = false;
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    // ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};
const handleBack = () => {
  router.go(-1);
};

// 组件挂载时加载表单
onMounted(loadProject);
</script>

<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

// 浮动操作按钮样式已移至全局样式
</style>
