<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="page-with-sticky-actions">
    <div class="app-container flex-layout page-content">

      <div class="form-area">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>项目维护</span>
            </div>
          </template>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="项目信息" name="first">
              <FormCanvas
                  ref="formCanvasRef"
                  :formSchema="formSchema"
                  render-mode="edit"/>
            </el-tab-pane>
            <el-tab-pane label="审批历史" name="second">
              <approval-log :logs="approvalHistory"/>
            </el-tab-pane>
            <el-tab-pane label="表单数据" name="third">
              <el-descriptions :column="4" label-width="180">
                <el-descriptions-item
                    :span="1"
                    :label="key"
                    v-for="key in sortedFormDataKeys"
                    :key="key"
                >
                  <el-input
                      type="textarea"
                      :row="5"
                      v-model="formSchema.formData[key]"
                      :disabled="key === 'PublicTitleEN' || key === 'PublicTitle' || key === 'RegistrationNumber'"
                  />
                </el-descriptions-item>
              </el-descriptions>
            </el-tab-pane>
          </el-tabs>

        </el-card>

      </div>
    </div>
  </div>
  <!-- 悬浮操作按钮 -->
  <div class="floating-actions">
    <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
    <el-button @click="handleSave" :loading="loading">保存/Save</el-button>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  computed,
  watch,
} from "vue";
import {useRoute, useRouter} from "vue-router";
import {ElLoading} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import {
  getProjectWithVersion,

} from "@/api/itmctr-mgt";

import {
  saveProject,
} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";
import ApprovalLog from "@/views/project/components/ApprovalLog.vue";
import type {ApprovalHistoryDto} from "@/dtos/itmctr";

const route = useRoute();
const router = useRouter();

const activeTab = ref("first");

// 表单数据
const formSchema = ref<FormDefinitionDto>({language: 'both', groups: []});

const approvalHistory = ref<ApprovalHistoryDto[]>([]);

const sortedFormDataKeys = computed(() => {
  if (!formSchema.value.formData) return [];
  return Object.keys(formSchema.value.formData).sort();
});

watch(
    () => formSchema.value,
    (newVal) => {
      if (newVal) {

        if (newVal.formData && newVal.formData.ApprovalHistory) {
          approvalHistory.value = JSON.parse(newVal.formData.ApprovalHistory);

        }
      }
    },
    {immediate: true}
);

const loading = ref<boolean>(false);

const businessId = computed(() => {
  return route.params.businessId;
});

const version = computed(() => {
  return route.params.version.replace(/-/g, '.');
});

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProjectWithVersion(String(businessId.value), String(version.value))

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      loading.value = false;

    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    // ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};

// 组件挂载时加载表单
onMounted(loadProject);

const handleBack = async () => {
  router.go(-1);
}
// 保存表单
const handleSave = async () => {
  ElMessageBox.confirm(
      "是否保存当前表单？",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  ).then(async () => {
    let loadingInstance = ElLoading.service({
      lock: true,
      text: "正在保存.../Saving...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    await save();

    if (loadingInstance) loadingInstance.close();
    router.go(-1);
  }).catch(() => {
  });
};


async function save() {
  try {
    loading.value = true;

    const schemaData = JSON.parse(JSON.stringify(formSchema.value));

    await saveProject(String(businessId.value), String(version.value), schemaData);

    loading.value = false;
    // return response.data;
  } catch (error: any) {
    // ElMessage.error(`保存失败: ${error.message || error}`);
    loading.value = false;
    // return null;
  }
}


const formCanvasRef = ref();
</script>

<style lang="scss" scoped>
html,
body,
#app,
.app-wrapper,
.main-content,
.app-container.flex-layout {
  height: 100%;
  min-height: 0;
}

.app-container.flex-layout {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  height: 100%;
  min-height: 0;
}

.form-area {
  flex: 1 1 0;
  min-width: 0;
  margin-right: 24px;
}

.annotation-panel {
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  // border: 1.5px solid #e3e8f0;
  border-radius: 14px;
  // box-shadow: 0 6px 24px rgba(64,158,255,0.06);
  // background: #fcfcfe;
  padding: 0 0 18px 0;
}

.annotation-title {
  font-weight: bold;
  font-size: 18px;
  color: #409eff;
  margin-bottom: 16px;
  padding-left: 10px;
  background: #f4f8ff;
  padding: 12px 0 10px 4px;
  border-radius: 10px 10px 0 0;
  border-bottom: 1px solid #e3e8f0;
}

.annotation-item {
  border: 1.2px solid #e3e8f0;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.04);
  padding: 14px 14px 10px 14px;
  margin-bottom: 18px;
  transition: box-shadow 0.2s, border-color 0.2s;
}

.annotation-item:hover {
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);
  border-color: #b3d8fd;
}

.annotation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.annotation-label {
  font-size: 15px;
  color: #666;
  font-weight: 500;
}

.annotation-input {
  width: 100%;
  font-size: 15px;
  min-height: 38px;
  background: #f7faff;
}

.fill-btn {
  margin-left: 8px;
  align-self: unset;
  margin-top: 0;
}


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

// 浮动操作按钮样式已移至布局插槽

.side-affix {
  position: fixed;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 200;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.affix-btn-group {
  //display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

#smartFill {
  text-align: center;
}

#aiTranslate {
  text-align: center;
  margin-top: 15px;
}

.affix-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  font-size: 22px;
  //display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.affix-btn-label {
  margin-top: 2px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: rgba(0, 0, 0, 0.18);
  border-radius: 10px;
  padding: 1px 8px;
  text-align: center;
  font-weight: 400;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  letter-spacing: 0;
  line-height: 1.2;
}

.smart-fill-btn {
  background: #13ce66;
  color: #fff;
  box-shadow: 0 0 12px 2px #13ce6688, 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

.smart-fill-btn:hover {
  box-shadow: 0 0 24px 6px #13ce66cc, 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ai-translate-btn {
  background: #409eff;
  color: #fff;
  box-shadow: 0 0 12px 2px #409eff88, 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

.ai-translate-btn:hover {
  box-shadow: 0 0 24px 6px #409effcc, 0 2px 8px rgba(0, 0, 0, 0.15);
}

.smart-fill-dialog-content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  padding: 12px 18px 0 18px;
  text-align: left;
}

.smart-fill-dialog-content p {
  margin-bottom: 10px;
}

.ai-translate-dialog-content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  padding: 12px 18px 0 18px;
  text-align: left;
}

.ai-translate-dialog-content p {
  margin-bottom: 10px;
}

.translate-tip-dialog-content {
  padding: 8px 18px 0 18px;
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.tip-icon {
  font-size: 32px;
  margin-right: 8px;
}

.tip-title {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.tip-body {
  font-size: 15px;
  color: #333;
  line-height: 1.9;
  text-align: left;
}

.tip-body p {
  margin: 0;
}

.extract-progress-content {
  padding: 24px 32px 18px 32px;
  text-align: center;
}

.extract-progress-icon {
  font-size: 48px;
  margin-bottom: 18px;
  color: #409eff;
}

.extract-progress-title {
  font-size: 20px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 12px;
  margin-top: 0;
  letter-spacing: 0.5px;
}

.extract-progress-time {
  font-size: 15px;
  color: #666;
  margin-bottom: 24px;
}

.el-progress {
  width: 90%;
  margin: 0 auto 8px auto;
  display: block;
  border-radius: 8px;
}

.extract-progress-percent {
  font-size: 15px;
  color: #409eff;
  margin-bottom: 8px;
}

.extract-progress-hint {
  font-size: 15px;
  color: #d77680;
  margin-top: 24px;
}

.extract-progress-msg {
  font-size: 15px;
  color: #333;
  margin-top: 8px;
}

.el-dialog {
  border-radius: 14px !important;
}

/* LeaderLine 全局样式修正，防止双滚动条 */
:global(.leader-line) {
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  pointer-events: none;
  z-index: 9999;
  overflow: visible !important;
}

.annotation-canvas {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 20;
}

:deep(.form-validate-error-box) .el-message-box {
  width: 600px !important;
  max-width: 90vw;
  min-width: 600px;
}

// 页面容器样式已移至布局层面
</style>
