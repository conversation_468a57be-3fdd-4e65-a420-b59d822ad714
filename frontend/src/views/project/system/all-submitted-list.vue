<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
        :form-items="searchFormItems"
        :initial-values="queryParams"
        @search="handleSearch"
        @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
        :title="t('menu.' + String(route.name), {}, {locale: 'zh-CN'}) + '/' + t('menu.' + String(route.name), {}, {locale: 'en-US'})"
        :data="formInstanceList"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :action-width="150"
        :current-page-prop="queryParams.$pageIndex"
        :page-size-prop="queryParams.$pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @sort-change="handleSortChange"
        :default-sort="{ prop: queryParams.$sortBy!, order: queryParams.$orderBy === 'asc' ? 'ascending' : 'descending' }"
    >
      <template #toolbar></template>

      <template #public_title="{row}">
        <div>{{ row.value?.public_title?.zh }}</div>
        <div>{{ row.value?.public_title?.en }}</div>
      </template>
      <!-- 状态列 -->
      <!--
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          <el-text v-if="getFormDataValue(row.formData, 'TraditionalProject')=='False'">非传统医学</el-text>
          <el-text v-else>{{ $t(getStatusText(row.status)) }}</el-text>
        </el-tag>
      </template>
      -->

      <!-- 操作列 -->
      <template #action="{ row }">

        <el-button type="primary" link @click="handleView(row)">
          <el-icon>
            <View/>
          </el-icon>
          查看
        </el-button>

        <el-button v-if="canRecall(row.formData)" type="primary" link @click="handleRecall(row)">
          <el-icon>
            <RefreshLeft/>
          </el-icon>
          撤回
        </el-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from "vue";
import SearchForm, {FormItem} from "@/components/common/SearchForm.vue";
import DataTable, {TableColumn} from "@/components/common/DataTable.vue";
import {View} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import {getPage} from "@/api/itmctr-mgt";

const route = useRoute();

import {
  FormInstanceDto,
  FormInstanceJsonDto,
  FormDefinitionDto,
} from "@/dtos/dynamic-form.dto";
import {
  getDisplayByDefinition,
  getStatusTagType,
  getStatusText,
  getFormDataValue, getRowFullStatusText,
} from "@/utils/dynamic-form";
import router from "@/router";
import {formatTimestampToLocalString} from "@/utils/date";
import {useI18n} from "vue-i18n";

const {t} = useI18n();

// props
const props = defineProps<{
  title: string;
  columns: Array<{
    prop: string;
    label: string;
    width?: number;
    placeholder?: string;
    search?: boolean;
  }>;
}>();

const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    multi: true,
    tagMode: true,
    label: "注册题目/Public title",
    prop: "public_title",
    placeholder: "",
  },
  {
    type: "input",
    multi: true,
    label: "注册号/Registration number",
    prop: "registration_number",
    placeholder: "",
  },
  {
    type: "input",
    multi: true,
    label: "申请注册联系人/Applicant",
    prop: "applicant",
    placeholder: "",
  },

  {
    type: "input",
    multi: true,
    label: "研究负责人/Study leader",
    prop: "study_leader",
    placeholder: "",
  },
  {
    type: "input",
    multi: true,
    label: "研究实施负责（组长）单位/Primary sponsor",
    prop: "primary_sponsor",
    placeholder: "",
  },
]);

const tableColumns = ref<TableColumn[]>([
      {
        prop: "registration_number",
        label: {zh: "注册号", en: "Registration number"},
        width: 150,
        fixed: true,
        formatter: (row: any) =>
            getDisplayByDefinition(formDefinition.value, row, "registration_number"),
      },
      {
        prop: "public_title",
        label: {zh: "注册题目", en: "Public title"},
        slot: 'public_title',
        width: 420,
        fixed: true,
        // formatter: (row: any) => {
        //   return getDisplayByDefinition(formDefinition.value, row, "public_title")
        // }
      }
      ,
      {
        prop: "status",
        label: {zh: "审核状态", en: "Review Status"},
        width: 150,
        formatter(row: any) {
          return getRowFullStatusText(row);
        },
      },
      {
        prop: "firstSubmitTime",
        label:
            {
              zh: "首次提交时间", en:
                  "First Submission"
            }
        ,
        sortable: true,
        width: 150,
        formatter(row
                  :
                  any
        ) {
          return formatTimestampToLocalString(getFormDataValue(row.formData, "FirstSubmitTime"));
        }
        ,
      }
      ,
      {
        prop: "firstApprovalUserName",
        label:
            {
              zh: "总审核员", en:
                  "Send User"
            }
        ,
        width: 100,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "FirstApprovalUserName");
        }
        ,
      }
      ,
      {
        prop: "secondApprovalUserName",
        label:
            {
              zh: "高级审核员", en:
                  "Execute User"
            }
        ,
        width: 100,
        sortable: true,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "SecondApprovalUserName");
        }
        ,
      }
      ,
      {
        prop: "ThirdApprovalUserName",
        label:
            {
              zh: "中级审核员", en:
                  "Third Execute User"
            }
        ,
        width: 100,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "ThirdApprovalUserName");
        }
        ,
      }
      ,
      {
        prop: "FourthApprovalUserName",
        label:
            {
              zh: "初级审核员", en:
                  "Fourth Execute User"
            }
        ,
        width: 100,
        formatter(row
                  :
                  any
        ) {
          return getFormDataValue(row.formData, "FourthApprovalUserName");
        }
        ,
      }
      ,
    ])
;

// 查询参数
const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  $sortBy: "firstSubmitTime", // 初始化排序字段
  $orderBy: "desc", // 初始化排序方向
  dynamicQueries: {},
});

const loading = ref(false);
const total = ref(0);

const formInstanceList = ref<FormInstanceJsonDto[]>([]);
const formDefinition = ref<FormDefinitionDto>({
  language: "",
  groups: [],
});

onMounted(async () => {
  await getList();
});

const getList = async () => {
  try {
    loading.value = true;

    queryParams.columns = searchFormItems.value.map(q => q.prop).concat(tableColumns.value.map(q => q.prop)).concat(["ProcessStatus", "EditProcessStatus", "ProjectTag", "ProjectTerminationCause", "ApprovalHistory"]);
    const response = await getPage(route.name, queryParams);
    const {data} = response;
    formInstanceList.value = data.rows || [];
    formDefinition.value = data.formDefinition;
    total.value = data.totals;
  } catch (error) {
    ElMessage.error("获取表单实例列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = async (formData: Record<string, any>) => {
  const dynamicQueries: Record<string, any> = {};
  for (const key in formData) {
    if (formData[key] != "" && key != "status") {
      dynamicQueries[key] = formData[key];
    }
  }
  Object.assign(queryParams, {
    ...formData,
    // status: formData.status,
    dynamicQueries: dynamicQueries,
  });
  queryParams.$pageIndex = 1;
  await getList();
};

const handleReset = async (formData: any) => {
  Object.assign(queryParams, formData);
  await getList();
};

const handleSizeChange = async (val: number) => {
  queryParams.$pageSize = val;
  await getList();
};


// 处理排序事件
const handleSortChange = async (sort: { prop: string; order: "ascending" | "descending" | null } | undefined) => {
  if (sort && sort.prop && sort.order) {
    queryParams.$sortBy = sort.prop;
    queryParams.$orderBy = sort.order === "ascending" ? "asc" : "desc";
  } else {
    queryParams.$sortBy = "";
    queryParams.$orderBy = "asc";
  }
  await getList();
};
const handleCurrentChange = async (val: number) => {
  queryParams.$pageIndex = val;
  await getList();
};

const handleView = (row: FormInstanceDto) => {
  // router.push({path: `/project/user/project-view/${row.businessId}`});
  const routeData = router.resolve({
    path: `/project/user/project-view/${row.businessId}/${row.version.replace(/\./g, '-')}`
  });
  window.open(routeData.href, '_blank');
};

const handleEdit = (row: FormInstanceDto) => {
  router.push({path: `/project/user/project-add/${row.businessId}`});
};

const canRecall = (formData: Record<string, string>) => {
  let processStatus = getFormDataValue(formData, "ProcessStatus");
  let projectTag = getFormDataValue(formData, "ProjectTag");
  let projectTerminationCause = getFormDataValue(formData, "ProjectTerminationCause");
  let editProcessStatus = getFormDataValue(formData, "EditProcessStatus");
  console.log("editProcessStatus", editProcessStatus);
  return editProcessStatus === '' &&
      (processStatus === 'PendingSecondAssignment' ||
          processStatus === 'PendingThirdAssignment' ||
          processStatus === 'PendingFourthApproval' ||
          (projectTag === 'Termination' && projectTerminationCause === 'NonTraditionalProject'));
}

const handleRecall = (row: FormInstanceDto) => {
  router.push({path: `/project/system/project-recall/${row.businessId}`});
}
</script>

<style lang="scss" scoped>

</style>
