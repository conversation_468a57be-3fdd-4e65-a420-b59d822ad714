<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分配项目信息/Assign project</span>
        </div>
      </template>
      <ApprovalLog :logs="approvalLogs"/>
      <FormCanvas :formSchema="formSchema" render-mode="view" />
      <el-form
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        ref="formRef"
        :model="assignProjectDto"
        :rules="currentRules"
        label-width="400px"
      >
        <el-form-item label="当前中级审核员" prop="currentThirdApprovalUser" v-if="currentThirdApprovalUserId">
          <span>{{ currentThirdApprovalUserName }} ({{ currentThirdApprovalUserAccount }})</span>
        </el-form-item>
        <el-form-item label="当前初级审核员" prop="currentFourthApprovalUser" v-if="currentFourthApprovalUserId">
          <span>{{ currentFourthApprovalUserName }} ({{ currentFourthApprovalUserAccount }})</span>
        </el-form-item>
        <el-form-item
          label="请指派中级/初级审核员"
          prop="userIdAndPosition"
        >
          <el-select
            v-model="assignProjectDto.userIdAndPosition"
            filterable
            clearable
            placeholder="请选择"
            style="width:500px !important;"
          >
            <el-option
              v-for="user in auditors"
              :key="user.key+'-'+user.positionCode"
              :label="user.realName + '[' + user.username + ']'+'['+user.organizationNamePath+']'+'['+user.positionName+']'"
              :value="user.key+'+'+user.positionCode+'+'+user.organizationCode"
            >
              <template #default>
                <div>
                  <div>{{ user.realName }} [{{ user.username }}]{{ user.organizationNamePath }} [{{
                      user.positionName
                    }}]
                  </div>
                  <div class="summary">
                    <template v-if="user.positionCode === 'CHECKER_LEVEL_3'">
                      待分配:{{ getUserStatistics(user.key, user.positionCode, 'Level3PendingAssignReview') }} 
                      待审核:{{ getUserStatistics(user.key, user.positionCode, 'Level3PendingReview') }}
                    </template>
                    <template v-else-if="user.positionCode === 'CHECKER_LEVEL_4'">
                      待审核:{{ getUserStatistics(user.key, user.positionCode, 'Level4PendingReview') }}
                    </template>
                  </div>
                </div>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="description">
          <el-input
            type="textarea"
            ref="descriptionRef"
            v-model="assignProjectDto.description"
            :rows="8"
            clearable
            placeholder="请填写审核意见"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
  <!-- 悬浮操作按钮 -->
  <div class="floating-actions">
    <el-button @click="handleSave" type="primary" :loading="loading"
    >保存/Save
    </el-button
    >
    <el-button @click="handleReturn" type="danger" :loading="loading"
    >退回/Return
    </el-button
    >
    <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, nextTick, computed} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, FormInstance, FormRules} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import {getProject} from "@/api/itmctr";
import {assignProject,returnProjectLevel2, getSummarizedData} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";


import {getManagedPositionUsers} from "@/api/rbac-mgt";
import {AssignProjectDto} from "@/dtos/itmctr";
import ApprovalLog from "@/views/project/components/ApprovalLog.vue";

const router = useRouter();
const route = useRoute();

// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const auditors = ref<any[]>([]);

// 用户统计数据状态管理 - 使用 userId-positionCode 作为唯一标识
const userStatistics = ref<Map<string, Map<string, number | string>>>(new Map());

const currentThirdApprovalUserName = ref<string>("");
const currentThirdApprovalUserAccount = ref<string>("");
const currentThirdApprovalUserId = ref<string>("");

const currentFourthApprovalUserName = ref<string>("");
const currentFourthApprovalUserAccount = ref<string>("");
const currentFourthApprovalUserId = ref<string>("");


const assignProjectDto = ref<AssignProjectDto>({
  userId: undefined,
  userIdAndPosition: undefined,
  positionCode: undefined,
  description: undefined,
});

const formRef = ref<FormInstance>();


// 保存按钮的验证规则
const saveRules: FormRules = {
  userIdAndPosition: [
    { required: true, message: "请选择中级/初级审核员", trigger: "blur" },
  ],
};

// 退回按钮的验证规则
const returnRules: FormRules = {
  description: [
    { required: true, message: "请填写退回原因", trigger: "blur" },
  ],
};

const currentRules = ref<FormRules>(saveRules);



const businessId = computed(() => {
  return route.params.businessId;
});

// 获取用户统计数据的显示值
const getUserStatistics = (userId: string, positionCode: string, statisticsCode: string): string | number => {
  const key = `${userId}-${positionCode}`;
  const userStats = userStatistics.value.get(key);
  if (!userStats) {
    return '-';
  }
  const value = userStats.get(statisticsCode);
  return value !== undefined ? value : '-';
};

// 异步加载单个用户的统计数据
const loadUserStatistics = async (userId: string, positionCode: string) => {
  const key = `${userId}-${positionCode}`;
  
  // 初始化用户统计数据
  if (!userStatistics.value.has(key)) {
    userStatistics.value.set(key, new Map());
  }
  
  const userStats = userStatistics.value.get(key)!;
  
  // 根据岗位类型确定需要加载的统计数据
  let statisticsCodes: string[] = [];
  if (positionCode === 'CHECKER_LEVEL_3') {
    statisticsCodes = ['Level3PendingAssignReview', 'Level3PendingReview'];
  } else if (positionCode === 'CHECKER_LEVEL_4') {
    statisticsCodes = ['Level4PendingReview'];
  }
  
  // 设置初始加载状态
  statisticsCodes.forEach(code => {
    userStats.set(code, '...');
  });
  
  // 使用Promise.allSettled替代forEach，确保异步操作正确执行
  const promises = statisticsCodes.map(async (statisticsCode) => {
    try {
      const data = await getUserSummarizedData(userId, positionCode, statisticsCode);
      userStats.set(statisticsCode, data || 0);
    } catch (error) {
      console.error(`获取用户 ${userId} 岗位 ${positionCode} 的 ${statisticsCode} 统计数据失败:`, error);
      userStats.set(statisticsCode, 0);
    }
  });
  
  // 等待所有统计数据加载完成
  await Promise.allSettled(promises);
};

// 异步加载所有用户的统计数据 - 控制并发数量避免浏览器限制
const loadAllUserStatistics = async () => {
  if (auditors.value.length === 0) return;
  
  // 分批处理，每批最多5个并发请求，避免超出浏览器并发限制
  const batchSize = 5;
  for (let i = 0; i < auditors.value.length; i += batchSize) {
    const batch = auditors.value.slice(i, i + batchSize);
    const promises = batch.map(user => 
      loadUserStatistics(user.key, user.positionCode)
    );
    // 等待当前批次完成后再处理下一批次
    await Promise.allSettled(promises);
  }
};

// 获取用户统计数据的方法
const getUserSummarizedData = async (userId: string, positionCode: string, statisticsCode: string) => {
  try {
    const response = await getSummarizedData(userId, positionCode, statisticsCode);
    return response.data;
  } catch (error) {
    console.error("获取项目摘要失败", error);
    ElMessage.error("获取项目摘要失败");
  }
};

const approvalLogs = computed(() => {
  try {
    return JSON.parse(formSchema.value?.formData?.ApprovalHistory || "[]");
  } catch {
    return [];
  }
});

const loadAuditors = async () => {

  let loadingInstance: any;
  try {

    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取三级/四级审核员 - 改为并行调用
    const [thirdAuditors, fourthAuditors] = await Promise.all([
      getManagedPositionUsers("CHECKER_LEVEL_3"),
      getManagedPositionUsers("CHECKER_LEVEL_4")
    ]);

    // 合并三级和四级审核员
    auditors.value = [
      ...(thirdAuditors.data.flatMap(q => q.users.map(u => {
        u.organizationCode = q.organization.code;
        //如果namePath开头有"/"则去掉开头的"/"
        u.organizationNamePath = q.organization.namePath.startsWith("/") ? q.organization.namePath.substring(1) : q.organization.namePath;
        u.positionName = "中级审核员";
        u.positionCode = "CHECKER_LEVEL_3";
        return u;
      })) || []),
      ...(fourthAuditors.data.flatMap(q => q.users.map(u => {
        u.organizationCode = q.organization.code;
        u.organizationNamePath = q.organization.namePath.startsWith("/") ? q.organization.namePath.substring(1) : q.organization.namePath;
        u.positionName = "初级审核员";
        u.positionCode = "CHECKER_LEVEL_4";
        return u;
      })) || []),
    ];

    loading.value = false;
    if (loadingInstance) loadingInstance.close();
    
    // 异步加载用户统计数据，不阻塞主流程
    await loadAllUserStatistics();
  } catch (error: any) {
    console.error("获取中级/初级审核员失败:", error);
    ElMessage.error(`获取中级/初级审核员失败: ${error.message || error}`);
    loading.value = false;
    if (loadingInstance) loadingInstance.close();
  }
};

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      currentThirdApprovalUserName.value = formSchema.value.formData["ThirdApprovalUserName"];
      currentThirdApprovalUserAccount.value = formSchema.value.formData["ThirdApprovalUserAccount"];
      currentThirdApprovalUserId.value = formSchema.value.formData["ThirdApprovalUserId"];

      currentFourthApprovalUserName.value = formSchema.value.formData["FourthApprovalUserName"];
      currentFourthApprovalUserAccount.value = formSchema.value.formData["FourthApprovalUserAccount"];
      currentFourthApprovalUserId.value = formSchema.value.formData["FourthApprovalUserId"];

      loading.value = false;
      if (loadingInstance) loadingInstance.close();
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
    loading.value = false;
    if (loadingInstance) loadingInstance.close();
  }
};
const handleBack = () => {
  router.go(-1);
};

const handleReturn = async () => {
  loading.value = true;
  if (!formRef.value) {
    return;
  }

  currentRules.value = returnRules;
  await nextTick();
  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  try {
    // const split = assignProjectDto.value.userIdAndPosition.split("-");
    // assignProjectDto.value.organizationCode = split[2];
    // assignProjectDto.value.positionCode = split[1];
    // assignProjectDto.value.userId = split[0];
    //
    await returnProjectLevel2(String(businessId.value), assignProjectDto.value);
    router.go(-1);
  } catch (error) {
    ElMessage.error("退回失败");
  } finally {
    loading.value = false;
  }
};
const handleSave = async () => {
  loading.value = true;
  if (!formRef.value) {
    return;
  }

  currentRules.value = saveRules;
  await nextTick();
  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  try {
    const split = assignProjectDto.value.userIdAndPosition.split("+");
    assignProjectDto.value.organizationCode = split[2];
    assignProjectDto.value.positionCode = split[1];
    assignProjectDto.value.userId = split[0];
    await assignProject(String(businessId.value), assignProjectDto.value);
    router.go(-1);
  } catch (error) {
    console.error("分配项目失败", error);
    ElMessage.error("分配项目失败");
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载表单
onMounted(async () => {
  // 并行加载用户列表和项目数据，不相互阻塞
  await Promise.all([loadAuditors(), loadProject()]);
});
</script>

<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}
.el-select-dropdown__item {
  height: 100% !important;
}
.summary {
  line-height: 18px;
  font-size: 12px;
  color: #606266;
}
</style>
