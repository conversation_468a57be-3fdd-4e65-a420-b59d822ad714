<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
        :form-items="searchFormItems"
        :initial-values="queryParams"
        @search="handleSearch"
        @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
        :title="t('menu.' + String(route.name), {}, {locale: 'zh-CN'}) + '/' + t('menu.' + String(route.name), {}, {locale: 'en-US'})"
        :data="formInstanceList"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :current-page-prop="queryParams.$pageIndex"
        :page-size-prop="queryParams.$pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
    >
      <template #toolbar></template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ $t(getStatusText(row.status)) }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleReview(row)">
          <el-icon>
            <Checked/>
          </el-icon>
          审核
        </el-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from "vue";
import SearchForm, {FormItem} from "@/components/common/SearchForm.vue";
import DataTable, {TableColumn} from "@/components/common/DataTable.vue";
import {FormDataQueryOperator, FormInstanceStatus} from "@/enums";
import {Checked} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import {getPage} from "@/api/itmctr-mgt";
import {useUserStore} from "@/stores/user";
import {
  FormInstanceDto,
  FormInstanceJsonDto,
  FormDefinitionDto,
} from "@/dtos/dynamic-form.dto";
import {
  getDisplayByDefinition,
  getStatusTagType,
  getStatusText,
  getFormDataValue,
} from "@/utils/dynamic-form";
import router from "@/router";
import {formatTimestampToLocalString} from "@/utils/date";
import {useI18n} from "vue-i18n";

const {t} = useI18n();
const route = useRoute();

// props
const props = defineProps<{
  formCode: string;
  title: string;
  columns: Array<{
    prop: string;
    label: string;
    width?: number;
    placeholder?: string;
    search?: boolean;
  }>;
}>();
const userStore = useUserStore();

const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    multi: true,
    tagMode: true,
    label: "注册题目/Public title",
    prop: "public_title",
    placeholder: "",
  },
  {
    type: "input",
    multi: true,
    label: "注册号/Registration number",
    prop: "registration_number",
    placeholder: "",
  },
  {
    type: "input",
    multi: true,
    label: "申请注册联系人/Applicant",
    prop: "applicant",
    placeholder: "",
  },
]);

const tableColumns = ref<TableColumn[]>([
  {
    prop: "registration_number",
    label: {zh: "注册号", en: "Registration number"},
    width: 120,
    formatter: (row: any) =>
        getDisplayByDefinition(formDefinition.value, row, "registration_number"),
  },
  {
    prop: "public_title",
    label: {zh: "注册题目", en: "Public title"},
    width: 120,
    formatter: (row: any) =>
        getDisplayByDefinition(formDefinition.value, row, "public_title"),
  },
  {
    prop: "firstSubmitTime",
    label: {zh: "首次提交时间", en: "First Submission"},
    width: 100,
    formatter(row: any) {
      // return getFormDataValue(row.formData, "FirstSubmitTime");
      return formatTimestampToLocalString(getFormDataValue(row.formData, "FirstSubmitTime"));
    },
  },
  {
    prop: "ThirdApprovalUserName",
    label: {zh: "审核人", en: "Reviewer"},
    width: 100,
    formatter(row: any) {
      return getFormDataValue(row.formData, "ThirdApprovalUserName");
    },
  },
]);

// 查询参数
const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  dynamicQueries: {},
});

const loading = ref(false);
const total = ref(0);

const formInstanceList = ref<FormInstanceJsonDto[]>([]);
const formDefinition = ref<FormDefinitionDto>({
  language: "",
  groups: [],
});

onMounted(() => {
  getList();
});

const getList = async () => {
  try {
    loading.value = true;
    queryParams.columns = searchFormItems.value.map(q => q.prop).concat(tableColumns.value.map(q => q.prop)).concat(["ProcessStatus","EditProcessStatus","ProjectTag","ApprovalHistory"]);
const response = await getPage(route.name, queryParams);
    const {data} = response;
    formInstanceList.value = data.rows || [];
    formDefinition.value = data.formDefinition;
    total.value = data.totals;
  } catch (error) {
    ElMessage.error("获取表单实例列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: Record<string, any>) => {
  const dynamicQueries: Record<string, any> = {};
  for (const key in formData) {
    if (formData[key] != "" && key != "status") {
      dynamicQueries[key] = formData[key];
    }
  }
  Object.assign(queryParams, {
    ...formData,
    // status: formData.status,
    dynamicQueries: dynamicQueries,
  });
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const handleReview = (row: FormInstanceDto) => {
  router.push({path: `/project/system/project-send-number/${row.businessId}`});
};
</script>

<style lang="scss" scoped>

</style>
