<template>

  <el-alert v-if="!initialized" type="warning" show-icon :closable="false">正在加载相似项目...</el-alert>

  <el-card class="similar-projects" v-if="initialized">
    <template #header>
      <h3 class="similar-projects-header">相似项目</h3>
    </template>
    <el-alert v-if="cnProjects.length==0&&enProjects.length==0" type="info" show-icon :closable="false">
      没有找到相似项目
    </el-alert>
    <el-tabs :model-value="currentTab">
      <el-tab-pane label="中文相似" name="cn" v-if="cnProjects.length>0">
        <el-table :data="cnProjects">
          <el-table-column
              prop="registrationNumber"
              label="注册编号" width="200"
              :formatter="(row)=>row.value.registration_number"/>
          <el-table-column
              prop="title"
              label="注册标题">
            <template #default="{ row }">


              <el-link
                  target="_blank"
                  type="primary"
                  class="is-underline"
                  :href="$router.resolve({
                    path: `/project/user/project-view/${row.businessId}/${row.version.replace(/\./g, '-')}`
                  }).href">
                <div>{{ row.value.public_title.zh }}</div>
              </el-link>
              <el-link
                  target="_blank"
                  type="primary"
                  class="is-underline"
                  :href="$router.resolve({
                    path: `/project/user/project-view/${row.businessId}/${row.version.replace(/\./g, '-')}`
                  }).href">
                <div>{{ row.value.public_title.en }}</div>
              </el-link>

            </template>
          </el-table-column>
          <el-table-column
              prop="study_leader"
              label="申请注册联系人" width="180"
              :formatter="(row)=>row.value.study_leader.zh+'/'+row.value.applicant.en"/>
          <el-table-column
              prop="study_leader_telephone"
              label="申请注册联系人电话" width="220"
              :formatter="(row)=>row.value.study_leader_telephone"/>
          <el-table-column
              prop="study_leader_affiliation"
              label="申请注册所在单位" width="220"
              :formatter="(row)=>row.value.study_leader_affiliation.zh+'/'+row.value.study_leader_affiliation.en"/>
          <el-table-column
              prop="status"
              label="状态" width="120"
              :formatter="getStatusText"/>
          <el-table-column
              prop="version"
              label="版本号" width="120"
              :formatter="(row)=>row.version"/>
        </el-table>
        <el-alert v-if="cnProjectLength>cnProjects.length" type="info" show-icon :closable="false">
          还有{{ cnProjectLength - cnProjects.length }}个相似项目未显示。
        </el-alert>
      </el-tab-pane>
      <el-tab-pane label="英文相似" name="en" v-if="enProjects.length>0">
        <el-table :data="enProjects">
          <el-table-column
              prop="registrationNumber"
              label="注册编号" width="200"
              :formatter="(row)=>row.value.registration_number"/>
          <el-table-column
              prop="title"
              label="注册标题">
            <template #default="{ row }">


              <el-link
                  target="_blank"
                  type="primary"
                  class="is-underline"
                  :href="$router.resolve({
                    path: `/project/user/project-view/${row.businessId}/${row.version.replace(/\./g, '-')}`
                  }).href">
                <div>{{ row.value.public_title.zh }}</div>
              </el-link>
              <el-link
                  target="_blank"
                  type="primary"
                  class="is-underline"
                  :href="$router.resolve({
                    path: `/project/user/project-view/${row.businessId}/${row.version.replace(/\./g, '-')}`
                  }).href">
                <div>{{ row.value.public_title.en }}</div>
              </el-link>

            </template>
          </el-table-column>

          <el-table-column
              prop="study_leader"
              label="申请注册联系人" width="180"
              :formatter="(row)=>getMultiLanguageDisplay(row.value,'study_leader')"/>
          <el-table-column
              prop="study_leader_telephone"
              label="申请注册联系人电话" width="220"
              :formatter="(row)=>row.value.study_leader_telephone"/>
          <el-table-column
              prop="study_leader_affiliation"
              label="申请注册所在单位" width="220"
              :formatter="(row)=>getMultiLanguageDisplay(row.value,'study_leader_affiliation')"/>
          <el-table-column
              prop="status"
              label="状态" width="120"
              :formatter="getStatusText"/>
          <el-table-column
              prop="version"
              label="版本号" width="120"
              :formatter="(row)=>row.version"/>
        </el-table>
        <el-alert v-if="enProjectLength>enProjects.length" type="info" show-icon :closable="false">
          还有{{ enProjectLength - enProjects.length }}个相似项目未显示。
        </el-alert>
      </el-tab-pane>
    </el-tabs>

  </el-card>
</template>
<script setup lang="ts">
// props
import {
  getDisplayByDefinition,
  getFormDataValue, getMultiLanguageDisplay, getRowFullStatusText
} from "@/utils/dynamic-form";
import {ref} from "vue";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";
import {getPage} from "@/api/itmctr-mgt";
import {FormDataQueryOperator} from "@/enums";



const props = defineProps<{
  businessId: string;
  formDefinition: FormDefinitionDto;
}>();

const initialized = ref(false);


const getStatusText = (row: any) => {
  return getRowFullStatusText(row);
}

onMounted(async () => {
  await loadData();
})
const loadData = async () => {
  try {
    const publicTitle = getFormDataValue(props.formDefinition.formData, 'PublicTitle');
    const publicTitleEN = getFormDataValue(props.formDefinition.formData, 'PublicTitleEN');


    let formDataDynamicQueries = [{
      key: "PublicTitle",
      operator: FormDataQueryOperator.Equal,
      value: publicTitle
    }]
    const cnData = await getProjects(formDataDynamicQueries) || [];

    cnProjects.value = cnData.rows;
    cnProjectLength.value = cnData.total

    formDataDynamicQueries = [{
      key: "PublicTitleEN",
      operator: FormDataQueryOperator.Equal,
      value: publicTitleEN
    }]
    const enData = await getProjects(formDataDynamicQueries) || [];

    enProjects.value = enData.rows;
    enProjectLength.value = enData.total

    if (cnProjects.value.length == 0) {
      currentTab.value = 'en';
    }

    initialized.value = true;
  } catch
      (error: any) {
    console.error("获取相似项目失败:", error);
  }
}
const getProjects = async (formDataDynamicQueries: any) => {
  const response = await getPage("projectSystemAllAvailableSubmittedList", {
            $pageIndex: 1,
            $pageSize: 20,
            $sortBy: "firstSubmitTime", // 初始化排序字段
            $orderBy: "desc", // 初始化排序方向
            formDataDynamicQueries: formDataDynamicQueries,
          }
      )
  ;
  let rows = response.data.rows;
  const filteredRows = rows.filter((row: any) => row.businessId.toLowerCase() != props.businessId.toLowerCase());
  let total = parseInt(response.data.totals, 10) || 0;

  if (filteredRows.length != rows.length) {
    total = total - 1;
  }

  return {rows: filteredRows, total: total};
}

const cnProjects = ref<any[]>([]);
const enProjects = ref<any[]>([]);
const cnProjectLength = ref<number>(0);
const enProjectLength = ref<number>(0);
const currentTab = ref<string>('cn');
// 在 script setup 的末尾添加
defineExpose({
  cnProjects,
  enProjects,
  initialized
});
</script>
<style scoped>
.similar-projects-header {
  margin: 0;
}

.el-card__header {
  padding-top: 0;
  padding-bottom: 0;
}

.el-card__body {
  padding: 0;
}

.similar-projects {
  margin-bottom: 20px;
}
</style>