<template>
  <el-dialog
      v-model="visible"
      width="420px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
      class="long-time-progress-dialog"
  >
    <div class="long-time-progress-content">
      <el-icon
          class="long-time-progress-icon rotating"
          style="font-size: 48px; margin-bottom: 8px" :is-loading="true"
      >
        <Loading/>
      </el-icon>
      <div class="long-time-progress-title">
        {{ props.title }}
      </div>
      <div
          class="long-time-progress-time"
          v-if="dialogStatus != 'timeout'"
      >
        预计时间{{
          time
        }}，当前剩余等待时间：{{ countdownText }}
      </div>
      <el-progress
          v-if="dialogStatus != 'timeout'"
          :percentage="progressPercent"
          :stroke-width="16"
          :show-text="false"
          style="margin: 24px 0 8px 0"
          color="#409eff"
          :status="dialogStatus === 'done' ? 'success' : ''"
      />
      <div
          class="long-time-progress-percent"
          v-if="dialogStatus != 'timeout'"
      >
        {{ progressPercent }}%
      </div>
      <div
          v-if="dialogStatus === 'timeout'"
          class="long-time-progress-hint"
      >
        等待太久还没有结果？请尝试点击
        <el-button
            type="primary"
            size="small"
            :disabled="!canHandleQuery"
            @click="handleQuery"
        >去查询
        </el-button
        >
        获得最新进展。
        <span
            v-if="dialogQueryCooldown > 0"
            style="margin-left: 8px; color: #999"
        >{{ dialogQueryCooldown }}s后可再次查询</span
        >
      </div>
      <div v-if="handleQueryResultMsg" class="long-time-progress-msg">
        {{ handleQueryResultMsg }}
      </div>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">

import {Loading} from "@element-plus/icons-vue";
import {computed, ref} from "vue";

defineExpose({
  showDialog
})

const props = defineProps<{
  title: string;
  total: number;
  pollInterval: number;
}>();

const handleQueryResultMsg = ref("");
const countdown = ref(0); // 剩余秒数
const visible = ref<boolean>(false);
const dialogStatus = ref<"waiting" | "timeout" | "done">("waiting");
const dialogQueryCooldown = ref(0);
const progressPercent = ref(0);
const canHandleQuery = ref(true);
let timer: any = null;
let handleQueryTimer: any = null;

const time = computed(() => {
  if (props.total < 60) {
    return props.total + "秒";
  }
  if (props.total < 3600) {
    return Math.floor(props.total / 60) + "分钟" + props.total % 60 + "秒";
  }
  return Math.floor(props.total / 3600) + "小时" + Math.floor(props.total / 60) % 60 + "分钟" + props.total % 60 + "秒";
})

// 定义事件
const emit = defineEmits<{
  (e: 'query-status', callback: (result: boolean) => void): void;
  (e: 'completed'): void;
}>();


async function handleQueryStatus(): Promise<boolean> {
  return await new Promise<boolean>(resolve => {
    emit('query-status', (result: boolean) => {
      resolve(result);
    });
  });
}

async function handleCompleted() {
  // 通知父组件处理完成
  emit('completed');
}


function showDialog() {
  visible.value = true;
  dialogStatus.value = "waiting";
  handleQueryResultMsg.value = "";
  countdown.value = props.total;
  progressPercent.value = 0;
  canHandleQuery.value = true;
  dialogQueryCooldown.value = 0;
  clearInterval(timer);
  clearInterval(handleQueryTimer);
  timer = setInterval(async () => {
    countdown.value--;
    progressPercent.value = Math.round(
        ((props.total - countdown.value) /
            props.total) *
        100
    );
    // 每45秒轮询一次getProgress
    if (
        (props.total - countdown.value) %
        props.pollInterval ===
        0 ||
        countdown.value === 0
    ) {
      const result = await handleQueryStatus();
      if (result) {
        dialogStatus.value = "done";
        await closeDialog();
        return;
      }
    }
    if (countdown.value <= 0) {
      dialogStatus.value = "timeout";
      clearInterval(timer);
    }
  }, 1000);
}

async function closeDialog() {
  clearInterval(timer);
  clearInterval(handleQueryTimer);
  visible.value = false;
  dialogStatus.value = "waiting";
  handleQueryResultMsg.value = "";
  canHandleQuery.value = true;
  dialogQueryCooldown.value = 0;
  countdown.value = 0;
  progressPercent.value = 0;

  await handleCompleted();

}

async function handleQuery() {
  if (!canHandleQuery.value) return;
  canHandleQuery.value = false;
  handleQueryResultMsg.value = "";

  const result = await handleQueryStatus();
  if (!result) {
    handleQueryResultMsg.value = "未查询成功，请稍后重试";
    dialogQueryCooldown.value = props.pollInterval;
    handleQueryTimer = setInterval(() => {
      dialogQueryCooldown.value--;
      if (dialogQueryCooldown.value <= 0) {
        canHandleQuery.value = true;
        clearInterval(handleQueryTimer);
      }
    }, 1000);
  } else {
    // 查询成功，直接关闭弹窗
    await closeDialog();
  }
}


const countdownText = computed(() => {
  const min = Math.floor(countdown.value / 60)
      .toString()
      .padStart(2, "0");
  const sec = (countdown.value % 60).toString().padStart(2, "0");
  if (min == "-1" || sec == "-1") {
    return "--";
  }
  return `${min}:${sec}`;
});


</script>
<style scoped lang="scss">

.long-time-progress-dialog :deep(.el-dialog__header) {
  display: none;
}

.long-time-progress-content {
  text-align: center;
  padding: 8px 0 0 0;
}

.long-time-progress-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  margin-top: 4px;
}

.long-time-progress-time {
  font-size: 15px;
  color: #666;
  margin-bottom: 8px;
}

.long-time-progress-percent {
  font-size: 16px;
  color: #409eff;
  font-weight: 600;
  margin-bottom: 8px;
}

.long-time-progress-hint {
  margin-top: 12px;
  color: #666;
  font-size: 14px;
}

.long-time-progress-msg {
  color: #d93025;
  margin-top: 8px;
  font-size: 14px;
}
.rotating {
  animation: rotate-animation 2s linear infinite;
  transform-origin: center center;
}

@keyframes rotate-animation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>