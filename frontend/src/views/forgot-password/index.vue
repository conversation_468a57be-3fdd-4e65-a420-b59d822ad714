<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="forgot-password-container">
    <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        class="forgot-password-form"
        label-position="top"
    >
      <div class="title-container">
        <h3 class="title">{{ $t("forgotPassword.title") }}</h3>
        <lang-select class="lang-select"/>
      </div>

      <!-- 步骤条 -->
      <el-steps :active="currentStep" finish-status="success" simple>
        <el-step :title="$t('forgotPassword.title')"/>
        <el-step :title="$t('resetPassword.title')"/>
      </el-steps>

      <!-- 第一步：忘记密码 -->
      <div v-if="currentStep === 0">
        <el-form-item prop="username">
          <el-input
              v-model="forgotPasswordForm.username"
              :placeholder="$t('forgotPassword.username')"
              type="text"
              autocomplete="on"
          >
            <template #prefix>
              <el-icon>
                <User/>
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="verifyMethod">
          <el-radio-group v-model="forgotPasswordForm.verifyMethod">
            <el-radio label="email">{{ $t("forgotPassword.email") }}</el-radio>
            <el-radio label="phone">{{ $t("forgotPassword.phone") }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="verifyValue">
          <el-input
              v-model="forgotPasswordForm.verifyValue"
              :placeholder="$t('forgotPassword.emailOrPhone')"
          />
        </el-form-item>

        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input
                v-model="forgotPasswordForm.captchaCode"
                :placeholder="$t('forgotPassword.captcha')"
                class="captcha-input"
            >
              <template #prefix>
                <el-icon>
                  <Picture/>
                </el-icon>
              </template>
            </el-input>
            <div class="captcha-image" @click="refreshCaptcha">
              <img v-if="captchaImage" :src="captchaImage" alt="验证码"/>
              <div v-else class="captcha-loading">
                <el-icon>
                  <Loading/>
                </el-icon>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-button
            :loading="loading"
            type="primary"
            class="submit-button"
            @click="handleSendVerificationCode"
        >
          {{ $t("forgotPassword.next") }}
        </el-button>

        <div class="form-footer">
          <router-link to="/login" class="back-link">
            {{ $t("forgotPassword.back") }}
          </router-link>
        </div>
      </div>

      <!-- 第二步：重置密码 -->
      <div v-if="currentStep === 1">
        <!-- 根据验证方式显示邮箱或手机号输入框 -->
<!--        <el-form-item-->
<!--            v-if="forgotPasswordForm.verifyMethod === 'email'"-->
<!--            prop="email"-->
<!--        >-->
<!--          <el-input-->
<!--              v-model="resetPasswordForm.email"-->
<!--              :placeholder="$t('forgotPassword.email')"-->
<!--              type="email"-->
<!--          >-->
<!--            <template #prefix>-->
<!--              <el-icon>-->
<!--                <Message/>-->
<!--              </el-icon>-->
<!--            </template>-->
<!--          </el-input>-->
<!--        </el-form-item>-->

<!--        <el-form-item-->
<!--            v-if="forgotPasswordForm.verifyMethod === 'phone'"-->
<!--            prop="phoneNumber"-->
<!--        >-->
<!--          <el-input-->
<!--              v-model="resetPasswordForm.phoneNumber"-->
<!--              :placeholder="$t('forgotPassword.phone')"-->
<!--              type="tel"-->
<!--          >-->
<!--            <template #prefix>-->
<!--              <el-icon>-->
<!--                <Phone/>-->
<!--              </el-icon>-->
<!--            </template>-->
<!--          </el-input>-->
<!--        </el-form-item>-->

        <el-form-item prop="code">
          <el-input
              v-model="resetPasswordForm.code"
              :placeholder="$t('resetPassword.verificationCode')"
              type="text"
          >
            <template #prefix>
              <el-icon>
                <Key/>
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="newPassword">
          <el-input
              v-model="resetPasswordForm.newPassword"
              :type="passwordVisible ? 'text' : 'password'"
              :placeholder="$t('resetPassword.newPassword')"
              autocomplete="new-password"
          >
            <template #prefix>
              <el-icon>
                <Lock/>
              </el-icon>
            </template>
            <template #suffix>
              <el-icon
                  class="show-pwd"
                  @click="passwordVisible = !passwordVisible"
              >
                <View v-if="passwordVisible"/>
                <Hide v-else/>
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="confirmNewPassword">
          <el-input
              v-model="resetPasswordForm.confirmNewPassword"
              :type="confirmPasswordVisible ? 'text' : 'password'"
              :placeholder="$t('resetPassword.confirmPassword')"
              autocomplete="new-password"
          >
            <template #prefix>
              <el-icon>
                <Lock/>
              </el-icon>
            </template>
            <template #suffix>
              <el-icon
                  class="show-pwd"
                  @click="confirmPasswordVisible = !confirmPasswordVisible"
              >
                <View v-if="confirmPasswordVisible"/>
                <Hide v-else/>
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-button
            :loading="loading"
            type="primary"
            class="submit-button"
            @click="handleResetPassword"
        >
          {{ $t("resetPassword.submit") }}
        </el-button>

        <div class="form-footer">
          <el-button type="text" @click="currentStep = 0">
            {{ $t("resetPassword.back") }}
          </el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from "vue";
import {useRouter} from "vue-router";
import {useI18n} from "vue-i18n";
import {ElMessage} from "element-plus";
import type {FormInstance, FormRules} from "element-plus";
import {
  User,
  Lock,
  View,
  Hide,
  Picture,
  Loading,
  Key,
  Message,
  Phone,
} from "@element-plus/icons-vue";
import LangSelect from "@/components/common/LangSelect.vue";
import {forgotPassword, resetPassword} from "@/api/rbac";
import {useUserStore} from "@/stores/user";

const {t} = useI18n();
const router = useRouter();
const userStore = useUserStore();
const forgotPasswordFormRef = ref<FormInstance>();
const loading = ref(false);
const captchaImage = ref<string>("");
const currentStep = ref(0);
const passwordVisible = ref(false);
const confirmPasswordVisible = ref(false);

// 忘记密码表单
const forgotPasswordForm = reactive({
  username: "",
  verifyMethod: "email",
  verifyValue: "",
  captchaId: "",
  captchaCode: "",
});

// 重置密码表单
const resetPasswordForm = reactive({
  username: "",
  email: "",
  phoneNumber: "",
  code: "",
  newPassword: "",
  confirmNewPassword: "",
});

// 表单验证规则
const forgotPasswordRules: FormRules = {
  username: [
    {required: true, trigger: "blur", message: t("forgotPassword.username")},
  ],
  verifyMethod: [
    {
      required: true,
      trigger: "change",
      message: t("forgotPassword.verifyMethod"),
    },
  ],
  verifyValue: [
    {required: true, trigger: "blur", message: t("forgotPassword.emailOrPhone")},
  ],
  captchaCode: [
    {required: true, trigger: "blur", message: t("forgotPassword.captcha")},
  ],
};

// 获取验证码
const refreshCaptcha = async () => {
  try {
    const captcha = await userStore.getCaptcha();
    if (captcha) {
      captchaImage.value = captcha.image;
      forgotPasswordForm.captchaId = captcha.id;
    }
  } catch (error) {
    console.error("Failed to get captcha:", error);
    // ElMessage.error("获取验证码失败，请刷新页面重试");
  }
};

// 发送验证码
const handleSendVerificationCode = async () => {
  if (!forgotPasswordFormRef.value) return;

  try {
    loading.value = true;
    await forgotPasswordFormRef.value.validate();

    // 构建忘记密码参数
    const forgotPasswordData = {
      username: forgotPasswordForm.username,
      verifyMethod: forgotPasswordForm.verifyMethod,
      verifyValue: forgotPasswordForm.verifyValue,
      captchaId: forgotPasswordForm.captchaId,
      captchaCode: forgotPasswordForm.captchaCode,
    };

    const result = await forgotPassword(forgotPasswordData);

    if (result.data) {
      ElMessage.success("验证码已发送，请查收/Verification code sent. Please check your messages.");

      // 设置重置密码表单的用户名
      resetPasswordForm.username = forgotPasswordForm.username;

      // 进入下一步
      currentStep.value = 1;
    } else {
      ElMessage.error("发送验证码失败，请重试/Failed to send verification code. Please try again.");
      await refreshCaptcha();
    }
  } catch (error: any) {
    console.error("Send verification code failed:", error);
    ElMessage.error(error.message || "发送验证码失败/Failed to send verification code");
    // 发送验证码失败后刷新验证码
    await refreshCaptcha();
  } finally {
    loading.value = false;
  }
};

// 重置密码
const handleResetPassword = async () => {
  if (!forgotPasswordFormRef.value) return;

  try {
    loading.value = true;

    // 验证表单
    await forgotPasswordFormRef.value.validate(async (valid, fields) => {
      if (!valid) {
        console.log("表单验证失败:", fields);
        loading.value = false;
        return;
      }

      // 构建重置密码参数
      const resetPasswordData = {
        // email: "",
        // phoneNumber: "",
        verifyMethod: forgotPasswordForm.verifyMethod,
        username: resetPasswordForm.username,
        code: resetPasswordForm.code,
        newPassword: resetPasswordForm.newPassword,
        confirmNewPassword: resetPasswordForm.confirmNewPassword,
      };

      // // 根据验证方式添加邮箱或手机号
      // if (forgotPasswordForm.verifyMethod === "email") {
      //   resetPasswordData.email = resetPasswordForm.email;
      // } else {
      //   resetPasswordData.phoneNumber = resetPasswordForm.phoneNumber;
      // }

      try {
        const result = await resetPassword(resetPasswordData);

        if (result.data) {
          ElMessage.success(t("resetPassword.success"));
          // 重置成功后跳转到登录页
          await router.push("/login");
        } else {
          ElMessage.error("重置密码失败，请重试/Failed to reset password. Please try again.");
        }
      } catch (error: any) {
        console.error("Reset password failed:", error);
        // ElMessage.error(error.message || "重置密码失败/Failed to reset password");
      } finally {
        loading.value = false;
      }
    });
  } catch (error: any) {
    console.error("Form validation error:", error);
    ElMessage.error(error.message || "表单验证失败/Form validation failed");
    loading.value = false;
  }
};

// 组件挂载时获取验证码
onMounted(() => {
  refreshCaptcha();
});
</script>

<style lang="scss" scoped>
.forgot-password-container {
  min-height: 100vh;
  width: 100%;
  background-color: #2d3a4b;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  .forgot-password-form {
    width: 450px;
    padding: 35px;
    border-radius: 6px;
    background: #fff;

    .title-container {
      position: relative;
      margin-bottom: 30px;
      text-align: center;

      .title {
        font-size: 26px;
        color: #333;
        margin: 0 auto 25px auto;
        font-weight: bold;
      }

      .lang-select {
        position: absolute;
        top: 4px;
        right: 0;
      }
    }

    :deep(.el-steps) {
      margin-bottom: 30px;
    }

    :deep(.el-input) {
      height: 48px;

      input {
        height: 48px;
      }

      .el-input__prefix {
        display: flex;
        align-items: center;
        color: #909399;
      }
    }

    .show-pwd {
      cursor: pointer;
      color: #909399;
    }

    .captcha-container {
      display: flex;
      align-items: center;

      .captcha-input {
        flex: 1;
      }

      .captcha-image {
        width: 120px;
        height: 48px;
        margin-left: 10px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .captcha-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
          color: #909399;
          font-size: 24px;

          .el-icon {
            animation: rotating 2s linear infinite;
          }
        }
      }
    }

    .submit-button {
      width: 100%;
      height: 48px;
      margin-top: 10px;
    }

    .form-footer {
      margin-top: 20px;
      text-align: center;

      .back-link {
        color: #409eff;
        font-size: 14px;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
