<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>{{ title }}</h1>
      <div class="dashboard-date">{{ currentDate }}</div>
    </div>

    <el-empty :image-size="200" v-if="!initialized" description="加载中..."/>
    <el-row v-if="initialized">
      <el-col :span="18">
        <el-row :gutter="20">
          <el-col :span="6" v-for="item in projectCountData" :key="item.define.category">
            <el-card shadow="hover" class="dashboard-card"
                     @click="router.push({ name: item.define.route })">
              <template #header>
                <div class="card-header">
                  <span>{{ $t(`menu.${item.define.route}`) }}</span>
                  <!-- <el-tag type="success">增长</el-tag> -->
                </div>
              </template>
              <div class="card-content">
                <div class="card-value">{{ item.count }}</div>
                <div class="card-chart">
                  <el-progress :percentage="180" :show-text="false" :color="item.define.color"/>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card" style="margin-left:20px">
          <template #header>
            <div class="card-header">
              <span> {{ $t('dashboard.quickNav') }}</span>
            </div>
          </template>
          <div class="quick-nav">


            <template
                v-for="link in links"
                :key="link.name">

              <el-button :type="link.type" plain @click="handleGoto(link)">
                {{ link.title }}
              </el-button>

            </template>


          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {getDashboardStatistics} from "@/api/itmctr";
import {useUserStore} from "@/stores/user";
import {useRouter} from 'vue-router'
import {useI18n} from "vue-i18n";

const {locale, t} = useI18n();
const router = useRouter()
// 获取当前日期
const currentDate = ref(new Date().toLocaleDateString(locale.value, {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
}))
const userStore = useUserStore();

const initialized = ref(false)

const positions = userStore.positions;

const roles = userStore.roles;


function handleGoto(link: any) {
  if (link.open) {
    // 如果链接是打开的，直接跳转到外部链接
    window.open(router.resolve({name: link.name}).href, '_blank', 'noopener');
    return;
  } else {
    // 跳转到指定的路由
    router.push({name: link.name});
  }
}

const links = computed(() => {


  if (roles.includes("RESEARCHER")) {
    return [
      {
        name: 'trialSearch',
        title: t('menu.trialSearch'),
        type: 'primary',
        open: true
      }

    ];
  } else if (roles.includes("CHECKER") || positions.includes("CHECKER_LEVEL_1") || positions.includes("CHECKER_LEVEL_2") || positions.includes("CHECKER_LEVEL_3") || positions.includes("CHECKER_LEVEL_4")) {
    return [{
      name: 'trialSearch',
      title: t('menu.trialSearch'),
      type: 'primary',
      open: true
    }, {
      name: 'reviewRule',
      title:  t('menu.reviewRule'),
      type: 'success',
      open: true
    }];


  } else {
    return [];
  }
});

// console.log(userStore.roles);
const title = computed(() => {
  if (roles.includes("RESEARCHER")) {
    return t('menu.projectCenter');
  } else if (roles.includes("CHECKER") || positions.includes("CHECKER_LEVEL_1") || positions.includes("CHECKER_LEVEL_2") || positions.includes("CHECKER_LEVEL_3") || positions.includes("CHECKER_LEVEL_4")) {
    return t('menu.projectApproval');
  } else {
    return t('menu.dashboard')
  }
});


const projectCountData = ref<Record<string, number>>({});
onMounted(async () => {
  await fetchDashboardStatistics();
});


// 获取项目统计数据
async function fetchDashboardStatistics() {
  try {

    const response = await getDashboardStatistics();
    response.data.forEach((item: any) => {
      item.define.title = t(`menu.${item.define.route}`);
    });
    projectCountData.value = response.data;
    initialized.value = true;
    return true;
  } catch (error) {
    console.error('获取项目统计数据失败:', error);
  }
}


</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      font-size: 24px;
      color: #303133;
    }

    .dashboard-date {
      font-size: 14px;
      color: #909399;
    }
  }

  .dashboard-row {
    margin-top: 20px;
  }

  .dashboard-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      padding: 10px 0;

      .card-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
      }
    }

    .welcome-content {
      display: flex;
      align-items: center;

      .welcome-logo {
        width: 80px;
        height: 80px;
        margin-right: 20px;
      }

      .welcome-text {
        h2 {
          margin-top: 0;
          margin-bottom: 10px;
          color: #303133;
        }

        p {
          margin: 5px 0;
          color: #606266;
        }
      }
    }

    .quick-nav {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .el-button {
        margin: 0;
      }
    }
  }
}
</style>