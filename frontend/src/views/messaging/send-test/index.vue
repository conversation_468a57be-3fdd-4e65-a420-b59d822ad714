<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>消息发送测试</span>
        </div>
      </template>

      <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="120px"
          style="max-width: 600px"
      >
        <el-form-item label="应用编码" prop="appCode">
          <el-input
              v-model="form.appCode"
              placeholder="请输入应用编码"
          />
        </el-form-item>

        <el-form-item label="消息类型" prop="messageType">
          <el-select
              v-model="form.messageType"
              placeholder="请选择消息类型"
              style="width: 100%"
              @change="handleMessageTypeChange"
          >
            <el-option
                v-for="item in MESSAGE_TYPE_OPTIONS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="发送目标" prop="target">
          <el-input
              v-model="form.target"
              :placeholder="getTargetPlaceholder()"
          />
          <div class="form-tip">
            {{ getTargetTip() }}
          </div>
        </el-form-item>

        <el-form-item label="模板编码" prop="templateCode">
          <el-select
              v-model="form.templateCode"
              placeholder="请选择模板"
              style="width: 100%"
              filterable
              @change="handleTemplateChange"
          >
            <el-option
                v-for="item in templateOptions"
                :key="item.templateCode"
                :label="`${item.templateCode} - ${item.templateName}`"
                :value="item.templateCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="currentTemplate" label="模板内容">
          <div class="template-content" v-html="currentTemplate.content">
          </div>
        </el-form-item>

        <el-form-item label="变量JSON">
          <el-input
              v-model="form.variablesJson"
              type="textarea"
              :rows="4"
              placeholder='请输入变量JSON，例如：{"code":"123456","name":"张三"}'
          />
          <div class="form-tip">
            JSON格式的变量数据，用于替换模板中的变量
          </div>
        </el-form-item>

        <el-form-item label="指定服务商">
          <el-select
              v-model="form.providerCode"
              placeholder="请选择服务商（可选）"
              style="width: 100%"
              clearable
          >
            <el-option
                v-for="item in providerOptions"
                :key="item.providerCode"
                :label="`${item.providerCode} - ${item.providerName}`"
                :value="item.providerCode"
            />
          </el-select>
          <div class="form-tip">
            不选择时将使用默认服务商
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="sendLoading" @click="handleSend">
            发送消息
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="info" @click="handleValidateJson">验证JSON</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 发送结果 -->
    <el-card v-if="sendResults.length > 0" class="box-card" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>发送记录</span>
          <el-button type="text" @click="sendResults = []">清空</el-button>
        </div>
      </template>

      <el-timeline>
        <el-timeline-item
            v-for="(result, index) in sendResults"
            :key="index"
            :timestamp="result.timestamp"
            :type="result.success ? 'success' : 'danger'"
        >
          <el-card>
            <div class="result-item">
              <div class="result-header">
                <el-tag :type="result.success ? 'success' : 'danger'">
                  {{ result.success ? '发送成功' : '发送失败' }}
                </el-tag>
                <span class="result-target">{{ result.target }}</span>
              </div>
              <div class="result-content">
                <p><strong>应用:</strong> {{ result.appCode }}</p>
                <p><strong>类型:</strong> {{ getMessageTypeLabel(result.messageType) }}</p>
                <p><strong>模板:</strong> {{ result.templateCode }}</p>
                <p v-if="result.providerCode"><strong>服务商:</strong> {{ result.providerCode }}</p>
                <p v-if="result.variables"><strong>变量:</strong> {{ result.variables }}</p>
                <p v-if="result.error" class="error-message"><strong>错误:</strong> {{ result.error }}</p>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from 'vue'
import {ElMessage} from 'element-plus'
import type {FormInstance, FormRules} from 'element-plus'
import {messageTemplateApi, messageProviderApi} from '@/api/messaging-mgt'
import {messageSendBusinessApi} from '@/api/messaging'
import type {
  SendMessageRequestDto,
  MessageTemplateDto,
  MessageProviderDto
} from '@/dtos/messaging-mgt.dto'
import {MESSAGE_TYPE_OPTIONS, MessageType, getMessageTypeLabel} from '@/enums/messaging'

// 发送结果接口
interface SendResult {
  timestamp: string
  success: boolean
  target: string
  appCode: string
  messageType: string
  templateCode: string
  providerCode?: string
  variables?: string
  error?: string
}

// 响应式数据
const sendLoading = ref(false)
const templateOptions = ref<MessageTemplateDto[]>([])
const providerOptions = ref<MessageProviderDto[]>([])
const currentTemplate = ref<MessageTemplateDto>()
const sendResults = ref<SendResult[]>([])

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<SendMessageRequestDto>({
  appCode: '',
  target: '',
  templateCode: '',
  variablesJson: '',
  messageType: '',
  providerCode: ''
})

// 表单验证规则
const formRules: FormRules = {
  appCode: [{required: true, message: '请输入应用编码', trigger: 'blur'}],
  messageType: [{required: true, message: '请选择消息类型', trigger: 'change'}],
  target: [{required: true, message: '请输入发送目标', trigger: 'blur'}],
  templateCode: [{required: true, message: '请选择模板', trigger: 'change'}]
}

// 获取目标输入提示
const getTargetPlaceholder = () => {
  switch (form.messageType) {
    case MessageType.SMS:
      return '请输入手机号，例如：13800138000'
    case MessageType.EMAIL:
      return '请输入邮箱地址，例如：<EMAIL>'
    default:
      return '请输入发送目标'
  }
}

// 获取目标输入说明
const getTargetTip = () => {
  switch (form.messageType) {
    case MessageType.SMS:
      return '支持中国大陆11位手机号'
    case MessageType.EMAIL:
      return '请输入有效的邮箱地址'
    default:
      return ''
  }
}

// 获取模板列表
const getTemplateOptions = async () => {
  if (!form.appCode) {
    templateOptions.value = []
    return
  }

  try {
    const {data} = await messageTemplateApi.getByApp(form.appCode)
    templateOptions.value = data.filter(t => t.isEnabled && t.templateType === form.messageType)
  } catch (error) {
    ElMessage.error('获取模板列表失败')
  }
}

// 获取服务商列表
const getProviderOptions = async () => {
  if (!form.messageType) {
    providerOptions.value = []
    return
  }

  try {
    const {data} = await messageProviderApi.getByType(form.messageType)
    providerOptions.value = data.filter(p => p.isEnabled)
  } catch (error) {
    ElMessage.error('获取服务商列表失败')
  }
}

// 消息类型变更
const handleMessageTypeChange = () => {
  form.templateCode = ''
  form.providerCode = ''
  currentTemplate.value = undefined
  getTemplateOptions()
  getProviderOptions()
}

// 模板变更
const handleTemplateChange = () => {
  currentTemplate.value = templateOptions.value.find(t => t.templateCode === form.templateCode)
}

// 发送消息
const handleSend = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    // 表单验证失败，直接返回
    return
  }

  // 验证JSON格式
  if (form.variablesJson) {
    try {
      JSON.parse(form.variablesJson)
    } catch {
      ElMessage.error('变量JSON格式不正确')
      return
    }
  }

  sendLoading.value = true
  try {
    await messageSendBusinessApi.send(form)

    // 记录发送结果
    sendResults.value.unshift({
      timestamp: new Date().toLocaleString(),
      success: true,
      target: form.target,
      appCode: form.appCode,
      messageType: form.messageType,
      templateCode: form.templateCode,
      providerCode: form.providerCode,
      variables: form.variablesJson
    })

    ElMessage.success('消息发送成功')
  } catch (error: any) {
    // 记录发送失败
    sendResults.value.unshift({
      timestamp: new Date().toLocaleString(),
      success: false,
      target: form.target,
      appCode: form.appCode,
      messageType: form.messageType,
      templateCode: form.templateCode,
      providerCode: form.providerCode,
      variables: form.variablesJson,
      error: error.message || '发送失败'
    })

    ElMessage.error('消息发送失败')
  } finally {
    sendLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    appCode: '',
    target: '',
    templateCode: '',
    variablesJson: '',
    messageType: '',
    providerCode: ''
  })
  currentTemplate.value = undefined
  templateOptions.value = []
  providerOptions.value = []
}

// 验证JSON
const handleValidateJson = () => {
  if (!form.variablesJson) {
    ElMessage.warning('请先输入变量JSON')
    return
  }

  try {
    const parsed = JSON.parse(form.variablesJson)
    ElMessage.success('JSON格式正确')
    console.log('解析结果:', parsed)
  } catch (error) {
    ElMessage.error('JSON格式不正确')
  }
}

// 监听应用编码变化
const handleAppCodeChange = () => {
  form.templateCode = ''
  currentTemplate.value = undefined
  getTemplateOptions()
}

// 初始化
onMounted(() => {
  // 可以在这里设置一些默认值
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.template-content {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.result-item {
  padding: 0;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.result-target {
  font-weight: bold;
  color: #303133;
}

.result-content p {
  margin: 4px 0;
  font-size: 14px;
}

.error-message {
  color: #f56c6c !important;
}
</style>
