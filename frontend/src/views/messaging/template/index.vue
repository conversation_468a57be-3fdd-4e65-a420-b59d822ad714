<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
        :form-items="searchFormItems"
        :initial-values="queryParams"
        @search="handleSearch"
        @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
        title="模板管理"
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :current-page-prop="queryParams.$pageIndex"
        :page-size-prop="queryParams.$pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button type="primary" @click="handleAdd">
          <el-icon>
            <Plus/>
          </el-icon>
          新增模板
        </el-button>
      </template>

      <!-- 消息类型列 -->
      <template #templateType="{ row }">
        <el-tag>{{ getMessageTypeLabel(row.templateType) }}</el-tag>
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="row.isEnabled ? 'success' : 'danger'">
          {{ row.isEnabled ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- 标题列 -->
      <template #title="{ row }">
        <span class="content-preview">{{ row.title }}</span>
      </template>

      <!-- 创建时间列 -->
      <template #createdTime="{ row }">
        {{ formatDateTimeOffsetToLocalDateTime(row.createdTime) }}
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="info" link @click="handlePreview(row)">预览</el-button>
        <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
      </template>
    </DataTable>

    <!-- 模板表单对话框 -->
    <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="800px"
        append-to-body
        destroy-on-close
    >
      <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="120px"
      >
        <el-form-item label="模板编码" prop="templateCode">
          <el-input
              v-model="form.templateCode"
              placeholder="请输入模板编码"
              :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="模板名称" prop="templateName">
          <el-input
              v-model="form.templateName"
              placeholder="请输入模板名称"
          />
        </el-form-item>
        <el-form-item label="应用编码" prop="appCode">
          <el-input
              v-model="form.appCode"
              placeholder="请输入应用编码"
          />
        </el-form-item>
        <el-form-item label="消息类型" prop="templateType">
          <el-select v-model="form.templateType" placeholder="请选择消息类型" style="width: 100%">
            <el-option
                v-for="item in MESSAGE_TYPE_OPTIONS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模板标题" prop="title">
          <el-input
              v-model="form.title"
              placeholder="请输入模板标题，使用 {{变量名}} 格式定义变量"
          />
          <div class="form-tip">
            使用 {{ 变量名 }} 格式定义变量，例如：您的验证码是{{ code }}，有效期{{ expiry }}分钟。
          </div>
        </el-form-item>
        <el-form-item label="模板内容" prop="content">
          <el-input
              v-model="form.content"
              type="textarea"
              :rows="6"
              placeholder="请输入模板内容，使用 {{变量名}} 格式定义变量"
          />
          <div class="form-tip">
            使用 {{ 变量名 }} 格式定义变量，例如：您的验证码是{{ code }}，有效期{{ expiry }}分钟。
          </div>
        </el-form-item>
        <el-form-item label="变量说明">
          <el-input
              v-model="form.variables"
              type="textarea"
              :rows="3"
              placeholder="请输入变量说明，例如：code-验证码，expiry-有效期"
          />
        </el-form-item>
        <el-form-item label="状态" prop="isEnabled">
          <el-switch v-model="form.isEnabled"/>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog
        title="模板预览"
        v-model="previewVisible"
        width="800px"
        append-to-body
    >
      <div v-if="previewTemplate">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板编码">{{ previewTemplate.templateCode }}</el-descriptions-item>
          <el-descriptions-item label="模板名称">{{ previewTemplate.templateName }}</el-descriptions-item>
          <el-descriptions-item label="应用编码">{{ previewTemplate.appCode }}</el-descriptions-item>
          <el-descriptions-item label="消息类型">
            <el-tag>{{ getMessageTypeLabel(previewTemplate.templateType) }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <div style="margin-top: 20px;">
          <h4>模板标题：</h4>
          <div class="template-content">{{ previewTemplate.title }}</div>
        </div>
        <div style="margin-top: 20px;">
          <h4>模板内容：</h4>
          <div class="template-content" v-html="previewTemplate.content"></div>
        </div>

        <div v-if="previewTemplate.variables" style="margin-top: 20px;">
          <h4>变量说明：</h4>
          <div class="template-variables">{{ previewTemplate.variables }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus} from '@element-plus/icons-vue'
import type {FormInstance, FormRules} from 'element-plus'
import SearchForm, {FormItem} from '@/components/common/SearchForm.vue'
import DataTable, {TableColumn} from '@/components/common/DataTable.vue'
import {messageTemplateApi} from '@/api/messaging-mgt'
import type {
  MessageTemplateDto,
  MessageTemplateQueryCriteria,
  CreateTemplateRequestDto,
  UpdateTemplateRequestDto
} from '@/dtos/messaging-mgt.dto'
import {MESSAGE_TYPE_OPTIONS, getMessageTypeLabel} from '@/enums/messaging'
import {formatDateTimeOffsetToLocalDateTime} from '@/utils/date'

// 搜索表单项配置
const searchFormItems = computed(() => [
  {
    type: "input",
    label: "模板编码",
    prop: "templateCode",
    placeholder: "请输入模板编码",
  },
  {
    type: "input",
    label: "模板名称",
    prop: "templateName",
    placeholder: "请输入模板名称",
  },
  {
    type: "input",
    label: "应用编码",
    prop: "appCode",
    placeholder: "请输入应用编码",
  },
  {
    type: "select",
    label: "消息类型",
    prop: "templateType",
    placeholder: "请选择消息类型",
    options: MESSAGE_TYPE_OPTIONS,
  },
  {
    type: "select",
    label: "状态",
    prop: "isEnabled",
    placeholder: "请选择状态",
    options: [
      {label: "启用", value: true},
      {label: "禁用", value: false},
    ],
  },
] as FormItem[])

// 表格列配置
const tableColumns = computed(() => [
  {prop: "templateCode", label: "模板编码", width: 150},
  {prop: "templateName", label: "模板名称", width: 200},
  {prop: "appCode", label: "应用编码", width: 120},
  {prop: "templateType", label: "消息类型", slot: "templateType", width: 120},
  {prop: "title", label: "模板标题", slot: "title", width: 200},
  {prop: "isEnabled", label: "状态", slot: "status", width: 100},
  {prop: "createdTime", label: "创建时间", slot: "createdTime", width: 180},
] as TableColumn[])

// 响应式数据
const loading = ref(false)
const total = ref(0)
const tableData = ref<MessageTemplateDto[]>([])
const dialogVisible = ref(false)
const previewVisible = ref(false)
const submitLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref<MessageTemplateDto>()
const previewTemplate = ref<MessageTemplateDto>()

// 表单引用
const formRef = ref<FormInstance>()

// 查询参数
const queryParams = reactive<MessageTemplateQueryCriteria>({
  $pageIndex: 1,
  $pageSize: 20,
  templateCode: undefined,
  templateName: undefined,
  appCode: undefined,
  templateType: undefined,
  isEnabled: undefined,
})

// 表单数据
const form = reactive<CreateTemplateRequestDto>({
  templateCode: '',
  templateName: '',
  appCode: '',
  templateType: '',
  title: '',
  content: '',
  variables: '',
  isEnabled: true,
  remark: ''
})

// 表单验证规则
const formRules: FormRules = {
  templateCode: [{required: true, message: '请输入模板编码', trigger: 'blur'}],
  templateName: [{required: true, message: '请输入模板名称', trigger: 'blur'}],
  appCode: [{required: true, message: '请输入应用编码', trigger: 'blur'}],
  templateType: [{required: true, message: '请选择消息类型', trigger: 'change'}],
  content: [{required: true, message: '请输入模板内容', trigger: 'blur'}]
}

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑模板' : '新增模板'
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const {data} = await messageTemplateApi.getPage(queryParams)
    tableData.value = data.rows
    total.value = data.totals
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = async (formData: any) => {
  Object.assign(queryParams, formData)
  queryParams.$pageIndex = 1
  await getList()
}

// 重置处理
const handleReset = async (formData: any) => {
  Object.assign(queryParams, formData)
  await getList()
}

// 分页处理
const handleSizeChange = async (val: number) => {
  queryParams.$pageSize = val
  await getList()
}

const handleCurrentChange = async (val: number) => {
  queryParams.$pageIndex = val
  await getList()
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    templateCode: '',
    templateName: '',
    appCode: '',
    templateType: '',
    title: '',
    content: '',
    variables: '',
    isEnabled: true,
    remark: ''
  })
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: MessageTemplateDto) => {
  isEdit.value = true
  currentRecord.value = row
  Object.assign(form, {
    templateCode: row.templateCode,
    templateName: row.templateName,
    appCode: row.appCode,
    templateType: row.templateType,
    title: row.title,
    content: row.content,
    variables: row.variables || '',
    isEnabled: row.isEnabled,
    remark: row.remark || ''
  })
  dialogVisible.value = true
}

// 预览
const handlePreview = (row: MessageTemplateDto) => {
  previewTemplate.value = row
  previewVisible.value = true
}

// 删除
const handleDelete = async (row: MessageTemplateDto) => {
  try {
    await ElMessageBox.confirm('确认要删除该模板吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await messageTemplateApi.delete(row.key)
    ElMessage.success('删除成功')
    await getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    // 表单验证失败，直接返回
    return
  }

  submitLoading.value = true
  try {
    if (isEdit.value && currentRecord.value) {
      await messageTemplateApi.update(currentRecord.value.key, form as UpdateTemplateRequestDto)
      ElMessage.success('修改成功')
    } else {
      await messageTemplateApi.create(form)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.content-preview {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-content {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  white-space: pre-wrap;
  word-break: break-word;
}

.template-variables {
  background-color: #f0f9ff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
  color: #409eff;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
