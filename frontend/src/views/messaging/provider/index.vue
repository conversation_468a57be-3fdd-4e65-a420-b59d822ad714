<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
        :form-items="searchFormItems"
        :initial-values="queryParams"
        @search="handleSearch"
        @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
        title="服务商管理"
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :current-page-prop="queryParams.$pageIndex"
        :page-size-prop="queryParams.$pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button type="primary" @click="handleAdd">
          <el-icon>
            <Plus/>
          </el-icon>
          新增服务商
        </el-button>
      </template>

      <!-- 消息类型列 -->
      <template #providerType="{ row }">
        <el-tag>{{ getMessageTypeLabel(row.providerType) }}</el-tag>
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="row.isEnabled ? 'success' : 'danger'">
          {{ row.isEnabled ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- 创建时间列 -->
      <template #createdTime="{ row }">
        {{ formatDateTimeOffsetToLocalDateTime(row.createdTime) }}
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
      </template>
    </DataTable>

    <!-- 服务商表单对话框 -->
    <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="600px"
        append-to-body
        destroy-on-close
    >
      <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="120px"
      >
        <el-form-item label="服务商编码" prop="providerCode">
          <el-input
              v-model="form.providerCode"
              placeholder="请输入服务商编码"
              :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="服务商名称" prop="providerName">
          <el-input
              v-model="form.providerName"
              placeholder="请输入服务商名称"
          />
        </el-form-item>
        <el-form-item label="消息类型" prop="providerType">
          <el-select v-model="form.providerType" placeholder="请选择消息类型" style="width: 100%"
                     @change="onProviderTypeChange">
            <el-option
                v-for="item in MESSAGE_TYPE_OPTIONS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务商实例" prop="providerInstance">
          <el-select v-model="form.providerInstance" placeholder="请选择服务商实例" style="width: 100%" clearable>
            <el-option
                v-for="item in providerInstanceByType"
                :key="item"
                :label="item"
                :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置信息" prop="config">
          <el-input
              v-model="form.config"
              type="textarea"
              :rows="4"
              placeholder="请输入服务商配置信息（JSON格式）"
          />
        </el-form-item>
        <el-form-item label="状态" prop="isEnabled">
          <el-switch v-model="form.isEnabled"/>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus} from '@element-plus/icons-vue'
import type {FormInstance, FormRules} from 'element-plus'
import SearchForm, {FormItem} from '@/components/common/SearchForm.vue'
import DataTable, {TableColumn} from '@/components/common/DataTable.vue'
import {messageProviderApi} from '@/api/messaging-mgt'
import type {
  MessageProviderDto,
  MessageProviderQueryCriteria,
  CreateProviderRequestDto,
  UpdateProviderRequestDto
} from '@/dtos/messaging-mgt.dto'
import {MESSAGE_TYPE_OPTIONS, getMessageTypeLabel} from '@/enums/messaging'
import {formatDateTimeOffsetToLocalDateTime} from '@/utils/date'

const PROVIDER_INSTANCES = ref<Record<string, string[]>>({});

// 搜索表单项配置
const searchFormItems = computed(() => [
  {
    type: "input",
    label: "服务商编码",
    prop: "providerCode",
    placeholder: "请输入服务商编码",
  },
  {
    type: "input",
    label: "服务商名称",
    prop: "providerName",
    placeholder: "请输入服务商名称",
  },
  {
    type: "select",
    label: "消息类型",
    prop: "providerType",
    placeholder: "请选择消息类型",
    options: MESSAGE_TYPE_OPTIONS,
  },
  {
    type: "select",
    label: "状态",
    prop: "isEnabled",
    placeholder: "请选择状态",
    options: [
      {label: "启用", value: true},
      {label: "禁用", value: false},
    ],
  },
] as FormItem[])

// 表格列配置
const tableColumns = computed(() => [
  {prop: "providerCode", label: "服务商编码", width: 150},
  {prop: "providerName", label: "服务商名称", width: 200},
  {prop: "providerType", label: "消息类型", slot: "providerType", width: 120},
  {prop: "providerInstance", label: "服务商实例", width: 120},
  {prop: "isEnabled", label: "状态", slot: "status", width: 100},
  {prop: "createdTime", label: "创建时间", slot: "createdTime", width: 180},
  {prop: "remark", label: "备注"},
] as TableColumn[])

// 响应式数据
const loading = ref(false)
const total = ref(0)
const tableData = ref<MessageProviderDto[]>([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref<MessageProviderDto>()

// 表单引用
const formRef = ref<FormInstance>()

// 查询参数
const queryParams = reactive<MessageProviderQueryCriteria>({
  $pageIndex: 1,
  $pageSize: 20,
  providerCode: undefined,
  providerName: undefined,
  providerType: undefined,
  providerInstance: undefined,
  isEnabled: undefined,
})

// 表单数据
const form = reactive<CreateProviderRequestDto>({
  providerCode: '',
  providerName: '',
  providerType: '',
  providerInstance: '',
  config: '',
  isEnabled: true,
  remark: ''
})

// 表单验证规则
const formRules: FormRules = {
  providerCode: [{required: true, message: '请输入服务商编码', trigger: 'blur'}],
  providerName: [{required: true, message: '请输入服务商名称', trigger: 'blur'}],
  providerType: [{required: true, message: '请选择消息类型', trigger: 'change'}],
  providerInstance: [{required: true, message: '请选择服务商实例', trigger: 'change'}],
  config: [{required: true, message: '请输入配置信息', trigger: 'blur'}]
}

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑服务商' : '新增服务商'
})

const providerInstanceByType = computed(() => {
  return PROVIDER_INSTANCES.value[form.providerType] || []
})


// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const {data} = await messageProviderApi.getPage(queryParams)
    tableData.value = data.rows
    total.value = data.totals
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData)
  queryParams.$pageIndex = 1
  getList()
}

// 重置处理
const handleReset = (formData: any) => {
  Object.assign(queryParams, formData)
  getList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val
  getList()
}

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val
  getList()
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    providerCode: '',
    providerName: '',
    providerType: '',
    providerInstance: '',
    config: '',
    isEnabled: true,
    remark: ''
  })
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: MessageProviderDto) => {
  isEdit.value = true
  currentRecord.value = row
  Object.assign(form, {
    providerCode: row.providerCode,
    providerName: row.providerName,
    providerType: row.providerType,
    providerInstance: row.providerInstance,
    config: row.config,
    isEnabled: row.isEnabled,
    remark: row.remark || ''
  })
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: MessageProviderDto) => {
  try {
    await ElMessageBox.confirm('确认要删除该服务商吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await messageProviderApi.delete(row.key)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    // 表单验证失败，直接返回
    return
  }

  submitLoading.value = true
  try {
    if (isEdit.value && currentRecord.value) {
      await messageProviderApi.update(currentRecord.value.key, form as UpdateProviderRequestDto)
      ElMessage.success('修改成功')
    } else {
      await messageProviderApi.create(form)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}
const getProviderInstances = async () => {
  try {
    const {data} = await messageProviderApi.getInstanceNames()
    PROVIDER_INSTANCES.value = data
  } catch (error) {
    ElMessage.error('获取服务商实例失败')
  }
}
function onProviderTypeChange() {
  
    form.providerInstance = ''
}
// 初始化
onMounted(() => {

  getProviderInstances();
  getList()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style>
