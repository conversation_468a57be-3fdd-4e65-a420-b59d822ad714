<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
        :form-items="searchFormItems"
        :initial-values="queryParams"
        @search="handleSearch"
        @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
        title="账户管理"
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :current-page-prop="queryParams.pageIndex"
        :page-size-prop="queryParams.pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button type="primary" @click="handleAdd">
          <el-icon>
            <Plus/>
          </el-icon>
          新增账户
        </el-button>
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="row.isEnabled ? 'success' : 'danger'">
          {{ row.isEnabled ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- 创建时间列 -->
      <template #createdTime="{ row }">
        {{ formatDateTime(row.createdTime) }}
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="success" link @click="handleManageProviders(row)">服务商管理</el-button>
        <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
      </template>
    </DataTable>

    <!-- 账户表单对话框 -->
    <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="500px"
        append-to-body
        destroy-on-close
    >
      <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="100px"
      >
        <el-form-item label="账户编码" prop="accountCode">
          <el-input
              v-model="form.accountCode"
              placeholder="请输入账户编码"
              :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="账户名称" prop="accountName">
          <el-input
              v-model="form.accountName"
              placeholder="请输入账户名称"
          />
        </el-form-item>
        <el-form-item label="应用编码" prop="appCode">
          <el-input
              v-model="form.appCode"
              placeholder="请输入应用编码"
          />
        </el-form-item>
        <el-form-item label="状态" prop="isEnabled">
          <el-switch v-model="form.isEnabled"/>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 服务商管理对话框 -->
    <el-dialog
        title="服务商管理"
        v-model="providerDialogVisible"
        width="1200px"
        append-to-body
        destroy-on-close
    >
      <div v-if="currentAccount">
        <div style="margin-bottom: 20px;">
          <el-button type="primary" @click="handleAddProvider">
            <el-icon>
              <Plus/>
            </el-icon>
            添加服务商
          </el-button>
        </div>

        <el-table :data="accountProviders" border style="width: 100%">
          <el-table-column prop="providerCode" label="服务商编码" width="150"/>
          <el-table-column prop="providerType" label="消息类型" width="120">
            <template #default="{ row }">
              <el-tag>{{ getMessageTypeLabel(row.providerType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100"/>

          <el-table-column prop="quotaTotal" label="总额度" width="100">
            <template #default="{ row }">
              <span style="color: #409eff; font-weight: bold;">{{ row.quotaTotal || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="quotaUsed" label="已用额度" width="100">
            <template #default="{ row }">
              <span style="color: #f56c6c; font-weight: bold;">{{ row.quotaUsed || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余额度" width="100">
            <template #default="{ row }">
              <span style="color: #67c23a; font-weight: bold;">
                {{ (row.quotaTotal || 0) - (row.quotaUsed || 0) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="isEnabled" label="状态" width="100">
            <template #default="{ row }">
              <el-switch
                  v-model="row.isEnabled"
                  @change="handleProviderStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleEditProvider(row)">编辑</el-button>
              <el-button type="success" link @click="handleRecharge(row)">充值</el-button>
              <el-button type="info" link @click="handleViewRechargeHistory(row)">充值历史</el-button>

              <el-button type="danger" link @click="handleRemoveProvider(row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 添加服务商对话框 -->
    <el-dialog
        title="添加服务商"
        v-model="addProviderDialogVisible"
        width="1000px"
        append-to-body
        destroy-on-close
    >
      <el-form
          ref="providerFormRef"
          :model="providerForm"
          :rules="providerFormRules"
          label-width="120px"
      >
        <el-form-item label="服务商" prop="providers">
          <el-table :data="providerForm.providers" border style="width: 100%">
            <el-table-column label="服务商" width="400">
              <template #default="{ row, $index }">
                <el-select
                    v-model="row.providerCode"
                    placeholder="请选择服务商"
                    style="width: 100%"
                    @change="handleSingleProviderSelect($index)"
                >
                  <el-option
                      v-for="item in getAvailableProvidersForRow($index)"
                      :key="item.providerCode"
                      :label="`${item.providerCode} - ${item.providerName}`"
                      :value="item.providerCode"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="消息类型" width="120">
              <template #default="{ row }">
                <el-tag v-if="row.providerType">{{ getMessageTypeLabel(row.providerType) }}</el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="优先级" width="120">
              <template #default="{ row }">
                <el-input-number
                    v-model="row.priority"
                    :min="1"
                    :max="99"
                    size="small"
                    style="width: 80px"
                />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button
                    type="danger"
                    link
                    size="small"
                    @click="removeProviderRow($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 10px;">
            <el-button type="primary" size="small" @click="addProviderRow">
              <el-icon>
                <Plus/>
              </el-icon>
              添加服务商
            </el-button>
          </div>
          <div class="form-tip">
            优先级数字越小优先级越高
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addProviderDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="addProviderLoading" @click="handleConfirmAddProvider">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 充值对话框 -->
    <el-dialog
        title="账户充值"
        v-model="rechargeDialogVisible"
        width="500px"
        append-to-body
        destroy-on-close
    >
      <el-form
          ref="rechargeFormRef"
          :model="rechargeForm"
          :rules="rechargeFormRules"
          label-width="100px"
      >
        <el-form-item label="账户编码">
          <el-input v-model="rechargeForm.accountCode" disabled/>
        </el-form-item>
        <el-form-item label="服务商">
          <el-input v-model="rechargeForm.providerCode" disabled/>
        </el-form-item>
        <el-form-item label="充值金额" prop="amount">
          <el-input-number
              v-model="rechargeForm.amount"
              :min="1"
              :max="999999"
              style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
              v-model="rechargeForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="rechargeDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="rechargeLoading" @click="handleConfirmRecharge">
            确认充值
          </el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 充值记录对话框 -->
    <el-dialog
        :title="rechargeHistoryDialogTitle"
        v-model="rechargeHistoryDialogVisible"
        width="900px"
        append-to-body
        destroy-on-close
    >
      <el-table
          :data="rechargeHistory"
          :loading="rechargeHistoryLoading"
          border
          style="width: 100%"
      >
        <el-table-column prop="balanceBefore" label="充值前余额" width="120">
          <template #default="{ row }">
            {{ row.balanceAfter - row.amount }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="充值金额" width="120">
          <template #default="{ row }">
            <span style="color: #67c23a; font-weight: bold;">+{{ row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="balanceAfter" label="充值后余额" width="120"/>
        <el-table-column prop="rechargeTime" label="充值时间" width="180">
          <template #default="{ row }">
            {{ formatDateTimeOffsetToLocalDateTime(row.rechargeTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" width="120"/>
        <el-table-column prop="remark" label="备注"/>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
            v-model:current-page="rechargeHistoryQueryParams.$pageIndex"
            v-model:page-size="rechargeHistoryQueryParams.$pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="rechargeHistoryTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleRechargeHistorySizeChange"
            @current-change="handleRechargeHistoryCurrentChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus} from '@element-plus/icons-vue'
import type {FormInstance, FormRules} from 'element-plus'
import SearchForm, {FormItem} from '@/components/common/SearchForm.vue'
import DataTable, {TableColumn} from '@/components/common/DataTable.vue'
import {
  messageAccountApi,
  messageAccountProviderApi,
  messageProviderApi,
  messageAccountRechargeApi
} from '@/api/messaging-mgt'
import type {
  MessageAccountDto,
  MessageAccountQueryCriteria,
  CreateAccountRequestDto,
  UpdateAccountRequestDto,
  MessageAccountProviderDto,
  MessageProviderDto,
  CreateAccountProviderRequestDto,
  AccountProviderRechargeRequestDto,
  MessageAccountRechargeDto,
  MessageAccountRechargeQueryCriteria
} from '@/dtos/messaging-mgt.dto'
import {getMessageTypeLabel} from '@/enums/messaging'
import {formatDateTime, formatDateTimeOffsetToLocalDateTime} from '@/utils/date'

// 搜索表单项配置
const searchFormItems = computed(() => [
  {
    type: "input",
    label: "账户编码",
    prop: "accountCode",
    placeholder: "请输入账户编码",
  },
  {
    type: "input",
    label: "账户名称",
    prop: "accountName",
    placeholder: "请输入账户名称",
  },
  {
    type: "input",
    label: "应用编码",
    prop: "appCode",
    placeholder: "请输入应用编码",
  },
  {
    type: "select",
    label: "状态",
    prop: "isEnabled",
    placeholder: "请选择状态",
    options: [
      {label: "启用", value: true},
      {label: "禁用", value: false},
    ],
  },
] as FormItem[])

// 表格列配置
const tableColumns = computed(() => [
  {prop: "accountCode", label: "账户编码", width: 150},
  {prop: "accountName", label: "账户名称", width: 150},
  {prop: "appCode", label: "应用编码", width: 120},
  {prop: "isEnabled", label: "状态", slot: "status", width: 100},
  {prop: "createdTime", label: "创建时间", slot: "createdTime", width: 180},
  {prop: "remark", label: "备注"},
] as TableColumn[])

// 响应式数据
const loading = ref(false)
const total = ref(0)
const tableData = ref<MessageAccountDto[]>([])
const dialogVisible = ref(false)
const providerDialogVisible = ref(false)
const addProviderDialogVisible = ref(false)
const rechargeDialogVisible = ref(false)
const rechargeHistoryDialogVisible = ref(false)
const submitLoading = ref(false)
const addProviderLoading = ref(false)
const rechargeLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref<MessageAccountDto>()
const currentAccount = ref<MessageAccountDto>()
const currentProvider = ref<MessageAccountProviderDto>()
const accountProviders = ref<MessageAccountProviderDto[]>([])
const rechargeHistory = ref<MessageAccountRechargeDto[]>([])
const rechargeHistoryLoading = ref(false)
const rechargeHistoryTotal = ref(0)
const availableProviders = ref<MessageProviderDto[]>([])

// 表单引用
const formRef = ref<FormInstance>()
const providerFormRef = ref<FormInstance>()
const rechargeFormRef = ref<FormInstance>()

// 查询参数
const queryParams = reactive<MessageAccountQueryCriteria>({
  $pageIndex: 1,
  $pageSize: 20,
  accountCode: undefined,
  accountName: undefined,
  appCode: undefined,
  isEnabled: undefined,
})

// 表单数据
const form = reactive<CreateAccountRequestDto>({
  accountCode: '',
  accountName: '',
  appCode: '',
  isEnabled: true,
  remark: ''
})

// 添加服务商表单
const providerForm = reactive({
  providers: [] as Array<{
    providerCode: string
    providerType: string
    priority: number
  }>
})

// 充值表单
const rechargeForm = reactive({
  accountCode: '',
  providerCode: '',
  amount: 0,
  remark: ''
})

// 充值记录查询参数
const rechargeHistoryQueryParams = reactive<MessageAccountRechargeQueryCriteria>({
  $pageIndex: 1,
  $pageSize: 10,
  accountProviderId: undefined,
  accountCode: undefined,
  providerCode: undefined
})

// 表单验证规则
const formRules: FormRules = {
  accountCode: [{required: true, message: '请输入账户编码', trigger: 'blur'}],
  accountName: [{required: true, message: '请输入账户名称', trigger: 'blur'}],
  appCode: [{required: true, message: '请输入应用编码', trigger: 'blur'}]
}

const providerFormRules: FormRules = {
  providers: [{required: true, message: '请至少添加一个服务商', trigger: 'change'}]
}

const rechargeFormRules: FormRules = {
  amount: [{required: true, message: '请输入充值金额', trigger: 'blur'}]
}

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑账户' : '新增账户'
})

const rechargeHistoryDialogTitle = computed(() => {
  if (currentProvider.value && currentAccount.value) {
    return `充值记录 - ${currentAccount.value.accountName} (${currentProvider.value.providerCode})`
  }
  return '充值记录'
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const {data} = await messageAccountApi.getPage(queryParams)
    tableData.value = data.rows
    total.value = data.totals
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData)
  queryParams.$pageIndex = 1
  getList()
}

// 重置处理
const handleReset = (formData: any) => {
  Object.assign(queryParams, formData)
  getList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val
  getList()
}

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val
  getList()
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    accountCode: '',
    accountName: '',
    appCode: '',
    isEnabled: true,
    remark: ''
  })
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: MessageAccountDto) => {
  isEdit.value = true
  currentRecord.value = row
  Object.assign(form, {
    accountCode: row.accountCode,
    accountName: row.accountName,
    appCode: row.appCode,
    isEnabled: row.isEnabled,
    remark: row.remark || ''
  })
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: MessageAccountDto) => {
  try {
    await ElMessageBox.confirm('确认要删除该账户吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await messageAccountApi.delete(row.key)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    // 表单验证失败，直接返回
    return
  }

  submitLoading.value = true
  try {
    if (isEdit.value && currentRecord.value) {
      await messageAccountApi.update(currentRecord.value.key, form as UpdateAccountRequestDto)
      ElMessage.success('修改成功')
    } else {
      await messageAccountApi.create(form)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 管理服务商
const handleManageProviders = async (row: MessageAccountDto) => {
  currentAccount.value = row
  try {
    const {data} = await messageAccountProviderApi.getByAccount(row.accountCode)
    accountProviders.value = data
    providerDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取服务商列表失败')
  }
}


// 获取可用服务商列表
const getAvailableProviders = async () => {
  try {
    const {data} = await messageProviderApi.getAll()
    availableProviders.value = data.filter(p => p.isEnabled)
  } catch (error) {
    ElMessage.error('获取服务商列表失败')
  }
}

// 添加服务商
const handleAddProvider = async () => {
  await getAvailableProviders()
  // 重置表单
  providerForm.providers = []
  // 默认添加一行
  addProviderRow()
  addProviderDialogVisible.value = true
}

// 添加服务商行
const addProviderRow = () => {
  providerForm.providers.push({
    providerCode: '',
    providerType: '',
    priority: providerForm.providers.length + 1,
  })
}

// 删除服务商行
const removeProviderRow = (index: number) => {
  providerForm.providers.splice(index, 1)
  // 重新调整优先级
  providerForm.providers.forEach((item, idx) => {
    item.priority = idx + 1
  })
}

// 获取可用服务商（排除已选择的）
const getAvailableProvidersForRow = (index: number) => {
  const selectedCodes = providerForm.providers
      .filter((_, idx) => idx !== index)
      .map(p => p.providerCode)
  return availableProviders.value.filter(p => !selectedCodes.includes(p.providerCode))
}

// 单个服务商选择变化
const handleSingleProviderSelect = (index: number) => {
  const row = providerForm.providers[index]
  const provider = availableProviders.value.find(p => p.providerCode === row.providerCode)
  if (provider) {
    row.providerType = provider.providerType
  }
}

// 确认添加服务商
const handleConfirmAddProvider = async () => {
  if (!providerFormRef.value) return

  // 验证表单数据
  if (providerForm.providers.length === 0) {
    ElMessage.error('请至少添加一个服务商')
    return
  }

  // 验证每行数据
  for (const provider of providerForm.providers) {
    if (!provider.providerCode) {
      ElMessage.error('请选择服务商')
      return
    }
    if (!provider.priority || provider.priority < 1) {
      ElMessage.error('请设置正确的优先级')
      return
    }
  }

  addProviderLoading.value = true
  try {
    const requests: CreateAccountProviderRequestDto[] = providerForm.providers.map(provider => ({
      accountCode: currentAccount.value!.accountCode,
      providerCode: provider.providerCode,
      providerType: provider.providerType,
      priority: provider.priority,
      quotaTotal: 0, // 初始额度为0
      isEnabled: true
    }))

    // 批量创建账户服务商关联
    await Promise.all(requests.map(req => messageAccountProviderApi.create(req)))

    ElMessage.success('添加成功')
    addProviderDialogVisible.value = false
    handleManageProviders(currentAccount.value!)
  } catch (error) {
    ElMessage.error('添加失败')
  } finally {
    addProviderLoading.value = false
  }
}

// 编辑服务商
const handleEditProvider = (row: MessageAccountProviderDto) => {
  ElMessage.info('编辑服务商功能开发中...')
}

// 充值
const handleRecharge = (row: MessageAccountProviderDto) => {
  Object.assign(rechargeForm, {
    accountCode: row.accountCode,
    providerCode: row.providerCode,
    amount: 0,
    remark: ''
  })
  rechargeDialogVisible.value = true
}

// 确认充值
const handleConfirmRecharge = async () => {
  if (!rechargeFormRef.value) return

  try {
    await rechargeFormRef.value.validate()
  } catch (error) {
    // 表单验证失败，直接返回
    return
  }

  rechargeLoading.value = true
  try {
    const request: AccountProviderRechargeRequestDto = {
      accountCode: rechargeForm.accountCode,
      providerCode: rechargeForm.providerCode,
      amount: rechargeForm.amount,
      remark: rechargeForm.remark
    }

    await messageAccountProviderApi.recharge(request)
    ElMessage.success('充值成功')
    rechargeDialogVisible.value = false
    handleManageProviders(currentAccount.value!)
  } catch (error) {
    ElMessage.error('充值失败')
  } finally {
    rechargeLoading.value = false
  }
}

// 移除服务商
const handleRemoveProvider = async (row: MessageAccountProviderDto) => {
  try {
    await ElMessageBox.confirm('确认要移除该服务商吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await messageAccountProviderApi.delete(row.key)
    ElMessage.success('移除成功')
    handleManageProviders(currentAccount.value!)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除失败')
    }
  }
}

// 服务商状态变更
const handleProviderStatusChange = async (row: MessageAccountProviderDto) => {
  try {
    await messageAccountProviderApi.setEnabled(row.key, row.isEnabled)
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.isEnabled = !row.isEnabled
  }
}


// 获取充值记录列表
const getRechargeHistoryList = async () => {
  rechargeHistoryLoading.value = true
  try {
    const {data} = await messageAccountRechargeApi.getPage(rechargeHistoryQueryParams)
    rechargeHistory.value = data.rows
    rechargeHistoryTotal.value = data.totals
  } catch (error) {
    ElMessage.error('获取充值记录失败')
  } finally {
    rechargeHistoryLoading.value = false
  }
}

// 查看充值记录
const handleViewRechargeHistory = async (row: MessageAccountProviderDto) => {
  currentProvider.value = row

  // 设置查询参数
  rechargeHistoryQueryParams.accountCode = row.accountCode
  rechargeHistoryQueryParams.providerCode = row.providerCode
  rechargeHistoryQueryParams.$pageIndex = 1
  rechargeHistoryQueryParams.accountProviderId = row.key

  // 显示对话框并获取数据
  rechargeHistoryDialogVisible.value = true
  await getRechargeHistoryList()
}

// 充值记录分页处理
const handleRechargeHistorySizeChange = (val: number) => {
  rechargeHistoryQueryParams.$pageSize = val
  getRechargeHistoryList()
}

const handleRechargeHistoryCurrentChange = (val: number) => {
  rechargeHistoryQueryParams.$pageIndex = val
  getRechargeHistoryList()
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
