import {defineStore} from 'pinia'
import {login, logout, getCaptcha, getUserInfo, refreshToken as refreshTokenApi, getUserPositions} from '@/api/rbac'
import {
    getToken,
    setToken,
    getRefreshToken,
    setRefreshToken,
    getMenus,
    setMenus,
    getPermissions,
    setPermissions,
    setPositions,
    setRoles,
    getPositions,
    getRoles,
    clearAuthData
} from '@/utils/auth'
import {getDeviceId, getDeviceInfoString} from '@/utils/device'
import {LoginParams, RefreshTokenParams} from '@/dtos'
import {get} from 'lodash-es'

interface UserState {
    token: string
    refreshToken: string
    name: string
    username: string
    avatar: string
    roles: string[]
    permissions: string[] // 按钮权限
    menus: string[] // 菜单和目录权限
    positions: string[] // 用户岗位
    userId: string | null
    captcha: {
        id: string
        image: string
        expiresIn: number
    } | null
    gender: string
    country: string
    unit: string
    contactAddress: string
    telephone: string
}

export const useUserStore = defineStore('user', {
    state: (): UserState => ({
        token: getToken() || '',
        refreshToken: getRefreshToken() || '',
        name: '',
        username: '',
        avatar: '',
        roles: getRoles() || [],
        permissions: getPermissions() || [], // 按钮权限
        menus: getMenus() || [], // 菜单和目录权限
        positions: getPositions() || [], // 用户岗位
        userId: null,
        captcha: null,
        gender: '',
        country: '',
        unit: '',
        contactAddress: '',
        telephone: '',
    }),

    actions: {
        async getCaptcha() {
            try {
                const {data} = await getCaptcha()
                // 添加Base64图片前缀
                const imageWithPrefix = `data:image/png;base64,${data.image}`
                this.captcha = {
                    id: data.id,
                    image: imageWithPrefix,
                    expiresIn: data.expiresIn
                }
                return this.captcha
            } catch (error) {
                throw error
            }
        },

        async login(loginData: LoginParams) {
            try {
                console.log('开始登录:', loginData.username)
                const {data} = await login(loginData)
                console.log('登录响应:', data)

                // 如果没有accessToken，说明登录失败
                if (!data.accessToken) {
                    console.log('登录失败，没有accessToken')
                    // 登录失败，需要重新获取验证码
                    await this.getCaptcha()
                    throw new Error('登录失败，请检查用户名和密码')
                }

                console.log('登录成功，设置token、角色和权限')
                this.token = data.accessToken
                this.refreshToken = data.refreshToken || ''
                this.roles = data.roles || []
                this.positions = data.positions || []


                this.menus = data.menuPermissions;
                this.permissions = data.buttonPermissions;

                setMenus(this.menus)
                setPermissions(this.permissions)
                setPositions(this.positions)
                setRoles(this.roles);


                // 确保角色不为空
                // if (!this.roles || this.roles.length === 0) {
                //   console.log('角色为空，设置默认角色: admin')
                //   this.roles = ['admin']
                // }

                console.log('保存token到localStorage')
                setToken(this.token)
                setRefreshToken(this.refreshToken)

                console.log('登录成功，获取用户信息')
                // 登录成功后获取用户信息
                await this.fetchUserInfo()
                // 登录成功后获取用户职位信息
                // await this.fetchUserPositions()

                // 登录成功后清空验证码
                this.captcha = null

                console.log('登录流程完成')
                return data
            } catch (error) {
                console.error('登录失败:', error)
                // 登录失败，获取新验证码
                await this.getCaptcha()
                throw error
            }
        },
        // async fetchUserPositions() {
        //     try {
        //         console.log('获取用户职位信息开始')
        //         const {data} = await getUserPositions()
        //         this.positions = data.map(item => item.code) || []
        //         setPositions(data.map(item => item.code))
        //         return data
        //     } catch (error) {
        //         console.error('获取用户职位信息失败:', error)
        //         throw error
        //     }
        // },
        async fetchUserInfo() {
            try {
                console.log('获取用户信息开始')
                const {data} = await getUserInfo()
                console.log('获取用户信息成功:', data)

                // 更新用户信息
                this.userId = data.key
                this.name = data.realName || ''
                this.username = data.username || ''
                this.avatar = data.avatar || ''
                this.gender = data.gender || ''
                this.country = data.country || ''
                this.unit = data.unit || ''
                this.contactAddress = data.contactAddress || ''
                this.telephone = data.telephone || ''

                // 如果roles为空，设置一个默认角色
                // if (!this.roles || this.roles.length === 0) {
                //   console.log('设置默认角色: admin')
                //   this.roles = ['admin']
                // }

                // 获取用户权限
                await this.fetchPermissions()

                console.log('用户信息更新完成:', {
                    userId: this.userId,
                    name: this.name,
                    avatar: this.avatar,
                    roles: this.roles,
                    permissions: this.permissions.length,
                    gender: this.gender,
                    country: this.country,
                    unit: this.unit,
                    contactAddress: this.contactAddress,
                    telephone: this.telephone,
                })

                return data
            } catch (error) {
                console.error('获取用户信息失败:', error)
                throw error
            }
        },

        async fetchPermissions() {
            return {
                permissions: this.permissions,
                menus: this.menus
            }
        },
        async fetchPositions() {
            return {
                positions: this.positions
            }
        },
        async clearLoginState() {
            this.token = ''
            this.refreshToken = ''
            this.roles = []
            this.permissions = []
            this.positions = []
            this.menus = []
            this.userId = null
            this.name = ''
            this.avatar = ''
            this.captcha = null
            // 清除所有认证数据
            clearAuthData()
        },
        async logout() {
            try {
                await logout()
            } finally {
                await this.clearLoginState()
            }
        },

        async refreshUserToken() {
            console.log('开始刷新令牌')

            try {
                // 构建刷新令牌参数
                const refreshParams: RefreshTokenParams = {
                    refreshToken: this.refreshToken,
                    deviceInfo: getDeviceInfoString(),
                    deviceId: getDeviceId()
                }

                console.log('刷新令牌参数:', {
                    deviceId: refreshParams.deviceId,
                    hasRefreshToken: !!this.refreshToken
                })

                const {data} = await refreshTokenApi(refreshParams)
                console.log('刷新令牌成功:', {
                    hasAccessToken: !!data.accessToken,
                    hasRefreshToken: !!data.refreshToken
                })

                // 更新token
                this.token = data.accessToken || ''
                this.refreshToken = data.refreshToken || ''

                setToken(this.token)
                setRefreshToken(this.refreshToken)

                return data
            } catch (error: any) {
                // 刷新令牌失败，清空token
                console.error('刷新令牌失败:', error)
                this.resetToken()

                // // 检查是否是401错误
                // if (error.response && error.response.status === 401) {
                //   console.log('刷新令牌返回401，立即跳转到登录页')
                //
                //   // 使用replace强制刷新页面
                //   window.location.replace('/login')
                //
                //   // 添加一个延迟执行的备份跳转
                //   setTimeout(() => {
                //     console.log('执行备份跳转')
                //     window.location.href = '/login'
                //   }, 100)
                // }

                // 抛出错误，让调用者也能处理
                throw error
            }
        },

        resetToken() {
            this.token = ''
            this.refreshToken = ''
            this.roles = []
            this.permissions = []
            this.menus = []
            this.userId = null
            this.name = ''
            this.avatar = ''
            this.captcha = null
            // 清除所有认证数据
            clearAuthData()
        }
    }
})