import request from '@/utils/request'
import {
    PageResult
} from '@/dtos'
import { getApiBaseUrl } from '@/config/env'
import { ApplicationLogDto, ApplicationLogQueryParams } from "@/dtos/logging.dto";

const baseURL = getApiBaseUrl('logging-mgt')

/**
 * 获取日志分页列表
 * @param params 查询参数
 */
export function getApplicationLogPage(params: ApplicationLogQueryParams) {
    return request<PageResult<ApplicationLogDto>>({
        url: `${baseURL}/ApplicationLog/page`,
        method: 'get',
        params,
        timeout: 100000
    })
}

