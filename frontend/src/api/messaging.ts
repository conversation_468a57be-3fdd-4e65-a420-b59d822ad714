import request from '@/utils/request'
import {
    MessageSendDto,
    SendMessageRequestDto
} from "@/dtos/messaging-mgt.dto";

import {getApiBaseUrl} from '@/config/env'

const BASE_URL = getApiBaseUrl('messaging')

/**
 * 消息发送API（业务接口）
 */
export const messageSendBusinessApi = {
    /**
     * 发送消息
     */
    send(data: SendMessageRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageSend/send`, data)
    },

    /**
     * 查询发送状态
     */
    getStatus(sendId: number) {
        return request.get<MessageSendDto>(`${BASE_URL}/MessageSend/status/${sendId}`)
    }
}