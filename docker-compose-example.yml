version: "3.7"
name: xj-framework-example

networks:
  xjframework-example-network:
    driver: bridge

services:
  example-webapi:
    image: xj-framework/example-webapi:latest
    build:
      context: backend
      dockerfile: src/domains/example/XJ.Framework.Example.WebApi/Dockerfile${STAGE}
    ports:
      - "8900:8080"
      - "9990:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 500M
        reservations:
          cpus: "0.1"
          memory: 300M

  example-webapi-mgt:
    image: xj-framework/example-webapi-mgt:latest
    build:
      context: backend
      dockerfile: src/domains/example/XJ.Framework.Example.WebApi.Mgt/Dockerfile${STAGE}
    ports:
      - "8901:8080"
      - "9991:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"